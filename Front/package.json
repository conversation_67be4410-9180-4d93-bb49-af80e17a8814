{"name": "datatist-wolf-front", "version": "0.1.0", "private": true, "homepage": "/aimarketer/", "scripts": {"prepare-env": "node scripts/init-env.js", "start": "pnpm prepare-env && rsbuild dev", "build": "pnpm prepare-env && rsbuild build", "preview": "rsbuild preview", "eslint": "eslint --ext .js,.jsx src", "eslint:fix": "eslint --ext .js,.jsx,.ts,.tsx src --fix", "clean-cache": "rm -rf node_modules/.cache/hard-source", "build-tsc": "rimraf dist && tsc -P ./tsconfig.json || true", "format": "npx prettier --write src/** & npm run eslint:fix", "getRandomString": "node scripts/getRandomString.js"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "IE 11"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version", "not ie <= 8"]}, "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/compatible": "^1.0.8", "@ant-design/icons": "^4.8.1", "@antv/g2": "^4.2.11", "@antv/g6": "^4.8.24", "@antv/l7": "2.9.27", "@antv/l7-district": "^2.3.12", "@antv/l7-maps": "2.9.27", "@antv/layout": "^0.3.25", "@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-stencil": "^2.1.5", "@antv/x6-plugin-transform": "^2.1.8", "@antv/x6-react-shape": "2.0.8", "@antv/xflow-extension": "1.0.52", "@babel/plugin-proposal-decorators": "^7.25.9", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@umijs/hooks": "^1.9.3", "ahooks": "^3.8.4", "antd": "4.24.9", "axios": "^0.19.2", "buffer": "^6.0.3", "classnames": "^2.5.1", "codemirror": "^5.65.18", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.13", "emoji-mart": "^5.6.0", "html2canvas": "^1.4.1", "i18next": "^23.16.8", "immutability-helper": "^3.1.1", "jsencrypt": "^3.3.2", "json-beautify": "^1.1.1", "lodash": "^4.17.21", "memory-cache": "^0.2.0", "process": "^0.11.10", "prop-types": "^15.8.1", "querystring": "^0.2.1", "rc-picker": "^4.9.2", "rc-resize-observer": "1.2.1", "react": "^16.14.0", "react-codemirror2": "^7.3.0", "react-color": "^2.19.3", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-dom": "^16.14.0", "react-fast-marquee": "^1.6.5", "react-grid-layout": "^1.5.0", "react-i18next": "^15.4.0", "react-loadable": "npm:@docusaurus/react-loadable", "react-redux": "^7.2.9", "react-resizable": "^1.11.1", "react-router-dom": "^5.3.4", "react-slick": "^0.28.1", "react-visibility-sensor": "^5.1.1", "react-window": "^1.8.11", "redux": "^4.2.1", "redux-thunk": "^2.4.2", "slick-carousel": "^1.8.1", "sortablejs": "^1.15.6", "uuid": "^8.3.2", "wolf-static-cpnt": "1.5.2", "zustand": "^4.5.6"}, "devDependencies": {"stream-browserify": "^3.0.0", "@rsbuild/core": "^1.2.19", "@rsbuild/plugin-react": "^1.1.0", "@rsbuild/plugin-sass": "^1.2.1", "@rspack/core": "^1.2.8", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.15", "@types/node": "^20.17.17", "@types/react": "^16.14.62", "@types/react-dom": "^18.3.5", "@types/react-redux": "^7.1.34", "@types/react-router-dom": "^5.3.3", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "ant-path-matcher": "^0.0.5", "antd-dayjs-webpack-plugin": "^1.0.6", "babel-eslint": "10.0.3", "eslint": "6.6.0", "eslint-config-airbnb": "18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "6.2.3", "eslint-plugin-react": "7.14.3", "eslint-plugin-react-hooks": "^3.0.0", "eslint-plugin-unused-imports": "^3.2.0", "husky": "^4.3.8", "lint-staged": "^10.5.4", "node-polyfill-webpack-plugin": "^4.1.0", "postcss": "^8.5.1", "postcss-loader": "^8.1.1", "prettier": "^3.4.2", "rsbuild-cli": "^0.0.0", "sass": "^1.84.0", "sass-loader": "^16.0.4", "sass-resources-loader": "^2.2.5", "tailwindcss": "^3.4.17", "typescript": "5.0.4"}, "lint-staged": {"*.{js,jsx}": ["eslint --fix", "git add"]}, "resolutions": {"@types/react": "^16.14.0"}, "pnpm": {"overrides": {"path-to-regexp": "1.9.0"}}}