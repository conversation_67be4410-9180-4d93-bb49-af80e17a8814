import { Tabs } from 'antd';
import React, { useState } from 'react';
import { t } from '@/utils/translation';
import MyToDoListDone from './done';
import './index.scss';
import MyToDoListRunning from './running';
import MyToDoListStart from './start';

export default function MyToDoList(props) {
  const {
    location: { state }
  } = props;

  const [tabKey, setTabKey] = useState(state?.tabKey || '1');

  const items = [
    {
      label: t('setting-KJ6LYdxOPYCd'),
      key: '1',
      children: <MyToDoListStart props={props} tabKey={tabKey} />
    },
    {
      label: t('setting-DHAFODLBzEhD'),
      key: '2',
      children: <MyToDoListRunning props={props} tabKey={tabKey} />
    },
    {
      label: t('setting-PUFFyp4mUujm'),
      key: '3',
      children: <MyToDoListDone props={props} tabKey={tabKey} />
    }
  ];

  const onTabChange = (key) => {
    setTabKey(key);
  };

  return (
    <div className="todoList">
      <header>
        <h1>{t('setting-ABFfFJg33IRB')}</h1>
      </header>
      <Tabs activeKey={tabKey} onChange={onTabChange} items={items} />
    </div>
  );
}
