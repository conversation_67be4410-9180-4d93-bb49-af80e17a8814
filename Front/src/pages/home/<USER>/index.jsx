import { Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { t } from '@/utils/translation';
import UserService from 'service/UserService';
import MyToDoListService from 'service/myToDoListService';
import MyToDoListDone from './done';
import './index.scss';
import MyToDoListRunning from './running';
import MyToDoListStart from './start';

const userService = new UserService();

const mapState = (store) => {
  return { systemInfo: store.systemInfo, messageInfo: store.messageInfo };
};

const MyToDoList = (props) => {
  const {
    location: { state }
  } = props;

  const [tabKey, setTabKey] = useState(state?.tabKey || '1');
  const [dictTypeList, setDictTypeList] = useState([]);
  const [userList, setUserList] = useState([]);

  useEffect(() => {
    const getDictTypeList = async () => {
      try {
        const res = await MyToDoListService.getDictType([
          {
            operator: 'EQ',
            propertyName: 'businessType',
            value: 'APPROVAL'
          },
          {
            operator: 'EQ',
            propertyName: 'code',
            value: 'sys_approval_scope'
          }
        ]);

        setDictTypeList(res);

        const redata = await userService.listBy([]);
        setUserList(redata);
      } catch (err) {
        console.error(err);
      }
    };

    getDictTypeList();
  }, []);

  const items = [
    {
      label: t('setting-rx1fCnrDTOp2'),
      key: '1',
      children: <MyToDoListStart props={props} tabKey={tabKey} dictTypeList={dictTypeList} userList={userList} />
    },
    {
      label: t('setting-IRifB8LgYFGp'),
      key: '2',
      children: <MyToDoListRunning props={props} tabKey={tabKey} dictTypeList={dictTypeList} userList={userList} />
    },
    {
      label: t('setting-x4Qz1PLcwbH8'),
      key: '3',
      children: <MyToDoListDone props={props} tabKey={tabKey} dictTypeList={dictTypeList} userList={userList} />
    }
  ];

  const onTabChange = (key) => {
    setTabKey(key);
  };

  return (
    <div className="todoList">
      <header>
        <h1>{t('setting-WuugSSLjga0h')}</h1>
      </header>
      <Tabs activeKey={tabKey} onChange={onTabChange} items={items} />
    </div>
  );
};

export default connect(mapState)(MyToDoList);
