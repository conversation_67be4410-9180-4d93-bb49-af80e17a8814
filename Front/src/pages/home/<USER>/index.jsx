import { useStore } from '@/store/share';
import { t } from 'utils/translation';

import {
  AppstoreOutlined,
  DownOutlined,
  EllipsisOutlined,
  SearchOutlined,
  UnorderedListOutlined
} from '@ant-design/icons';
import { useRequest } from '@umijs/hooks';
import {
  Button,
  Dropdown,
  Empty,
  Input,
  Modal,
  Pagination,
  Popover,
  Select,
  Spin,
  Table,
  Tooltip,
  message
} from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import Log from 'utils/log';

import ColumnActionCom from 'components/featurecoms/tableactioncom/index';
import CampaignsService from 'service/CampaignsService';
import UserService from 'service/UserService';
import RoleService from 'service/roleService';

import data from '@emoji-mart/data';
import logo from 'assets/images/<EMAIL>';
import { MyIcon } from 'utils/myIcon';
import { numConvert, setNativeValue, transformUrl } from 'utils/universal';

import { connect } from 'react-redux';
import getMenuTitle from 'utils/menuTitle';
import AddCampains from './addCampains';
import QueryForList from './queryforlist/index';

// import Edit from '../edit/index';
import CheckAuth from '@/utils/checkAuth';
import config from './config';
import './index.scss';

const { Option } = Select;

const userService = new UserService();
const campaignsService = new CampaignsService();
const roleService = new RoleService();

const log = Log.getLogger('UserManage');

const stateToProps = (state) => {
  return {
    meunData: state.meunData.menus.main,
    userDepartment: state.userDepartment
  };
};

const featureData = {
  detail: {
    text: t('dataCenter-90Y7Jp6WSHIM')
  },
  Divider: { key: 'Divider' },
  dropdownData: {
    edit: {
      text: t('dataCenter-Mxh6ORwjfxy8'),
      code: 'aim_campaigns_edit'
    },
    collect: {
      text: t('dataCenter-cfTOoA5HtfRd')
    },
    cancelCollect: {
      text: t('dataCenter-xLvnNxjXKFdm')
    },
    share: {
      text: t('dataCenter-Us97mkjH08Rv'),
      code: 'analyzer_campaigns_share_view'
    },
    cooperateManage: {
      text: t('dataCenter-GoO16YlUWypI'),
      code: 'analyzer_campaigns_share_view'
    },
    delete: {
      text: t('dataCenter-xmbk4yliJtB9'),
      code: 'aim_campaigns_delete'
    },
    stop: {
      text: t('dataCenter-RKFpao6EtOou'),
      code: 'aim_campaigns_stop'
    }
  }
};

const featureDataStop = {
  detail: {
    text: t('dataCenter-90Y7Jp6WSHIM')
  },
  Divider: { key: 'Divider' },
  dropdownData: {
    collect: {
      text: t('dataCenter-cfTOoA5HtfRd')
    },
    cancelCollect: {
      text: t('dataCenter-xLvnNxjXKFdm')
    },
    share: {
      text: t('dataCenter-Us97mkjH08Rv'),
      code: 'analyzer_campaigns_share_view'
    },
    cooperateManage: {
      text: t('dataCenter-GoO16YlUWypI'),
      code: 'analyzer_campaigns_share_view'
    },
    delete: {
      text: t('dataCenter-xmbk4yliJtB9'),
      code: 'aim_campaigns_delete'
    }
  }
};

const Campaigns = (props) => {
  const {
    location: { state }
  } = props;
  const [visible, setVisible] = useState(false);
  // const [search, setSearch] = useState([]);
  const [search, setSearch] = useState(state && state.params ? state.params.search : []);
  const [editValue, setEditValue] = useState({});
  const [isShowEdit, setIsShowEdit] = useState({ status: false, idEdit: null });
  const [loading, setLoading] = useState(false);
  // const [elements, setElements] = useState(config.elements);
  const [elements, setElements] = useState(config.elements);
  const [userId, setUserId] = useState(null);
  const [dataShowType, setDataShowType] = useState(state && state.dataShowType ? state.dataShowType : 'card');
  const [campaignsData, setCampaignsData] = useState(undefined);
  const [pagination] = useState(
    state && state.pagination ? _.cloneDeep(state.pagination) : _.cloneDeep(config.initPagination)
  );
  const [searchSelectVal, setSearchSelectVal] = useState('name');
  const [activeKey, setActiveKey] = useState(state && state.activeKey ? _.cloneDeep(state.activeKey) : 'ALL');
  const [sorterInfo, setSorterInfo] = useState({
    columnKey: null,
    order: null
  });
  const [statusVal, setStatusVal] = useState(undefined);
  const [owerVal, setOwerVal] = useState(undefined);
  const [recentSelectKey, setRecentSelectKey] = useState(null);
  const [filterVisible, setFilterVisible] = useState(false);
  const [defaultFormData, setDefaultFormData] = useState({});
  const [projectShareStatus, setProjectShareStatus] = useState(false);
  const inputRef = useRef(null);

  const { dispatchShare } = useStore();

  const uniqueAfterArr = (arr, name) => {
    const hash = {};
    return arr.reduce((acc, cru) => {
      if (!hash[cru[name]]) {
        hash[cru[name]] = { index: acc.length };
        acc.push(cru);
      } else {
        acc.splice(hash[cru[name]].index, 1, cru);
      }
      return acc;
    }, []);
  };

  const saveRecordInfo = async (id, record) => {
    const userInfo = await userService.getCurrentUser();
    await campaignsService.saveUserOperationRecord({
      targetId: id,
      id: record?.recentUserOperationRecord?.id,
      targetType: 'CAMPAIGNS',
      type: 'RECENT',
      createUserId: userInfo.id,
      updateUserId: userInfo.id,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
  };

  const saveCollect = async (val, userId) => {
    const { id } = val;
    try {
      await campaignsService.saveUserOperationRecord({
        targetId: id,
        targetType: 'CAMPAIGNS',
        type: 'FAVORITE',
        createUserId: userId,
        updateUserId: userId,
        createTime: dayjs().valueOf(),
        updateTime: dayjs().valueOf()
      });
      setSearch([...searchRef.current]);
      message.success(t('dataCenter-LFxqyM8cSkTl'));
    } catch (error) {
      console.error(error);
    }
  };

  const delCollect = async (val, userId) => {
    const { id } = val;
    try {
      await campaignsService.delByCondition({
        targetId: id,
        targetType: 'CAMPAIGNS',
        type: 'FAVORITE',
        loginId: userId
      });

      await campaignsService.saveUserOperationRecord({
        targetId: id,
        id: val?.recentUserOperationRecord?.id,
        targetType: 'CAMPAIGNS',
        type: 'RECENT',
        createUserId: userId,
        updateUserId: userId,
        createTime: dayjs().valueOf(),
        updateTime: dayjs().valueOf()
      });

      setSearch([...searchRef.current]);
      message.success(t('dataCenter-B6lvXXsxVR4F'));
    } catch (error) {
      console.error(error);
    }
  };

  async function getTableList(params, key = 'RECENT') {
    const userId = localStorage.getItem('userId') ? parseInt(localStorage.getItem('userId')) : null;
    setLoading(true);
    params.search.forEach((item) => {
      if (item.propertyName === 'orderNo' && item.value !== '') {
        // item.value.forEach(val => {
        //   if (_.isNaN(Number(val)) || val.indexOf(' ') > -1) {
        //     message.error(t('dataCenter-9utHRHykHJ80'));
        //     throw new Error(t('dataCenter-9utHRHykHJ80'));
        //   }
        // });
        // item.value = item.value.toString();
        if (_.isNaN(Number(item.value)) || item.value.indexOf(' ') > -1) {
          message.error(t('dataCenter-9utHRHykHJ80'));
          throw new Error(t('dataCenter-9utHRHykHJ80'));
        }
      }
    });

    const _params = _.cloneDeep(params);
    let tableParams;

    if (key === 'RECENT' || key === 'FAVORITE') {
      if (key === 'RECENT') {
        tableParams = {
          ..._params,
          search: uniqueAfterArr(
            [
              ..._params.search,
              {
                propertyName: 'recordType',
                operator: 'IN',
                value: 'RECENT,FAVORITE'
              }
            ],
            'propertyName'
          )
        };
      } else {
        tableParams = {
          ..._params,
          search: uniqueAfterArr(
            [
              ..._params.search,
              {
                propertyName: 'recordType',
                operator: 'EQ',
                value: 'FAVORITE'
              }
            ],
            'propertyName'
          )
        };
      }
    } else if (key === 'MINE') {
      _params.search = uniqueAfterArr(
        [..._params.search, { operator: 'EQ', propertyName: 'createUserId', value: userId }],
        'propertyName'
      );
      const index = _.findIndex(_params.search, (v) => v.propertyName === 'operationTime');
      if (index !== -1) {
        _params.search.splice(index, 1);
      }
      tableParams = _params;
    } else {
      _params.search = uniqueAfterArr([..._params.search], 'propertyName');
      const index = _.findIndex(_params.search, (v) => v.propertyName === 'operationTime');
      if (index !== -1) {
        _params.search.splice(index, 1);
      }
      tableParams = _params;
    }

    // const data = await campaignsService.query(params);

    const data =
      key === 'ALL' || key === 'MINE'
        ? await campaignsService.query(tableParams, true)
        : await campaignsService.findSegmentNewQuery(tableParams, true);
    pagination.total = data.totalElements;
    pagination.pageSize = tableParams.size;
    pagination.current = tableParams.page;
    pagination.sorts = tableParams.sorts;
    setCampaignsData(data);
    // setCache({ params: tableParams, pagination, dataShowType, activeKey: key })
    props.history.replace({
      state: { params: tableParams, pagination, dataShowType, activeKey: key }
    });
    setLoading(false);
    return {
      total: data.totalElements,
      list: data.content
    };
  }

  // async function getTableList(params, activeKey) {
  //   setLoading(true)
  //   params.search.forEach(item => {
  //     if (item.propertyName === 'orderNo' && item.value !== '') {
  //       item.value.forEach(val => {
  //         if (_.isNaN(Number(val)) || val.indexOf(' ') > -1) {
  //           message.error('id只能输入纯数字');
  //           throw new Error('id只能输入纯数字');
  //         }
  //       });
  //       item.value = item.value.toString();
  //     }
  //   });

  //   const data = await campaignsService.query(params);
  //   pagination.total = data.totalElements;
  //   pagination.pageSize = params.size;
  //   pagination.current = params.page;
  //   pagination.sorts = params.sorts;
  //   setCampaignsData(data);
  //   props.history.replace({ state: { params, pagination, dataShowType } });
  //   setLoading(false)
  //   return {
  //     total: data.totalElements,
  //     list: data.content
  //   };
  // }

  // useEffect(() => {
  //   props.history.replace({state: cache});
  // },[cache]);

  const onView = async (id, record) => {
    saveRecordInfo(id, record);
    // setCache({...params})
    props.history.push(`/aimarketer/home/<USER>/detail/${id}`);
  };

  const columnsList = [
    {
      title: 'ID',
      dataIndex: 'orderNo',
      width: 100,
      sorter: true,
      fixed: 'left'
    },
    {
      title: t('dataCenter-6FoXyfGEDqoH'),
      dataIndex: 'name',
      ellipsis: true,
      render: (text, record) => (
        <Tooltip title={text}>
          <a onClick={() => onView(record.id, record)}>{text}</a>
        </Tooltip>
      ),
      width: 320,
      height: 100,
      fixed: 'left'
    },
    {
      title: t('dataCenter-VOKP4acEnLPI'),
      dataIndex: 'ownerName',
      width: 120,
      render: (text, record) => <div>{record?.owner?.name}</div>
    },
    {
      title: t('dataCenter-M81OG6BvRbUi'),
      dataIndex: 'startTime',
      width: 150,
      sorter: true,
      // defaultSortOrder: 'descend',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD')
    },
    {
      title: t('dataCenter-xKjzTfv9o44p'),
      dataIndex: 'endTime',
      sorter: true,
      width: 150,
      // defaultSortOrder: 'descend',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD')
    },
    {
      title: t('dataCenter-wWucwAIyZLHW'),
      dataIndex: 'status',
      width: 100,
      render: (text) => {
        return (
          <div className="status">
            <span
              className="circle"
              style={{
                backgroundColor: _.filter(config.status, (v) => v.value === text)[0]?.color
              }}
            />
            <span
              style={{
                color: _.filter(config.status, (v) => v.value === text)[0]?.color
              }}
            >
              {text ? _.filter(config.status, (v) => v.value === text)[0]?.name : '-'}
            </span>
          </div>
        );
      }
    },
    {
      title: t('dataCenter-cN8TrA3MQqVJ'),
      dataIndex: 'createUserName',
      width: 150
    },
    {
      title: t('dataCenter-1TCxnKzxrhtJ'),
      dataIndex: 'createTime',
      width: 200,
      sorter: true,
      // defaultSortOrder: 'descend',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: t('dataCenter-kfEwnWA3RUwf'),
      dataIndex: 'updateUserName',
      width: 150
    },
    {
      title: t('dataCenter-b9Tvxf2Vr7qT'),
      dataIndex: 'updateTime',
      width: 200,
      sorter: true,
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    }
  ];

  const getRecentRange = (key) => {
    let startTime;
    const endTime = dayjs().endOf('day').valueOf();
    if (key === 'WEEK') {
      startTime = dayjs().subtract(1, 'week').startOf('day').valueOf();
    } else if (key === 'MON') {
      startTime = dayjs().subtract(1, 'months').startOf('day').valueOf();
    } else {
      startTime = dayjs().subtract(3, 'months').startOf('day').valueOf();
    }

    return `${startTime},${endTime}`;
  };

  useEffect(() => {
    init();
    log.debug('init', elements);
    return () => localStorage.removeItem('projectShareStatus');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const beforeunload = () => {
    props.history.replace({ state: null });
  };

  async function init() {
    const projectShareRes = await campaignsService.projectShareAuth({ projectId: localStorage.getItem('projectId') });
    const authShare = await userService.getActionGroupByRoleShare({
      id: Number(localStorage.getItem('roleId'))
    });

    if (projectShareRes && !_.isEmpty(authShare) && authShare.find((item) => item.code === 'STRATEGY').checked) {
      setProjectShareStatus(true);
      localStorage.setItem('projectShareStatus', 'ENABLE');
    } else {
      setProjectShareStatus(false);
      localStorage.setItem('projectShareStatus', 'DISABLE');
    }
    window.addEventListener('beforeunload', beforeunload);
    const redata = await userService.listBy([]);
    const { id } = await userService.getCurrentUser();
    const defaultFilter = {};
    if (state && state.params) {
      for (const item of state.params.search) {
        if (item.value !== '' && item.propertyName !== 'operationTime') {
          defaultFilter[`${item.propertyName}`] = item.value;
          if (item.operator === 'DATE_BETWEEN') {
            const dateArr = item.value.split(',');
            defaultFilter[`${item.propertyName}`] = dateArr.map((item) => dayjs(parseInt(item)));
          }

          if (item.propertyName === 'status') {
            setStatusVal(item.value);
          }

          if (item.propertyName === 'ownerId') {
            setOwerVal(item.value);
          }

          if (item.propertyName === 'name' || item.propertyName === 'orderNo') {
            setNativeValue([inputRef.current.input], item.value);
            setSearchSelectVal(item.propertyName);
            inputRef.current.input.value = item.value;
          }
        }
      }
    }
    setDefaultFormData(defaultFilter);
    localStorage.setItem('userId', id);
    setUserId(id);
    const roleList = await roleService.reqGetTableData({
      search: [],
      size: 50,
      page: 1,
      sort: [{ propertyName: 'createTime', direction: 'desc' }]
    });
    const _elements = _.cloneDeep(elements);
    _elements.createUserId.componentOptions.options = _.map(redata, (item) => ({
      key: item.id,
      value: item.id,
      text: item.name
    }));
    _elements.ownerId.componentOptions.options = _.map(roleList.content, (item) => ({
      key: item.user.id,
      value: item.user.id,
      text: item.user.name
    }));
    _elements.status.componentOptions.options = _.map(config.status, (item) => ({
      key: item.key,
      value: item.key,
      text: item.name
    }));
    setElements(_elements);
  }

  // eslint-disable-next-line no-unused-vars
  const { tableProps, params, refresh } = useRequest(
    ({ current, pageSize, sorter: s, filters: f }) => {
      const p = { page: current, size: pageSize };

      if (s?.field && s?.order) {
        setSorterInfo({ columnKey: s.field, order: s.order });
        p.sorts = [
          {
            direction: _.includes(s?.order, 'desc') ? 'desc' : 'asc',
            propertyName: s?.field
          }
        ];
      } else if (!s || !s.order) {
        setSorterInfo({ columnKey: null, order: null });
        p.sorts = [
          {
            direction: 'desc',
            propertyName: activeKey === 'ALL' || activeKey === 'MINE' ? 'updateTime' : 'operationTime'
            // propertyName: "updateTime"
          }
        ];
      }

      if (f) {
        Object.entries(f).forEach(([filed, value]) => {
          p[filed] = value;
        });
      }
      return getTableList({ ...p, search }, activeKey);
      // return new Promise((resolve) => {
      //   setTimeout(() => {
      //     resolve(getTableList({ ...p, search }, activeKey));
      //   }, 200);
      // });
    },
    {
      paginated: true,
      defaultPageSize: 10,
      pollingInterval: 60000,
      refreshDeps: [search],
      cacheKey: state && 'cacheKey'
    }
  );

  const searchRef = useRef(search);

  searchRef.current = search;

  const filterDropDownitems = (menuList, item) => {
    let _menuList = _.cloneDeep(menuList);
    if (!projectShareStatus || item.createUserId !== userId) {
      _menuList = _menuList.filter((e) => e.key !== 'share' && e.key !== 'cooperateManage');
    }
    return _menuList.map((menuItem) => {
      if (!menuItem.code || (menuItem.code && CheckAuth.checkAuth(menuItem.code)))
        return {
          label: <span onClick={() => onColumnActionClick(menuItem.key, menuItem.label, item)}>{menuItem.label}</span>,
          key: menuItem.key
        };
      return null;
    });
  };

  const menu = (item) => {
    const _cardDownMenuStop = _.cloneDeep(config.cardDragStopDownMenu);
    const _cardDownMenu = _.cloneDeep(config.cardDragDownMenu);

    if (item.status === 'TERMINATED') {
      let _newCardMenuStop;
      if (item.userOperationRecord) {
        _newCardMenuStop = _.filter(_cardDownMenuStop, (o) => o.key !== 'collect');
      } else {
        _newCardMenuStop = _.filter(_cardDownMenuStop, (o) => o.key !== 'cancelCollect');
      }
      return filterDropDownitems(_newCardMenuStop, item);
    } else {
      let _newCardMenu;
      if (item.userOperationRecord) {
        _newCardMenu = _.filter(_cardDownMenu, (o) => o.key !== 'collect');
      } else {
        _newCardMenu = _.filter(_cardDownMenu, (o) => o.key !== 'cancelCollect');
      }

      return filterDropDownitems(_newCardMenu, item);
    }
  };

  const onColumnActionClick = async (key, feature, record) => {
    setEditValue(record);
    const { id } = record;
    const userInfo = await userService.getCurrentUser();
    if (key === 'edit') {
      setIsShowEdit({ status: true, idEdit: id });
      changeVisible(true);
    } else if (key === 'detail') {
      onView(record.id, record);
    } else if (key === 'share' || key === 'cooperateManage') {
      dispatchShare(
        key === 'share'
          ? {
              shareOpen: true,
              shareInfo: { type: 'STRATEGY', id: record.id }
            }
          : {
              cooperateOpen: true,
              shareInfo: { type: 'STRATEGY', id: record.id }
            }
      );
    } else if (key === 'collect') {
      saveCollect(record, userInfo.id);
    } else if (key === 'cancelCollect') {
      delCollect(record, userInfo.id);
    } else if (key === 'delete') {
      Modal.confirm({
        title: t('dataCenter-xmbk4yliJtB9'),
        className: 'campainsDelWrap',
        content: <p className="confirmDelete">{t('dataCenter-FtDbOIPFB2EG')}{record.name}{t('dataCenter-vFoNQoB8mBBR')}</p>,
        okText: t('dataCenter-JfW4IvqvHB9a'),
        okType: 'danger',
        cancelText: t('dataCenter-xujEOCdXqerA'),
        async onOk() {
          await campaignsService.delCampaigns(record.id);
          message.success(t('dataCenter-P4l2EuJg2KfA'));
          setSearch([...searchRef.current]);
        },
        onCancel() {}
      });
    } else if (key === 'stop') {
      Modal.confirm({
        title: t('dataCenter-RKFpao6EtOou'),
        className: 'campainsDelWrap',
        content: <p className="confirmDelete">{t('dataCenter-Yg5zEf09TGAJ')}{record.name}{t('dataCenter-vFoNQoB8mBBR')}</p>,
        okText: t('dataCenter-DEcnj5BxqJ4d'),
        okType: 'danger',
        cancelText: t('dataCenter-zKkxyLwAsR94'),
        async onOk() {
          await campaignsService.stopCampaigns(record.id);
          saveRecordInfo(id, userInfo.id);
          message.success(t('dataCenter-90PwsQx1hSUa'));
          setSearch([...searchRef.current]);
        },
        onCancel() {}
      });
    }
  };

  const actionColumn = {
    title: t('dataCenter-pIZ7h8Bi1IaI'),
    className: 'td-set',
    width: 120,
    fixed: 'right',
    render: (text, record) => {
      const _featureListStop = _.cloneDeep(featureDataStop);
      const _featureList = _.cloneDeep(featureData);

      if (record.status === 'TERMINATED') {
        if (record.userOperationRecord) {
          delete _featureListStop.dropdownData.collect;
        } else {
          delete _featureListStop.dropdownData.cancelCollect;
        }
      } else {
        if (record.userOperationRecord) {
          delete _featureList.dropdownData.collect;
        } else {
          delete _featureList.dropdownData.cancelCollect;
        }
      }

      const shareStatus = localStorage.getItem('projectShareStatus');

      if (shareStatus === 'DISABLE' || record.createUserId !== Number(localStorage.getItem('userId'))) {
        delete _featureList.dropdownData.share;
        delete _featureList.dropdownData.cooperateManage;
        delete _featureListStop.dropdownData.share;
        delete _featureListStop.dropdownData.cooperateManage;
      }

      return (
        <ColumnActionCom
          closeDropdown
          featureData={record.status === 'TERMINATED' ? _featureListStop : _featureList}
          record={record}
          onClick={onColumnActionClick}
        />
      );
    }
  };

  const [columns, setColumns] = useState(columnsList);

  // you can read sorter and filters from params[0]
  // const { sorter = {}, filters = {} } = params[0];

  const queryData = useCallback(
    (data) => {
      let _search = _.cloneDeep(search);
      const outFilterList = _search.filter(
        (item) =>
          item.propertyName === 'status' ||
          item.propertyName === 'ownerId' ||
          item.propertyName === 'orderNo' ||
          item.propertyName === 'name'
      );
      if (statusVal || owerVal || inputRef.current.input.value) {
        if (!data.length) {
          _search = outFilterList;
          setDefaultFormData({});
        } else {
          _search = [...search, ...data];
        }
      } else {
        if (!data.length) {
          _search = [...data];
          setDefaultFormData({});
        } else {
          _search = [...search, ...data];
        }
      }
      const _filters = _.map(_search, (item) => {
        return {
          connector: 'AND',
          propertyName: item.propertyName,
          operator: item.operator,
          value:
            item.operator === 'DATE_BETWEEN' && _.isArray(item.value)
              ? item.value.map((item) => dayjs(item).valueOf()).join(',')
              : item.value
        };
      });
      setFilterVisible(false);
      setSearch(uniqueAfterArr(_filters, 'propertyName'));
    },
    [search]
  );

  // 增加操作列
  useEffect(() => {
    const _columns = _.cloneDeep(columns);
    _columns.push(actionColumn);
    _columns.forEach((item) => {
      if (item.title !== t('dataCenter-pIZ7h8Bi1IaI')) {
        item.sortOrder = sorterInfo.columnKey === item.dataIndex ? sorterInfo.order : null;
      }
    });
    const res = new Map();
    const unqieColumns = _columns.filter((item) => !res.has(item.title) && res.set(item.title, 1));
    setColumns(unqieColumns);
  }, [sorterInfo, projectShareStatus]);

  // 改变弹出框可见状态
  const changeVisible = (visible) => {
    setVisible(visible);
  };

  const onTabsClick = (activeKey) => {
    if (activeKey === 'ALL' || activeKey === 'MINE') {
      const _search = _.filter(search, (n) => n.propertyName !== 'recordType');
      setSearch(_search);
      getTableList(
        {
          size: pagination.pageSize,
          search: _search,
          sorts: [{ direction: 'desc', propertyName: 'updateTime' }],
          page: 1
        },
        activeKey
      );
    } else {
      getTableList(
        {
          size: pagination.pageSize,
          search,
          sorts: [{ direction: 'desc', propertyName: 'operationTime' }],
          page: 1
        },
        activeKey
      );
    }

    // localStorage.setItem('campaignTabKey', activeKey);
    // localStorage.setItem('campaignCache', true);
    setSorterInfo({ columnKey: null, order: null });
    // setSearch([...search])
    setActiveKey(activeKey);
  };

  const onCardChange = (pageNumber, pageSize) => {
    params[0].current = pageNumber;
    params[0].pageSize = pageSize;
    getTableList({ page: pageNumber, size: pageSize, search, sorts: pagination.sorts }, activeKey);
    refresh();
  };

  const addCampasChange = () => {
    changeVisible(true);
    setIsShowEdit({ status: true, idEdit: null });
  };

  const onTextChange = _.debounce((val) => {
    let _search = _.cloneDeep(search);
    if (searchSelectVal === 'name') {
      const index = _.findIndex(_search, (v) => v.propertyName === 'name');
      if (index !== -1) {
        _search.splice(index, 1);
      }
      _search = [..._search, { operator: 'LIKE', propertyName: 'name', value: val }];
    } else {
      if (val !== '' && !Number(val)) {
        message.error(t('dataCenter-iIqj5FZenGcM'));
        return;
      }
      const index = _.findIndex(_search, (v) => v.propertyName === 'orderNo');
      if (index !== -1) {
        _search.splice(index, 1);
      }
      _search = [..._search, { operator: 'EQ', propertyName: 'orderNo', value: val }];
    }
    setSearch(_search);
  }, 500);

  const onStatusChange = (val) => {
    let _search = _.cloneDeep(search);
    const index = _.findIndex(_search, (v) => v.propertyName === 'status');
    if (index !== -1) {
      _search.splice(index, 1);
    }
    _search = [..._search, { propertyName: 'status', operator: 'EQ', value: val || '' }];
    setSearch(_search);
    setStatusVal(val);
  };

  const onOwerChange = (val) => {
    let _search = _.cloneDeep(search);
    const index = _.findIndex(_search, (v) => v.propertyName === 'ownerId');
    if (index !== -1) {
      _search.splice(index, 1);
    }
    _search = [..._search, { propertyName: 'ownerId', operator: 'EQ', value: val || '' }];
    setSearch(_search);
    setOwerVal(val);
  };

  const onSelect = (value) => {
    // inputRef.current.state.value = '';
    setNativeValue([inputRef.current.input], '');
    let _search = _.cloneDeep(search);
    const index = _.findIndex(_search, (v) => v.propertyName === 'name' || v.propertyName === 'orderNo');
    if (index !== -1) {
      _search.splice(index, 1);
    }
    _search = [
      ..._search,
      {
        operator: 'EQ',
        propertyName: value,
        value: inputRef.current.input.value
      }
    ];
    setSearch(_search);
    setSearchSelectVal(value);
  };

  const switchDataShowType = (type) => {
    setDataShowType(type);
    props.history.replace({ state: { ...state, dataShowType: type } });
  };

  const onRecentMenuChange = (key) => {
    setRecentSelectKey(key);
    let _search = _.cloneDeep(search);
    const index = _.findIndex(_search, (v) => v.propertyName === 'operationTime');
    if (index !== -1) {
      _search.splice(index, 1);
    }
    _search = [
      ..._search,
      {
        propertyName: 'operationTime',
        operator: 'DATE_BETWEEN',
        value: getRecentRange(key)
      }
    ];
    setSearch([..._search]);
    // localStorage.setItem('userGroupFilter', JSON.stringify(_tableParams.search));
    // getTableList(_tableParams, activeKey);
  };

  // const recentDomSet = () => {
  //   return (
  //     <Menu onClick={onRecentMenuChange} selectedKeys={recentSelectKey}>
  //       <Menu.Item key="WEEK">一周</Menu.Item>
  //       <Menu.Item key="MON">一个月</Menu.Item>
  //       <Menu.Item key="TREEEMON">三个月</Menu.Item>
  //       <Menu.Item key="SIXMON">六个月</Menu.Item>
  //     </Menu>
  //   );
  // };

  const filterMoreContent = () => {
    const _element = _.cloneDeep(elements);
    const resElements = {
      startTime: _element.startTime,
      endTime: _element.endTime,
      createUserId: _element.createUserId
    };
    return (
      <div className="moreContent">
        <QueryForList show elements={resElements} onQuery={queryData} defaultFormData={defaultFormData} />
      </div>
    );
  };

  const items = [
    {
      label: (
        <a rel="noopener noreferrer" onClick={() => onRecentMenuChange('WEEK')}>
          {t('dataCenter-kvLso8Cy0KwW')}
        </a>
      ),
      key: 'WEEK'
    },
    {
      label: (
        <a rel="noopener noreferrer" onClick={() => onRecentMenuChange('MON')}>
          {t('dataCenter-S1InbxM3XbVJ')}
        </a>
      ),
      key: 'MON'
    },
    {
      label: (
        <a rel="noopener noreferrer" onClick={() => onRecentMenuChange('TREEEMON')}>
          {t('dataCenter-d7CYNHOtFKKP')}
        </a>
      ),
      key: 'TREEEMON'
    },
    {
      label: (
        <a rel="noopener noreferrer" onClick={() => onRecentMenuChange('SIXMON')}>
          {t('dataCenter-m9EyrevutEu1')}
        </a>
      ),
      key: 'SIXMON'
    }
  ];

  return (
    <div className="campainsForm">
      <header>
        <h1>{getMenuTitle(props.meunData, props.history.location.pathname)}</h1>
        <div className="rightSide">
          <Input.Group compact>
            <Select style={{ width: '72px' }} value={searchSelectVal} onSelect={onSelect}>
              <Option value="name">{t('dataCenter-6FoXyfGEDqoH')}</Option>
              <Option value="orderNo">ID</Option>
            </Select>

            <Input
              onChange={(e) => onTextChange(e.target.value)}
              style={{ width: '208px' }}
              suffix={<SearchOutlined />}
              ref={inputRef}
            />
          </Input.Group>
          <Button
            className="DTButton bor_ra-6"
            onClick={() => props.history.push('/aimarketer/home/<USER>/shareList')}
          >
            {t('dataCenter-m8pprbq0JZpe')}
          </Button>
          <CheckAuth code="aim_campaigns_edit">
            <Button className="DTButton bor_ra-6" type="primary" onClick={addCampasChange}>
              {t('dataCenter-rakgo9Po7WFD')}
            </Button>
          </CheckAuth>
        </div>
      </header>
      {/* <div className="campainsQueryForList">
        <QueryForList show={isFold} elements={elements} onQuery={queryData} />
      </div> */}
      <div className="filterWrap">
        <div className="left">
          <div className={`filterItem ${activeKey === 'ALL' ? 'active' : ''}`} onClick={() => onTabsClick('ALL')}>
            {t('dataCenter-tg5Td8NdO6eN')}
            {loading ? null : `${activeKey === 'ALL' ? `(${campaignsData ? campaignsData.totalElements : 0})` : ''}`}
          </div>
          <div className={`filterItem ${activeKey === 'MINE' ? 'active' : ''}`} onClick={() => onTabsClick('MINE')}>
            {t('dataCenter-Q67jXImXLHah')}
            {loading ? null : `${activeKey === 'MINE' ? `(${campaignsData ? campaignsData.totalElements : 0})` : ''}`}
          </div>
          <div style={{ marginRight: 32 }}>
            <span
              style={{ marginRight: 0 }}
              className={`filterItem ${activeKey === 'RECENT' ? 'active' : ''}`}
              onClick={() => onTabsClick('RECENT')}
            >
              {t('dataCenter-G6XUQmnFQGAE')}
              {loading
                ? null
                : `${activeKey === 'RECENT' ? `(${campaignsData ? campaignsData.totalElements : 0})` : ''}`}
            </span>
            <span>
              {activeKey === 'RECENT' ? (
                <Dropdown menu={{ items, selectedKeys: recentSelectKey }} trigger="click">
                  <DownOutlined
                    style={{
                      marginLeft: 8,
                      cursor: 'pointer',
                      color: 'rgba(0, 0, 0, 0.65)'
                    }}
                  />
                </Dropdown>
              ) : null}
            </span>
          </div>
          <div
            className={`filterItem ${activeKey === 'FAVORITE' ? 'active' : ''}`}
            onClick={() => onTabsClick('FAVORITE')}
          >
            {t('dataCenter-SIjZYIva9YQH')}
            {loading
              ? null
              : `${activeKey === 'FAVORITE' ? `(${campaignsData ? campaignsData.totalElements : 0})` : ''}`}
          </div>
        </div>
        <div className="right">
          <div className="rightFilter">
            <Select
              style={{ marginRight: '8px' }}
              allowClear
              value={statusVal}
              onChange={onStatusChange}
              showSearch
              optionFilterProp="children"
              placeholder={t('dataCenter-wWucwAIyZLHW')}
              bordered={false}
            >
              {elements.status.componentOptions.options.map((item) => (
                <Option key={item.key} value={item.value.toString()}>
                  {item.text}
                </Option>
              ))}
            </Select>

            <Select
              placeholder={t('dataCenter-VOKP4acEnLPI')}
              bordered={false}
              allowClear
              value={owerVal}
              onChange={onOwerChange}
              showSearch
              optionFilterProp="children"
            >
              {elements.ownerId.componentOptions.options.map((item) => (
                <Option key={item.key} value={item.value.toString()}>
                  {`${item.text}`}
                </Option>
              ))}
            </Select>

            <Popover
              content={filterMoreContent}
              trigger="click"
              overlayClassName="campaignsFilterPop"
              placement="topRight"
              onOpenChange={(e) => setFilterVisible(e)}
              open={filterVisible}
            >
              <div className="moreFilterText" onClick={() => setFilterVisible(true)}>
                {t('dataCenter-U4Nq33dxycHX')}
                <DownOutlined />
              </div>
            </Popover>
          </div>
          {dataShowType === 'table' ? (
            <AppstoreOutlined style={{ cursor: 'pointer' }} onClick={() => switchDataShowType('card')} />
          ) : (
            <UnorderedListOutlined style={{ cursor: 'pointer' }} onClick={() => switchDataShowType('table')} />
          )}
        </div>
      </div>
      <Table
        columns={columns}
        className="table1 bor_ra-6"
        //  style={{visibility: dataShowType === 'card' ? 'hidden' : 'visible'}}
        style={dataShowType === 'card' ? { visibility: 'hidden', height: 0, padding: 0 } : {}}
        rowKey="id"
        {...tableProps}
        dataSource={campaignsData?.content}
        loading={loading}
        pagination={{
          ...tableProps.pagination,
          showSizeChanger: true,
          total: campaignsData?.totalElements,
          current: pagination.current,
          showLessItems: true,
          pageSizeOptions: ['10', '20', '50'],
          showTotal: (e) => `${t('dataCenter-oVHOMzdWo1X7')} ${e} ${t('dataCenter-GviPAx92Kah6')}`,
          showQuickJumper: true
        }}
        scroll={{ x: 1300 }}
      />

      <div style={dataShowType === 'card' ? {} : { display: 'none', height: 0, padding: 0 }}>
        <Spin spinning={loading}>
          <div className="cardWrap">
            {campaignsData?.totalElements ? (
              campaignsData?.content.map((item) => (
                <div key={item.id} className="cardItem">
                  <div className="cardInfo">
                    <div className="infoImg">
                      {item.snapshot ? (
                        item.snapshot.type === 'EMOJI' ? (
                          <div className="emojiWrap">
                            <span style={{ fontSize: 66 }}>
                              {
                                data.emojis[item.snapshot.id].skins.find(
                                  (skinItem) => skinItem.unified === item.snapshot.unified
                                ).native
                              }
                            </span>
                          </div>
                        ) : (
                          <img src={transformUrl(item.snapshot.url)} alt="" />
                        )
                      ) : (
                        <img src={logo} alt="" />
                      )}
                    </div>
                    <div className="infoDesc">
                      <div className="itemTitle" onClick={() => onView(item.id, item)}>
                        <Tooltip title={item.name}>
                          <div className="desc-title">{item.name}</div>
                        </Tooltip>
                        <span
                          className="campaignsStatus"
                          style={{
                            backgroundColor: _.filter(config.status, (v) => v.value === item.status)[0]?.background
                          }}
                        >
                          <div className="status">
                            <span
                              className="circle"
                              style={{
                                backgroundColor: _.filter(config.status, (v) => v.value === item.status)[0]?.color
                              }}
                            />
                            <span
                              className="statusName"
                              style={{
                                color: _.filter(config.status, (v) => v.value === item.status)[0]?.color
                              }}
                            >
                              {_.filter(config.status, (v) => v.value === item.status)[0]?.text}
                            </span>
                          </div>
                        </span>
                      </div>
                      <div className="itemDescItem">
                        <div className="descItem">
                          <MyIcon type="icon-icon-id" style={{ fontSize: 16 }} /> ID：{item.orderNo}
                        </div>
                        <div className="descItem">
                          <MyIcon type="icon-icon-owner" style={{ fontSize: 16 }} /> {t('dataCenter-5obYLiOV9pLV')}{item.owner?.name}
                        </div>
                        <div className="descItem">
                          <MyIcon type="icon-icon-campaigntime" style={{ fontSize: 16 }} /> {t('dataCenter-PYJbnJa9QyXu')}：
                          {dayjs(item.startTime).format('YYYY-MM-DD')} ~ {dayjs(item.endTime).format('YYYY-MM-DD')}
                        </div>
                        <div className="descItem">
                          <MyIcon type="icon-icon-owner" style={{ fontSize: 16 }} /> {item.updateUserName} {t('dataCenter-ODS7fZcNRfwF')}{' '}
                          {dayjs(item.updateTime).format('YYYY.MM.DD')}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="cardNorm">
                    {item.metricsVoList?.map((iconItem) => (
                      <div key={iconItem.key} className="normItem">
                        <div className="normVal">
                          {numConvert(iconItem.value)}
                          {parseInt(iconItem.value) < 1000 ? null : '+'}
                        </div>
                        <div className="normTitle">
                          <MyIcon type={config.normIconList[iconItem.key]} style={{ fontSize: 16 }} />
                          <span className="normName">{iconItem.name}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="cardOptions">
                    <div className="optionsWrap">
                      <Button className="detail" onClick={() => onView(item.id, item)}>
                        {t('dataCenter-90Y7Jp6WSHIM')}
                      </Button>
                      <Dropdown
                        placement="bottom"
                        getPopupContainer={(triggerNode) => triggerNode.parentNode}
                        menu={{ items: menu(item) }}
                      >
                        <Button className="optionIcon">
                          <EllipsisOutlined />
                        </Button>
                      </Dropdown>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="cardItem">
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ position: 'absolute', left: '50%' }} />
              </div>
            )}

            {campaignsData?.totalElements ? (
              <Pagination
                showQuickJumper
                current={pagination.current}
                total={pagination.total}
                onChange={onCardChange}
                className="cardPaination"
                showTotal={(total) => `${t('dataCenter-oVHOMzdWo1X7')} ${total} ${t('dataCenter-GviPAx92Kah6')}`}
                showSizeChanger
                pageSizeOptions={[10, 20, 50]}
                pageSize={pagination.pageSize}
              />
            ) : null}
          </div>
        </Spin>
      </div>

      {visible && (
        <AddCampains
          visible={visible}
          history={props.history}
          action={() => setVisible(false)}
          editValue={editValue}
          isShowEdit={isShowEdit}
          refresh={refresh}
          userId={userId}
          onClose={() => {
            setVisible(false);
            setIsShowEdit({ status: false, idEdit: null });
            setEditValue({});
          }}
        />
      )}
    </div>
  );
};

export default connect(stateToProps)(Campaigns);
