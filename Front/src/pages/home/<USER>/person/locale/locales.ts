export default {
  cn: {
    // personSetting/personSetting.jsx translations - Front/src/pages/home/<USER>/personSetting/personSetting.jsx
    'setting-dIVc6jvCGdzS': '个人设置', // Personal Settings

    // personSetting/configs.js translations - Front/src/pages/home/<USER>/personSetting/configs.js
    'setting-zPUoXirDDQKT': '账号设置', // Account Settings
    'setting-3o2235LSuWIQ': '皮肤设置', // Theme Settings

    // person/list/index.jsx translations - Front/src/pages/home/<USER>/person/list/index.jsx
    'setting-PWiJ02BiJWWD': '登录时间', // Login Time
    'setting-vsHPNb7TxxIs': '登录状态', // Login Status
    'setting-ILxDxXH5UQS0': '登录成功', // Login Success
    'setting-XyfnIK5ttDqe': '登录失败', // Login Failed
    'setting-2270dW4DRj3m': '发送失败，请重试', // Send failed, please try again
    'setting-fRchdu3rvACw': '发送成功', // Send successful
    'setting-CMA0Fdw3Ay6F': '安全等级高', // High security level
    'setting-xuqHtgDAGSYW': '安全等级中', // Medium security level
    'setting-g62XPGdhBA7w': '安全等级低', // Low security level
    'setting-8cyCLFViJb26': '账号设置', // Account Settings
    'setting-uH2zrzXfxTaj': '账号信息', // Account Information
    'setting-M4FuQs93bjZ7': '编辑', // Edit
    'setting-Y15I18L0MkQp': '用户姓名', // User Name
    'setting-An45O0AOJJBD': '员工号', // Employee ID
    'setting-cH739OLbb6gF': '修改', // Modify
    'setting-0r9o4P31dXR9': '验证', // Verify
    'setting-XFwdm05zzYTj': '邮箱', // Email
    'setting-650KVDOS7wuA': '已验证', // Verified
    'setting-3jbVAWvOQeSq': '未验证', // Unverified
    'setting-nK5YtBdkgu0v': '手机号码', // Mobile Number
    'setting-dh6U7BH8Devk': '修改', // Modify
    'setting-TBr81ER2EpvM': '设置', // Set
    'setting-zqtPlr4aq6dZ': '密码', // Password
    'setting-0CB77UdX3N69': '创建时间', // Creation Time
    'setting-TU0ryqK4aJb8': '最近10次访问记录', // Recent 10 Access Records

    // person/updateInfo/updateEmail.jsx translations - Front/src/pages/home/<USER>/person/updateInfo/updateEmail.jsx
    'setting-o0ljLtadCgxf': '邮箱已存在！', // Email already exists!
    'setting-QtRGIW5HnVSe': '修改成功', // Modification successful
    'setting-gLACfCh0sCwv': '密码错误，请重试！', // Password incorrect, please try again!
    'setting-gfia3ktH2V0O': '修改邮箱', // Modify Email
    'setting-XnayjZCqHuh2': '登录密码', // Login Password
    'setting-UVMGtbaXLrAB': '请输入登录密码', // Please enter login password
    'setting-uK6ZVJT0cwWz': '新邮箱', // New Email
    'setting-OJhLunDYt2k3': '请输入新邮箱', // Please enter new email
    'setting-F4u8VaRSYSpf': '请输入正确的邮箱格式', // Please enter correct email format

    // person/updateInfo/updateMobile.jsx translations - Front/src/pages/home/<USER>/person/updateInfo/updateMobile.jsx
    'setting-NklXc1KkxVCj': '修改成功', // Modification successful
    'setting-jwFLZUOMJPNT': '密码错误，请重试！', // Password incorrect, please try again!
    'setting-PUAkggPcz52J': '发送失败，请重试', // Send failed, please try again
    'setting-ELofcBOMx9yL': '发送成功', // Send successful
    'setting-vrQgIsFW4GBt': '修改手机号', // Modify Mobile Number
    'setting-mero1tF7ujZu': '登录密码', // Login Password
    'setting-01EyFd13gnnc': '请输入登录密码', // Please enter login password
    'setting-067TOHookvlc': '新手机号', // New Mobile Number
    'setting-RhwGm3k1oJFx': '请输入新手机号', // Please enter new mobile number
    'setting-v6bk17U3DnCq': '请输入正确手机号', // Please enter correct mobile number
    'setting-MC5xIAu23RwH': '手机号已存在', // Mobile number already exists
    'setting-YnUtir02b6tO': '验证码', // Verification Code
    'setting-L1u95v3mQ4yf': '请输入验证码', // Please enter verification code
    'setting-YRWhy6lJXmYv': '秒后重试', // seconds to retry
    'setting-1R3x2tq0aQHa': '获取验证码', // Get Verification Code

    // person/updateInfo/updateName.jsx translations - Front/src/pages/home/<USER>/person/updateInfo/updateName.jsx
    'setting-DqODMzWaoqBr': '修改成功', // Modification successful
    'setting-wgfQly1CNFqg': '编辑用户姓名', // Edit User Name
    'setting-HMoEtBYE62Lt': '用户姓名', // User Name
    'setting-m979toMKoHsu': '请输入用户姓名', // Please enter user name
    'setting-WVPMCxGTqdV9': '最大支持32个字符，请重新输入', // Maximum 32 characters, please re-enter
    'setting-z8exow7pcynO': '请输入用户姓名', // Please enter user name

    // person/updateInfo/updatePassword.jsx translations - Front/src/pages/home/<USER>/person/updateInfo/updatePassword.jsx
    'setting-QrscplHDAm7g': '修改密码成功', // Password modification successful
    'setting-bhboTiA5kLNj': '设置密码成功', // Password setting successful
    'setting-crhRarTg0nER': '两次输入的密码不相同!', // The two passwords entered are different!
    'setting-xBCFKhdZ7Qjx': '修改密码', // Modify Password
    'setting-rsnehUFLdbZM': '设置密码', // Set Password
    'setting-aMSTUD1lIYZB': '原密码', // Original Password
    'setting-eRrKBD4EKeFd': '请输入原密码', // Please enter original password
    'setting-ycMNzyh8tKk6': '新密码', // New Password
    'setting-KyFRoPXZwItS': '请输入新密码', // Please enter new password
    'setting-jfEEROAlaKNc': '密码必须包含大写字母、小写字母、数字和特殊字符，长度大于{{minLength}}个字符', // Password must contain uppercase letters, lowercase letters, numbers and special characters, length greater than {{minLength}} characters
    'setting-mv3Q71bPhJ9Q': '必须为6-32位的数字字母或特殊字符', // Must be 6-32 digits, letters or special characters
    'setting-V2D6F2NUZIem': '密码不能和登录账号（邮箱/手机号/员工号）相同', // Password cannot be the same as login account (email/mobile/employee ID)
    'setting-eQA6SwRlKNJu': '请求错误', // Request error
    'setting-Mjxn2AcJWpxv': '请输入新密码', // Please enter new password
    'setting-Zl2w4hnNZE5R': '确认新密码', // Confirm New Password
    'setting-jx1Zgs6AFluk': '请输入确认新密码', // Please enter confirm new password
    'setting-SW84HbuRxSJc': '请输入确认新密码', // Please enter confirm new password

    // person/verifyMail/index.js translations - Front/src/pages/home/<USER>/person/verifyMail/index.js
    'setting-VSlV50Ln4qvX': '操作完成', // Operation completed
    'setting-gloZIodmLGSj': '邮箱验证成功', // Email verification successful
    'setting-TmYTg75cRRkN': '返回设置', // Return to Settings
    'setting-xs752C3IdMlU': '发送失败，请重试', // Send failed, please try again
    'setting-yRSbqGd8lJNV': '发送成功', // Send successful
    'setting-ylKgTSpK1ajG': '验证邮箱', // Verify Email
    'setting-Tvz4cKHP5X6U': '验证码', // Verification Code
    'setting-CNEXQp93Jcb7': '请输入验证码!', // Please enter verification code!
    'setting-U557L4BRbcyp': '秒后重试', // seconds to retry
    'setting-qHpBKwF6S82P': '获取验证码', // Get Verification Code
    'setting-CIBxTetGYoQM': '确定' // Confirm
  },
  en: {
    // personSetting/personSetting.jsx translations - Front/src/pages/home/<USER>/personSetting/personSetting.jsx
    'setting-dIVc6jvCGdzS': 'Personal Settings',

    // personSetting/configs.js translations - Front/src/pages/home/<USER>/personSetting/configs.js
    'setting-zPUoXirDDQKT': 'Account Settings',
    'setting-3o2235LSuWIQ': 'Theme Settings',

    // person/list/index.jsx translations - Front/src/pages/home/<USER>/person/list/index.jsx
    'setting-PWiJ02BiJWWD': 'Login Time',
    'setting-vsHPNb7TxxIs': 'Login Status',
    'setting-ILxDxXH5UQS0': 'Login Success',
    'setting-XyfnIK5ttDqe': 'Login Failed',
    'setting-2270dW4DRj3m': 'Send failed, please try again',
    'setting-fRchdu3rvACw': 'Send successful',
    'setting-CMA0Fdw3Ay6F': 'High security level',
    'setting-xuqHtgDAGSYW': 'Medium security level',
    'setting-g62XPGdhBA7w': 'Low security level',
    'setting-8cyCLFViJb26': 'Account Settings',
    'setting-uH2zrzXfxTaj': 'Account Information',
    'setting-M4FuQs93bjZ7': 'Edit',
    'setting-Y15I18L0MkQp': 'User Name',
    'setting-An45O0AOJJBD': 'Employee ID',
    'setting-cH739OLbb6gF': 'Modify',
    'setting-0r9o4P31dXR9': 'Verify',
    'setting-XFwdm05zzYTj': 'Email',
    'setting-650KVDOS7wuA': 'Verified',
    'setting-3jbVAWvOQeSq': 'Unverified',
    'setting-nK5YtBdkgu0v': 'Mobile Number',
    'setting-dh6U7BH8Devk': 'Modify',
    'setting-TBr81ER2EpvM': 'Set',
    'setting-zqtPlr4aq6dZ': 'Password',
    'setting-0CB77UdX3N69': 'Creation Time',
    'setting-TU0ryqK4aJb8': 'Recent 10 Access Records',

    // person/updateInfo/updateEmail.jsx translations - Front/src/pages/home/<USER>/person/updateInfo/updateEmail.jsx
    'setting-o0ljLtadCgxf': 'Email already exists!',
    'setting-QtRGIW5HnVSe': 'Modification successful',
    'setting-gLACfCh0sCwv': 'Password incorrect, please try again!',
    'setting-gfia3ktH2V0O': 'Modify Email',
    'setting-XnayjZCqHuh2': 'Login Password',
    'setting-UVMGtbaXLrAB': 'Please enter login password',
    'setting-uK6ZVJT0cwWz': 'New Email',
    'setting-OJhLunDYt2k3': 'Please enter new email',
    'setting-F4u8VaRSYSpf': 'Please enter correct email format',

    // person/updateInfo/updateMobile.jsx translations - Front/src/pages/home/<USER>/person/updateInfo/updateMobile.jsx
    'setting-NklXc1KkxVCj': 'Modification successful',
    'setting-jwFLZUOMJPNT': 'Password incorrect, please try again!',
    'setting-PUAkggPcz52J': 'Send failed, please try again',
    'setting-ELofcBOMx9yL': 'Send successful',
    'setting-vrQgIsFW4GBt': 'Modify Mobile Number',
    'setting-mero1tF7ujZu': 'Login Password',
    'setting-01EyFd13gnnc': 'Please enter login password',
    'setting-067TOHookvlc': 'New Mobile Number',
    'setting-RhwGm3k1oJFx': 'Please enter new mobile number',
    'setting-v6bk17U3DnCq': 'Please enter correct mobile number',
    'setting-MC5xIAu23RwH': 'Mobile number already exists',
    'setting-YnUtir02b6tO': 'Verification Code',
    'setting-L1u95v3mQ4yf': 'Please enter verification code',
    'setting-YRWhy6lJXmYv': 'seconds to retry',
    'setting-1R3x2tq0aQHa': 'Get Verification Code',

    // person/updateInfo/updateName.jsx translations - Front/src/pages/home/<USER>/person/updateInfo/updateName.jsx
    'setting-DqODMzWaoqBr': 'Modification successful',
    'setting-wgfQly1CNFqg': 'Edit User Name',
    'setting-HMoEtBYE62Lt': 'User Name',
    'setting-m979toMKoHsu': 'Please enter user name',
    'setting-WVPMCxGTqdV9': 'Maximum 32 characters, please re-enter',
    'setting-z8exow7pcynO': 'Please enter user name',

    // person/updateInfo/updatePassword.jsx translations - Front/src/pages/home/<USER>/person/updateInfo/updatePassword.jsx
    'setting-QrscplHDAm7g': 'Password modification successful',
    'setting-bhboTiA5kLNj': 'Password setting successful',
    'setting-crhRarTg0nER': 'The two passwords entered are different!',
    'setting-xBCFKhdZ7Qjx': 'Modify Password',
    'setting-rsnehUFLdbZM': 'Set Password',
    'setting-aMSTUD1lIYZB': 'Original Password',
    'setting-eRrKBD4EKeFd': 'Please enter original password',
    'setting-ycMNzyh8tKk6': 'New Password',
    'setting-KyFRoPXZwItS': 'Please enter new password',
    'setting-jfEEROAlaKNc': 'Password must contain uppercase letters, lowercase letters, numbers and special characters, length greater than {{minLength}} characters',
    'setting-mv3Q71bPhJ9Q': 'Must be 6-32 digits, letters or special characters',
    'setting-V2D6F2NUZIem': 'Password cannot be the same as login account (email/mobile/employee ID)',
    'setting-eQA6SwRlKNJu': 'Request error',
    'setting-Mjxn2AcJWpxv': 'Please enter new password',
    'setting-Zl2w4hnNZE5R': 'Confirm New Password',
    'setting-jx1Zgs6AFluk': 'Please enter confirm new password',
    'setting-SW84HbuRxSJc': 'Please enter confirm new password',

    // person/verifyMail/index.js translations - Front/src/pages/home/<USER>/person/verifyMail/index.js
    'setting-VSlV50Ln4qvX': 'Operation completed',
    'setting-gloZIodmLGSj': 'Email verification successful',
    'setting-TmYTg75cRRkN': 'Return to Settings',
    'setting-xs752C3IdMlU': 'Send failed, please try again',
    'setting-yRSbqGd8lJNV': 'Send successful',
    'setting-ylKgTSpK1ajG': 'Verify Email',
    'setting-Tvz4cKHP5X6U': 'Verification Code',
    'setting-CNEXQp93Jcb7': 'Please enter verification code!',
    'setting-U557L4BRbcyp': 'seconds to retry',
    'setting-qHpBKwF6S82P': 'Get Verification Code',
    'setting-CIBxTetGYoQM': 'Confirm'
  }
};
