import React from 'react';
import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Modal, Input, message } from 'antd';
import UserServiceBefore from 'service/UserService';
import { t } from '@/utils/translation';

const UserService = new UserServiceBefore();
const UpdateName = (props) => {
  const {
    state: { nameModalVisible, nameValue, loading },
    dispatch,
    form: { getFieldDecorator, validateFields }
  } = props;

  const handleOk = () => {
    validateFields(async (err, fields) => {
      if (err) return;
      try {
        dispatch({ loading: true });
        await UserService.modifyUserName({ ...nameValue, ...fields });
        message.success(t('setting-DqODMzWaoqBr'));
        // const info = await UserService.getCurrentUser();
        window.location.reload();
        // dispatch({ loading: false, nameModalVisible: false, info });
      } catch {
        dispatch({ loading: false });
      }
    });
  };

  return (
    <div>
      <Modal
        title={t('setting-wgfQly1CNFqg')}
        open={nameModalVisible}
        onOk={handleOk}
        maskClosable={false}
        onCancel={() => dispatch({ nameModalVisible: false })}
        confirmLoading={loading}
      >
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
          <Form.Item label={t('setting-HMoEtBYE62Lt')}>
            {getFieldDecorator('newUsername', {
              initialValue: nameValue.newUsername,
              getValueFromEvent: (e) => e.target.value.trim(),
              rules: [
                { required: true, message: t('setting-m979toMKoHsu') },
                { max: 32, message: t('setting-WVPMCxGTqdV9') }
              ]
            })(<Input placeholder={t('setting-z8exow7pcynO')} />)}
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
export default Form.create()(UpdateName);
