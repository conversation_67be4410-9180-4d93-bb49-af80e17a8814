import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, message, Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import UserServiceBefore from 'service/UserService';
import { t } from '@/utils/translation';

const UserService = new UserServiceBefore();

const UpdatePassword = (props) => {
  const {
    state: { pwdModalVisible, isPwdExisted, info },
    dispatch,
    form: { getFieldDecorator, validateFields, getFieldValue }
  } = props;
  const [vaildData, setVaildData] = useState({});

  useEffect(() => {
    const init = async () => {
      const res = await UserService.loginReferenceListBy([
        { operator: 'EQ', propertyName: 'type', value: 'PASSWORD_SAFETY' },
        { operator: 'EQ', propertyName: 'code', value: 'safety.verify' }
      ]);
      setVaildData(res[0]);
    };
    init();
  }, []);

  const [loading, setLoading] = useState(false);

  const handleOk = () => {
    validateFields(async (err, fields) => {
      if (err) return;
      try {
        setLoading(true);
        // 根据是否修改过密码调用不同的接口
        if (isPwdExisted) {
          await UserService.modifyPassWord(fields);
          message.success(t('setting-QrscplHDAm7g'));
        } else {
          await UserService.modifyFirstPwd(fields);
          message.success(t('setting-bhboTiA5kLNj'));
        }
        setLoading(false);
        dispatch({ pwdModalVisible: false });
      } catch {
        setLoading(false);
      }
    });
  };

  const compareToFirstPassword = (rule, value, callback) => {
    if (value && value !== getFieldValue('pwd')) {
      callback(t('setting-crhRarTg0nER'));
    } else {
      callback();
    }
  };

  return (
    <div>
      <Modal
        title={isPwdExisted ? t('setting-xBCFKhdZ7Qjx') : t('setting-rsnehUFLdbZM')}
        open={pwdModalVisible}
        maskClosable={false}
        onOk={handleOk}
        onCancel={() => dispatch({ pwdModalVisible: false })}
        confirmLoading={loading}
      >
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
          {isPwdExisted ? (
            <Form.Item label={t('setting-aMSTUD1lIYZB')}>
              {getFieldDecorator('originalPwd', {
                rules: [{ required: true, message: t('setting-eRrKBD4EKeFd') }]
              })(<Input.Password placeholder={t('setting-u8woPJUNPnlD')} />)}
            </Form.Item>
          ) : null}
          <Form.Item label={t('setting-KyFRoPXZwItS')}>
            {getFieldDecorator('pwd', {
              rules: [
                { required: true, message: t('setting-Mjxn2AcJWpxv') },
                {
                  pattern:
                    vaildData?.value === 'ON'
                      ? new RegExp(
                          `^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()\\-_=+{};:,<.>])(?!.*[\u4E00-\u9FA5])(?!.*\\s).{${vaildData.config.minLength},24}$`
                        )
                      : /^[0-9A-Za-z\\W_=?.。~!@#$%^&*()-=]{6,32}$/,
                  message:
                    vaildData?.value === 'ON'
                      ? t('setting-jfEEROAlaKNc', { minLength: vaildData.config.minLength })
                      : t('setting-mv3Q71bPhJ9Q')
                },
                vaildData?.value === 'ON' && {
                  validator: async (_, inputValue) => {
                    try {
                      if (inputValue === info.email || inputValue === info.mobile || inputValue === info.jobNo) {
                        return Promise.reject(new Error(t('setting-V2D6F2NUZIem')));
                      } else {
                        return Promise.resolve();
                      }
                    } catch (error) {
                      return Promise.reject(new Error(t('setting-eQA6SwRlKNJu')));
                    }
                  }
                }
              ]
            })(<Input.Password placeholder={t('setting-SW84HbuRxSJc')} />)}
          </Form.Item>
          <Form.Item label={t('setting-Zl2w4hnNZE5R')}>
            {getFieldDecorator('confirmPwd', {
              rules: [{ required: true, message: t('setting-jx1Zgs6AFluk') }, { validator: compareToFirstPassword }]
            })(<Input.Password placeholder={t('setting-jx1Zgs6AFluk')} />)}
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
export default Form.create()(UpdatePassword);
