import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { Input, message, Modal } from 'antd';
import React, { useEffect, useState } from 'react';
import UserServiceBefore from 'service/UserService';
import { t } from '@/utils/translation';

const UserService = new UserServiceBefore();

const UpdatePassword = (props) => {
  const {
    state: { pwdModalVisible, isPwdExisted, info },
    dispatch,
    form: { getFieldDecorator, validateFields, getFieldValue }
  } = props;
  const [vaildData, setVaildData] = useState({});

  useEffect(() => {
    const init = async () => {
      const res = await UserService.loginReferenceListBy([
        { operator: 'EQ', propertyName: 'type', value: 'PASSWORD_SAFETY' },
        { operator: 'EQ', propertyName: 'code', value: 'safety.verify' }
      ]);
      setVaildData(res[0]);
    };
    init();
  }, []);

  const [loading, setLoading] = useState(false);

  const handleOk = () => {
    validateFields(async (err, fields) => {
      if (err) return;
      try {
        setLoading(true);
        // 根据是否修改过密码调用不同的接口
        if (isPwdExisted) {
          await UserService.modifyPassWord(fields);
          message.success(t('setting-QrscplHDAm7g'));
        } else {
          await UserService.modifyFirstPwd(fields);
          message.success(t('setting-Ej8Ej8Ej8Ej8'));
        }
        setLoading(false);
        dispatch({ pwdModalVisible: false });
      } catch {
        setLoading(false);
      }
    });
  };

  const compareToFirstPassword = (rule, value, callback) => {
    if (value && value !== getFieldValue('pwd')) {
      callback(t('setting-Ej8Ej8Ej8Ej9'));
    } else {
      callback();
    }
  };

  return (
    <div>
      <Modal
        title={isPwdExisted ? t('setting-Ej8Ej8Ej8Ej0') : t('setting-Ej8Ej8Ej8Ej1')}
        open={pwdModalVisible}
        maskClosable={false}
        onOk={handleOk}
        onCancel={() => dispatch({ pwdModalVisible: false })}
        confirmLoading={loading}
      >
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 14 }}>
          {isPwdExisted ? (
            <Form.Item label={t('setting-Ej8Ej8Ej8Ej2')}>
              {getFieldDecorator('originalPwd', {
                rules: [{ required: true, message: t('setting-Ej8Ej8Ej8Ej3') }]
              })(<Input.Password placeholder={t('setting-Ej8Ej8Ej8Ej4')} />)}
            </Form.Item>
          ) : null}
          <Form.Item label={t('setting-Ej8Ej8Ej8Ej5')}>
            {getFieldDecorator('pwd', {
              rules: [
                { required: true, message: t('setting-Ej8Ej8Ej8Ej6') },
                {
                  pattern:
                    vaildData?.value === 'ON'
                      ? new RegExp(
                          `^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()\\-_=+{};:,<.>])(?!.*[\u4E00-\u9FA5])(?!.*\\s).{${vaildData.config.minLength},24}$`
                        )
                      : /^[0-9A-Za-z\\W_=?.。~!@#$%^&*()-=]{6,32}$/,
                  message:
                    vaildData?.value === 'ON'
                      ? t('setting-Ej8Ej8Ej8Ej7', { minLength: vaildData.config.minLength })
                      : t('setting-Ej8Ej8Ej8Ej8')
                },
                vaildData?.value === 'ON' && {
                  validator: async (_, inputValue) => {
                    try {
                      if (inputValue === info.email || inputValue === info.mobile || inputValue === info.jobNo) {
                        return Promise.reject(new Error(t('setting-Ej8Ej8Ej8Ej9')));
                      } else {
                        return Promise.resolve();
                      }
                    } catch (error) {
                      return Promise.reject(new Error(t('setting-Ej8Ej8Ej8Ej10')));
                    }
                  }
                }
              ]
            })(<Input.Password placeholder={t('setting-Ej8Ej8Ej8Ej11')} />)}
          </Form.Item>
          <Form.Item label={t('setting-Ej8Ej8Ej8Ej12')}>
            {getFieldDecorator('confirmPwd', {
              rules: [{ required: true, message: t('setting-Ej8Ej8Ej8Ej13') }, { validator: compareToFirstPassword }]
            })(<Input.Password placeholder={t('setting-Ej8Ej8Ej8Ej14')} />)}
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
export default Form.create()(UpdatePassword);
