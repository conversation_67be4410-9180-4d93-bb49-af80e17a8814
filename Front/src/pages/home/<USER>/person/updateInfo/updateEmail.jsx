import { Form, Input, Modal, message } from 'antd';
import React from 'react';
import accountService from 'service/accountService';
import { t } from '@/utils/translation';

const UpdateEmailModal = (props) => {
  const [form] = Form.useForm();
  const {
    state: { emailModalVisible, info, loading },
    dispatch
  } = props;

  // 调用父组件确认方法，完成后关闭弹窗
  const onOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        try {
          dispatch({ loading: true });
          // 首先调用验证密码是否正确，如果正确调用
          const isEmailValid = await accountService.ensureAccount({
            email: values.account.trim()
          });
          if (!isEmailValid) {
            message.error(t('settign-o0ljLtadCgxf'));
            dispatch({ loading: false });
            return;
          }
          const isValid = await accountService.checkPassword({
            password: values.password.trim()
          });
          if (isValid) {
            await accountService.modifyAccount({
              type: 'EMAIL',
              account: values.account.trim(),
              enable: false
            });
            message.success(t('settign-QtRGIW5HnVSe'));
            // const info = await UserService.getCurrentUser();
            window.location.reload();
          } else {
            message.error(t('settign-gLACfCh0sCwv'));
            dispatch({ loading: false });
          }

          // dispatch({ loading: false, nameModalVisible: false, info });
        } catch {
          dispatch({ loading: false });
        }
      })
      .catch((info) => {
        console.error('Validate Failed:', info);
      });
  };

  return (
    <Modal
      confirmLoading={loading}
      title={t('settign-gfia3ktH2V0O')}
      open={emailModalVisible}
      onOk={onOk}
      onCancel={() => dispatch({ emailModalVisible: false })}
      className="changeEmailModal"
    >
      <Form
        name="changeEmailModal"
        form={form}
        labelCol={{
          span: 6
        }}
        wrapperCol={{
          span: 16
        }}
        initialValues={{}}
      >
        <Form.Item
          label={t('settign-XnayjZCqHuh2')}
          name="password"
          rules={[
            {
              required: true,
              message: t('settign-UVMGtbaXLrAB')
            }
          ]}
        >
          <Input.Password autoComplete="off" />
        </Form.Item>
        <Form.Item
          label={t('setting-uK6ZVJT0cwWz')}
          name="account"
          initialValue={info.email}
          rules={[
            {
              required: true,
              message: t('setting-OJhLunDYt2k3')
            },
            {
              pattern: /^([a-z0-9A-Z]+[-|.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\.)+[a-zA-Z]{2,}$/,
              message: t('setting-F4u8VaRSYSpf')
            }
          ]}
        >
          <Input autoComplete="off" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UpdateEmailModal;
