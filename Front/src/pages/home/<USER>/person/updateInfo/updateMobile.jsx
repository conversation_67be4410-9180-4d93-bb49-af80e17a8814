import { Button, Col, Form, Input, Modal, Row, Statistic, message } from 'antd';
import React, { useState } from 'react';
import accountService from 'service/accountService';
import { t } from '@/utils/translation';

const { Countdown } = Statistic;

const UpdateMobileModal = (props) => {
  const [form] = Form.useForm();
  const {
    state: { mobileModalVisible, info, loading },
    dispatch
  } = props;

  const [sending, setSending] = useState(false);

  // 调用父组件确认方法，完成后关闭弹窗
  const onOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        try {
          dispatch({ loading: true });
          // 首先调用验证密码是否正确，如果正确调用
          const isValid = await accountService.checkPassword({
            password: values.password
          });

          if (isValid) {
            await accountService.modifyAccount({
              type: 'MOBILE',
              account: values.account,
              code: values.code
            });
            message.success(t('setting-NklXc1KkxVCj'));
            // const info = await UserService.getCurrentUser();
            window.location.reload();
          } else {
            message.error(t('setting-jwFLZUOMJPNT'));
            dispatch({ loading: false });
          }

          // dispatch({ loading: false, nameModalVisible: false, info });
        } catch {
          dispatch({ loading: false });
        }
      })
      .catch((info) => {
        console.error('Validate Failed:', info);
      });
  };

  const handleValidator = async (rule, val) => {
    if (val) {
      const res = await accountService.ensureAccount({ mobile: val.trim() });
      if (res) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(rule.message));
    }
  };

  const getCaptcha = async () => {
    await form.validateFields(['account']);
    const account = form.getFieldValue('account');
    const res = await accountService.sendVerifyCode({ type: 'SMS', account });
    if (!res) {
      message.error(t('setting-PUAkggPcz52J'));
      return;
    }
    setSending(true);
    message.success(t('setting-ELofcBOMx9yL'));
  };

  return (
    <Modal
      confirmLoading={loading}
      title={t('setting-vrQgIsFW4GBt')}
      open={mobileModalVisible}
      onOk={onOk}
      onCancel={() => dispatch({ mobileModalVisible: false })}
      className="changeMobileModal"
    >
      <Form
        name="changeMobileModal"
        form={form}
        labelCol={{
          span: 6
        }}
        wrapperCol={{
          span: 16
        }}
        initialValues={{}}
      >
        <Form.Item
          label={t('setting-mero1tF7ujZu')}
          name="password"
          rules={[
            {
              required: true,
              message: t('setting-01EyFd13gnnc')
            }
          ]}
        >
          <Input.Password autoComplete="off" />
        </Form.Item>
        <Form.Item
          label={t('setting-067TOHookvlc')}
          name="account"
          initialValue={info.mobile}
          rules={[
            {
              required: true,
              message: t('setting-RhwGm3k1oJFx')
            },
            {
              pattern: /^(?:(?:\+|00)86)?1\d{10}$/,
              message: t('setting-v6bk17U3DnCq')
            },
            {
              validator: handleValidator,
              message: t('setting-MC5xIAu23RwH')
            }
          ]}
        >
          <Input autoComplete="off" />
        </Form.Item>
        <Form.Item label={t('setting-YnUtir02b6tO')}>
          <Row gutter={8}>
            <Col span={16} style={{ maxWidth: '65.8%' }}>
              <Form.Item
                name="code"
                noStyle
                rules={[
                  {
                    required: true,
                    message: t('setting-L1u95v3mQ4yf')
                  }
                ]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Button onClick={getCaptcha} disabled={sending} className="getCaptcha">
                {sending ? (
                  <Countdown
                    onFinish={() => setSending(false)}
                    valueStyle={{ fontSize: 14, color: 'rgba(0, 0, 0, 0.45)' }}
                    value={Date.now() + 1000 * 60}
                    suffix={t('setting-YRWhy6lJXmYv')}
                    format="ss"
                  />
                ) : (
                  <span>{t('setting-1R3x2tq0aQHa')}</span>
                )}
              </Button>
            </Col>
          </Row>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UpdateMobileModal;
