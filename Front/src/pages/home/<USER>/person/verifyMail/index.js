import React, { useState } from 'react';
import { Input, Form, Button, Divider, message, Row, Col, Statistic } from 'antd';
import { connect } from 'react-redux';
import { SafetyCertificateOutlined } from '@ant-design/icons';
import TextLogo from 'assets/images/<EMAIL>';
import AccountService from 'service/accountService';
import { transformUrl } from 'utils/universal';
import SystemInfo from 'components/bussinesscoms/systeminfocom/index';
import { useRouterState } from 'utils/customhooks';
import { t } from '@/utils/translation';
import './index.scss';

const mapState = (store) => {
  return { systemInfo: store.systemInfo };
};

const { Countdown } = Statistic;

const VerifyMail = (props) => {
  const [form] = Form.useForm();

  const [loading, setLoading] = useState(false);

  const [routerState = {}] = useRouterState();

  const [sending, setSending] = useState(true);

  const { systemInfo } = props;

  const onFinish = async (value) => {
    // const data = { ...value, ...state };
    try {
      setLoading(true);
      const isValidCode = await AccountService.modifyAccount({
        ...value,
        type: 'EMAIL',
        enable: true,
        account: routerState.account
      });
      if (isValidCode) {
        message.success(t('setting-IiPGQt8lucif'));
        props.history.replace({
          pathname: '/aimarketer/usercenter/succeed',
          state: {
            title: t('setting-MzyTMIWdTcLj'),
            backUrl: '/aimarketer/usercenter/setting/personSetting',
            backButtonText: t('setting-3AntknQrRkfJ')
          }
        });
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
    }
  };

  const getCaptcha = async () => {
    const res = await AccountService.sendVerifyCode({
      type: 'MAIL',
      account: routerState.account
    });
    if (!res) {
      message.error(t('setting-JRvMdAG25IF5'));
      return;
    }
    setSending(true);
    message.success(t('setting-ybnrJaRFQnI9'));
  };

  return (
    <div className="verifyMail">
      <div className="bindInfo">
        <div className="heade">
          <img src={transformUrl(systemInfo['page.light.logoUrl']) || TextLogo} alt="logo" />
        </div>
        <div className="main">
          <div className="emailTitle">{t('setting-FUiIrq3Umn5I')}</div>
          <div className="description">{t('setting-s8NuGir7F4mY', { account: routerState.account })}</div>
          <Form className="formStyle" onFinish={onFinish} form={form} layout="vertical">
            <Form.Item required label={t('setting-B2ImcaHNnoX7')}>
              <Row gutter={8}>
                <Col span={16}>
                  <Form.Item
                    name="code"
                    noStyle
                    validateTrigger="onBlur"
                    rules={[{ required: true, message: t('setting-qpCG0mUqWe96') }]}
                  >
                    <Input
                      autoComplete="off"
                      prefix={<SafetyCertificateOutlined />}
                      className="inputEmail"
                      size="large"
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Button onClick={getCaptcha} disabled={sending} className="getCaptcha">
                    {sending ? (
                      <Countdown
                        onFinish={() => setSending(false)}
                        valueStyle={{
                          fontSize: 14,
                          color: 'rgba(0, 0, 0, 0.45)'
                        }}
                        value={Date.now() + 1000 * 60}
                        suffix={t('setting-fldLJ3rhHKgu')}
                        format="ss"
                      />
                    ) : (
                      <span>{t('setting-oiXTdNe8G2fG')}</span>
                    )}
                  </Button>
                </Col>
              </Row>
            </Form.Item>
            <Button type="primary" disabled={loading} className="login-form-button" htmlType="submit">
              {t('setting-cbVsmpg3zLFK')}
            </Button>
          </Form>
        </div>
      </div>
      <Divider />
      <SystemInfo />
    </div>
  );
};
export default connect(mapState)(VerifyMail);
