import { Button, Divider, List, message, Table } from 'antd';
import SystemInfo from 'components/bussinesscoms/systeminfocom/index';
import dayjs from 'dayjs';
import React, { useEffect, useReducer } from 'react';
import { withRouter } from 'react-router-dom';
import accountService from 'service/accountService';
import loginRecordService from 'service/loginRecordService';
import _organizationManageService from 'service/organizationmanageService';
import UserServiceBefore from 'service/UserService';
import UpdateEmailModal from '../updateInfo/updateEmail';
import UpdateMobileModal from '../updateInfo/updateMobile';
import UpdateName from '../updateInfo/updateName';
import UpdatePassword from '../updateInfo/updatePassword';
import { t } from '@/utils/translation';
import './index.scss';

const UserService = new UserServiceBefore();
const reducer = (state, action) => ({ ...state, ...action });

const columns = [
  {
    title: t('setting-PWiJ02BiJWWD'),
    dataIndex: 'time',
    key: 'time',
    render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
  },
  {
    title: 'ip',
    dataIndex: 'ip',
    key: 'ip'
  },
  {
    title: t('setting-vsHPNb7TxxIs'),
    dataIndex: 'status',
    key: 'status',
    render: (text) => {
      if (text === 'SUC') {
        return (
          <div className="status">
            <div className="point" style={{ backgroundColor: '#52C41A' }} />
            <div>{t('setting-ILxDxXH5UQS0')}</div>
          </div>
        );
      } else {
        return (
          <div className="status">
            <div className="point" style={{ backgroundColor: '#FF4D4F' }} />
            <div>{t('setting-XyfnIK5ttDqe')}</div>
          </div>
        );
      }
    }
  }
];

const Person = (props) => {
  const [state, dispatch] = useReducer(reducer, {
    isPwdExisted: true,
    info: {},
    loading: false,
    nameModalVisible: false,
    nameValue: {},
    pwdModalVisible: false,
    emailModalVisible: false,
    mobileModalVisible: false,
    emailEditStatus: false,
    mobileEditStatus: false,
    loginHistory: []
  });
  const {
    info,
    nameModalVisible,
    pwdModalVisible,
    emailModalVisible,
    mobileModalVisible,
    loading,
    isPwdExisted,
    loginHistory,
    emailEditStatus,
    mobileEditStatus
  } = state;

  useEffect(() => {
    (async () => {
      try {
        dispatch({ loading: true });
        const res = await _organizationManageService.loginPasswordListby([
          { operator: 'EQ', propertyName: 'type', value: 'ACCOUNT_VERIFY' },
          { operator: 'IN', propertyName: 'code', value: 'account.mail.verify,account.mobile.verify' }
        ]);
        dispatch({
          loading: false,
          emailEditStatus: res.find((item) => item.code === 'account.mail.verify').value === 'ON',
          mobileEditStatus: res.find((item) => item.code === 'account.mobile.verify').value === 'ON'
        });
      } catch {
        dispatch({ loading: false });
      }
    })();
  }, []);

  // 判断是否是初始化密码
  useEffect(() => {
    (async () => {
      try {
        dispatch({ loading: true });
        const isPwdExisted = await UserService.isPwdExisted();
        dispatch({ isPwdExisted, loading: false });
      } catch {
        dispatch({ loading: false });
      }
    })();
  }, []);

  useEffect(() => {
    (async () => {
      try {
        dispatch({ loading: true });
        const _info = await UserService.getCurrentUser();
        _info.email = isEmaile(_info?.email) ? _info.email : '';
        _info.mobile = isPhone(_info?.mobile) ? _info.mobile : '';
        dispatch({ info: _info, loading: false });
      } catch {
        dispatch({ loading: false });
      }
    })();
  }, []);

  useEffect(() => {
    // 获取登录记录
    (async () => {
      try {
        dispatch({ loading: true });
        const reData = await loginRecordService.getLoginRecord({
          page: 1,
          size: 10,
          sorts: [{ direction: 'desc', propertyName: 'time' }]
        });
        dispatch({ loginHistory: reData.content, loading: false });
      } catch {
        dispatch({ loading: false });
      }
    })();
  }, []);

  const handleUpdateName = () => {
    dispatch({ nameValue: { newUsername: info.name }, nameModalVisible: true });
  };

  const handleUpdatePassword = () => {
    dispatch({ pwdModalVisible: true });
  };

  const handleUpdateEmail = () => {
    dispatch({ emailModalVisible: true });
  };
  const isEmaile = (e) => {
    const regEmail = /^([a-z0-9A-Z]+[-|.|_]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\.)+[a-zA-Z]{2,}$/;
    return regEmail.test(e);
  };
  const isPhone = (e) => {
    const regPhone = /^1[3-9]\d{9}$/;
    return regPhone.test(e);
  };
  const handleUpdateMobile = () => {
    dispatch({ mobileModalVisible: true });
  };

  // 验证邮箱
  const handleVerifyMail = async () => {
    // 首先调用发送验证码接口，返回数据之后跳转到新页面
    const res = await accountService.sendVerifyCode({
      account: info.email,
      type: 'MAIL'
    });
    if (!res) {
      message.error(t('setting-2270dW4DRj3m'));
      return;
    }
    message.success(t('setting-fRchdu3rvACw'));
    props.history.push({
      pathname: '/aimarketer/usercenter/verifyMail',
      state: { account: info.email }
    });
  };

  // const getPwdLevel = () => {
  //   if (info.pwdLevel === 'HIGH') {
  //     return <><span>***********</span><span className="high"><span className="highLevel">安全等级高</span></span></>;
  //   } else if (info.pwdLevel === 'MEDIUM') {
  //     return <><span>***********</span><span className="medium"><span className="highLevel">安全等级中</span></span></>;
  //   } else if (info.pwdLevel === 'LOW') {
  //     return <><span>***********</span><span className="low"><span className="highLevel">安全等级低</span></span></>;
  //   } else {
  //     return '***********';
  //   }
  // };

  return (
    <div className="personalSettingList">
      {/* <header>
        <h1>账号设置</h1>
      </header> */}
      <section className="projectList">
        <div className="projectListHeader">{t('setting-uH2zrzXfxTaj')}</div>
        <Divider />
        <List loading={loading}>
          <List.Item
            actions={[
              <Button onClick={handleUpdateName} type="primary">
                {t('setting-M4FuQs93bjZ7')}
              </Button>
            ]}
          >
            <List.Item.Meta title={t('setting-Y15I18L0MkQp')} description={info.name} />
          </List.Item>
          <List.Item
            actions={[
              <Button type="primary" disabled>
                {t('setting-M4FuQs93bjZ7')}
              </Button>
            ]}
          >
            <List.Item.Meta title={t('setting-An45O0AOJJBD')} description={info.jobNo} />
          </List.Item>
          <List.Item
            actions={
              localStorage.getItem('env') === 'NS'
                ? null
                : emailEditStatus && [
                    <Button onClick={handleUpdateEmail}>{t('setting-cH739OLbb6gF')}</Button>,
                    <Button onClick={handleVerifyMail} disabled={!isEmaile(info.email)} type="primary">
                      {t('setting-0r9o4P31dXR9')}
                    </Button>
                  ]
            }
          >
            <List.Item.Meta
              title={t('setting-XFwdm05zzYTj')}
              description={
                <>
                  <span>{isEmaile(info.email) ? info.email : '-'}</span>
                  {isEmaile(info.email) && (
                    <span className={info.emailValid ? 'verified' : 'unverified'}>
                      {info.emailValid ? t('setting-650KVDOS7wuA') : t('setting-3jbVAWvOQeSq')}
                    </span>
                  )}
                </>
              }
            />
          </List.Item>
          <List.Item
            actions={
              localStorage.getItem('env') === 'NS'
                ? null
                : mobileEditStatus && [
                    <Button onClick={handleUpdateMobile} type="primary">
                      {t('setting-dh6U7BH8Devk')}
                    </Button>
                  ]
            }
          >
            <List.Item.Meta title={t('setting-nK5YtBdkgu0v')} description={isPhone(info.mobile) ? info.mobile : '-'} />
          </List.Item>
          <List.Item
            actions={[
              <Button onClick={handleUpdatePassword} type="primary">
                {isPwdExisted ? t('setting-dh6U7BH8Devk') : t('setting-TBr81ER2EpvM')}
              </Button>
            ]}
          >
            <List.Item.Meta
              title={t('setting-zqtPlr4aq6dZ')}
              description={
                <>
                  <span>***********</span>
                  {localStorage.getItem('passwordTip') && (
                    <span className="unverified">{localStorage.getItem('passwordTip')}</span>
                  )}
                </>
              }
            />
          </List.Item>
          <List.Item>
            <List.Item.Meta
              title={t('setting-0CB77UdX3N69')}
              description={dayjs(info.createTime).format('YYYY-MM-DD HH:mm:ss')}
            />
          </List.Item>
        </List>
      </section>
      <section className="loginHistory">
        <div className="title">{t('setting-TU0ryqK4aJb8')}</div>
        <Table
          className="loginHistoryList"
          rowKey="id"
          columns={columns}
          dataSource={loginHistory}
          pagination={false}
        />
      </section>
      {nameModalVisible && <UpdateName state={state} dispatch={dispatch} />}
      {pwdModalVisible && <UpdatePassword state={state} dispatch={dispatch} />}
      {emailModalVisible && <UpdateEmailModal state={state} dispatch={dispatch} />}
      {mobileModalVisible && <UpdateMobileModal state={state} dispatch={dispatch} />}
      <SystemInfo />
    </div>
  );
};
export default withRouter(Person);
