import { Button, Form, Input, message, Modal, Select, Space, Table, Tooltip } from 'antd';
import TableSearch from 'components/bussinesscoms/tableSearch/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { t } from '@/utils/translation';
import MyToDoListService from 'service/myToDoListService';
import UserService from 'service/UserService';
import { elements, initHistoryParam, initParam } from '../config';

import myToDoListService from '../../../../service/myToDoListService';
import '../index.scss';

const userService = new UserService();

const { TextArea } = Input;

const pagination = {
  showTotal: (totals) => t('setting-QuVw2omuYIkL', { totals }),
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const hisToryPagination = {
  showTotal: (totals) => t('setting-QuVw2omuYIkL', { totals }),
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const statusList = {
  RUNNING: t('setting-gU5qEwJhl7n2'),
  FINISH: t('setting-9lgNjdtnk0E1'),
  REJECT: t('setting-CTSCJMH0wGYi'),
  BACKOUT: t('setting-kYjhgwaNgx1a'),
  CANCEL: t('setting-IV5TwpDxn4LT')
};
const typeList = {
  CAMPAIGN: t('setting-gx1muiY6Z4Gg'),
  SEGMENT: t('setting-XdQBqKGRRvsg'),
  MARKET_WORKS: t('setting-cCSTeEYAkhxo'),
  LABEL: t('setting-G0rT12SNqeam')
};
const detailUrlList = {
  CAMPAIGN: '/aimarketer/home/<USER>',
  SEGMENT: '/aimarketer/home/<USER>/userGroup'
};

export default function MyToDoListStart({ props, tabKey }) {
  const {
    location: { state }
  } = props;
  const [form] = Form.useForm();

  const [dataSource, setDataSource] = useState([]);
  const [historyTableSource, setHistoryTableSource] = useState([]);
  const [userId, setUserId] = useState(undefined);
  const [loading, setLoading] = useState(false);
  const [param, setParam] = useState(_.cloneDeep(state?.paramStart || initParam));
  const [historyParam, setHistoryParam] = useState(initHistoryParam);
  //   const [tabKey, setTabKey] = useState(1);
  const [, setApproveStatus] = useState(false);

  const [open, setOpen] = useState(false);
  const [logStatus, setLogStatus] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);

  const renderOptions = (record) => (
    <Space>
      <Button
        onClick={() => onColumnActionClick('back', record)}
        type="link"
        disabled={record.status !== 'RUNNING' || record.type === 'LABEL'}
      >
        {t('setting-8d5FgAyIaJ1f')}
      </Button>
      <Button onClick={() => onColumnActionClick('log', record)} type="link">
        {t('setting-JuInJ6EwSYXj')}
      </Button>
    </Space>
  );
  const columns = [
    {
      title: t('setting-n8t0Xou8ummI'),
      key: 'processNo',
      dataIndex: 'processNo',
      sorter: true,
      width: 150
    },
    {
      title: t('setting-eWAzU92rgk2o'),
      key: 'name',
      width: 200,
      dataIndex: 'name',
      render: (text, record) => (
        <Tooltip title={text}>
          <a onClick={() => onColumnActionClick('detail', record)}>{text}</a>
        </Tooltip>
      )
    },
    {
      title: t('setting-sKybqPej6fgD'),
      key: 'type',
      width: 150,
      dataIndex: 'type',
      render: (text) => <span>{typeList[text]}</span>
    },
    {
      title: t('setting-nJ3st9ldp8Cv'),
      key: 'createUserName',
      width: 150,
      dataIndex: 'createUserName'
    },
    {
      title: t('setting-QHJkuu9Un2QL'),
      width: 200,
      dataIndex: 'createTime',
      sorter: true,
      key: 'createTime',
      render: (text, record) => (record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : '-')
      //   render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: t('setting-ipEut3kWr2zN'),
      width: 100,
      key: 'status',
      render: (text) => <span>{statusList[text.status]}</span>
    },
    {
      title: t('setting-wKhqT9w6Gwph'),
      key: 'opinion',
      width: 200,
      render: (record) => (
        <Tooltip title={record.opinion ? record.opinion : ''}>
          <span className="optionWrap">{record.opinion ? record.opinion : '-'}</span>
        </Tooltip>
      )
    },
    {
      title: t('setting-XYytmk7vjoHD'),
      key: 'dealUserId ',
      width: 100,
      render: (record) => <span>{record.dealUserName ? record.dealUserName : '-'}</span>
    },
    {
      title: t('setting-K7S2UZ5vxPIm'),
      width: 200,
      dataIndex: 'dealTime',
      sorter: true,
      key: 'dealTime',
      render: (text, record) => (record.dealTime ? dayjs(record.dealTime).format('YYYY-MM-DD HH:mm:ss') : '-')
      //   render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: t('setting-AmLZgSI1m1WL'),
      width: 200,
      fixed: 'right',
      render: (text, record) => <Space>{renderOptions(record)}</Space>
    }
  ];

  const columnsHistory = [
    {
      title: t('setting-nJ3st9ldp8Cv'),
      key: 'createUserName',
      width: 150,
      dataIndex: 'createUserName',
      render: (text, record) => (
        <div>{(record.createTime && record.status === 'RUNNING') || record.status === 'BACKOUT' ? text : '-'}</div>
      )
    },
    {
      title: t('setting-QHJkuu9Un2QL'),
      width: 200,
      dataIndex: 'createTime',
      sorter: true,
      key: 'createTime',
      render: (text, record) =>
        (record.createTime && record.status === 'RUNNING') || record.status === 'BACKOUT'
          ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '-'
    },
    {
      title: t('setting-ipEut3kWr2zN'),
      width: 100,
      key: 'status',
      render: (text) => <span>{statusList[text.status]}</span>
    },
    {
      title: t('setting-XYytmk7vjoHD'),
      key: 'dealUserName ',
      width: 100,
      render: (record) => <span>{record.dealUserName ? record.dealUserName : '-'}</span>
    },
    {
      title: t('setting-wKhqT9w6Gwph'),
      key: 'opinion',
      width: 200,
      render: (record) => (
        <Tooltip title={record.opinion ? record.opinion : ''}>
          <span className="optionWrap">{record.opinion ? record.opinion : '-'}</span>
        </Tooltip>
      )
    },
    {
      title: t('setting-K7S2UZ5vxPIm'),
      width: 200,
      dataIndex: 'dealTime',
      sorter: true,
      key: 'dealTime',
      render: (text, record) => (record.dealTime ? dayjs(record.dealTime).format('YYYY-MM-DD HH:mm:ss') : '-')
    }
  ];

  useEffect(() => {
    const init = async () => {
      const { id } = await userService.getCurrentUser();
      const res = await myToDoListService.getProcessAuthority({
        type: 'CAMPAIGN'
      });
      setApproveStatus(res);
      setUserId(id);
    };
    init();
  }, []);

  const handleTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setParam({ ...param });
  };

  const handleHistoryTableChange = (lastpagination, filtersArg, sorter) => {
    historyParam.page = lastpagination.current;
    historyParam.size = lastpagination.pageSize;
    if (sorter.field) {
      historyParam.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setHistoryParam({ ...historyParam });
  };

  useEffect(() => {
    const getTableData = async () => {
      setLoading(true);
      if (userId) {
        const finalParam = _.cloneDeep(param);
        finalParam.search = [
          ...finalParam.search,
          {
            operator: 'EQ',
            propertyName: 'projectId',
            value: localStorage.getItem('projectId')
          },
          { operator: 'EQ', propertyName: 'createUserId', value: userId }
        ];

        const result = await MyToDoListService.query(finalParam);
        pagination.total = result.totalElements;
        pagination.current = param.page;
        pagination.pageSize = param.size;
        props.history.replace({
          state: { ...state, paramStart: param, tabKey }
        });
        setDataSource(result.content);
        setLoading(false);
      }
    };
    getTableData();
  }, [param, tabKey, userId]);

  useEffect(() => {
    const getHistoryTableData = async () => {
      setHistoryLoading(true);
      const finalParam = _.cloneDeep(historyParam);
      const result = await MyToDoListService.processHistory(finalParam);
      hisToryPagination.total = result.content.filter((item) => item.status !== 'DRAFT').totalElements;
      hisToryPagination.current = historyParam.page;
      hisToryPagination.pageSize = historyParam.size;
      setHistoryTableSource(result.content.filter((item) => item.status !== 'DRAFT'));
      setLoading(false);
      setHistoryLoading(false);
    };
    getHistoryTableData();
  }, [historyParam]);

  const showModal = () => {
    setOpen(true);
  };

  const handleCancel = () => {
    setOpen(false);
  };

  const showLogModal = async (id) => {
    setHistoryParam({
      ...historyParam,
      search: [{ operator: 'EQ', propertyName: 'instanceId', value: id }]
    });
    setLogStatus(true);
  };

  const hideLogModal = () => {
    setLogStatus(false);
  };

  const onSubmit = async () => {
    setConfirmLoading(true);
    const { id, name, opinion, status, businessId, definitionId, processNo, type } = form.getFieldValue();
    await myToDoListService.saveProcessInstance({
      businessId,
      id,
      name,
      dealUserId: userId,
      dealTime: dayjs().valueOf(),
      status,
      opinion,
      processNo,
      definitionId,
      projectId: localStorage.getItem('projectId'),
      type
    });

    message.success(t('setting-2qAgvETJSHms'));
    setConfirmLoading(false);
    handleCancel();
    setParam({ ...param });
  };

  const onColumnActionClick = async (key, record) => {
    const { id, businessId, definitionId, processNo, name, createTime, type, shareUrl } = record;
    if (key === 'back') {
      Modal.confirm({
        title: t('setting-8d5FgAyIaJ1f'),
        className: 'backWrap',
        content: <p className="backDesc">{t('setting-OQt7lDZghi4G', { name: record.name })}</p>,
        okText: t('setting-lOQLA6ZpUcWV'),
        okType: 'primary',
        cancelText: t('setting-hsgrItmXuWAe'),
        async onOk() {
          await myToDoListService.saveProcessInstance({
            businessId,
            createTime,
            id,
            name,
            definitionId,
            processNo,
            status: 'BACKOUT',
            projectId: localStorage.getItem('projectId'),
            type
          });
          message.success(t('setting-4zdEYQ56znDq'));
          setParam({ ...param });
        },
        onCancel() {}
      });
    } else if (key === 'detail') {
      if (type === 'MARKET_WORKS') {
        const result = await myToDoListService.getAuthorization({
          loginId: userId,
          projectId: localStorage.getItem('projectId')
        });

        const newUrl =
          shareUrl.indexOf('?') >= 0 ? `${shareUrl}&Authorization=${result}` : `${shareUrl}?Authorization=${result}`;
        window.open(newUrl, '_blank');
      } else if (type === 'LABEL') {
        window.open(shareUrl, '_self');
      } else {
        props.history.push(`${detailUrlList[type]}/detail?id=${businessId}&definition=${true}`);
      }
    } else if (key === 'finish') {
      form.setFieldsValue({
        name: record.name,
        id: record.id,
        businessId,
        definitionId: record.definitionId,
        processNo: record.processNo,
        status: 'FINISH',
        opinion: null,
        type
      });
      showModal();
    } else if (key === 'reject') {
      form.setFieldsValue({
        name: record.name,
        businessId,
        id: record.id,
        definitionId: record.definitionId,
        processNo: record.processNo,
        status: 'REJECT',
        opinion: null,
        type
      });
      showModal();
    } else if (key === 'log') {
      showLogModal(record.id);
    }
  };
  return (
    <div className="todoList">
      {/* <header>
        <h1>待办事项</h1>
      </header> */}
      {/* <p className="rule">
        <span className={tabKey === 1 ? 'selectSpan' : ''} onClick={() => { setTabKey(1); }} style={{ marginRight: 20 }}>我发起的</span>
        {
          approveStatus ? <span className={tabKey === 2 ? 'selectSpan' : ''} onClick={() => { setTabKey(2); }} style={{ marginRight: 20 }}>待审批的</span> : null
        }

        <span className={tabKey === 3 ? 'selectSpan' : ''} onClick={() => { setTabKey(3); }} style={{ marginRight: 20 }}>已审批的</span>
      </p> */}
      <div className="search">
        <TableSearch
          elements={elements({})}
          onChange={(data) => setParam({ ...param, ...data })}
          span={8}
          initialValues={state?.paramStart?.search}
        />
      </div>
      <div className="content">
        <div className="tableWrap">
          <Table
            dataSource={dataSource}
            columns={columns}
            loading={loading}
            onChange={handleTableChange}
            rowKey="id"
            pagination={pagination}
            scroll={{ x: 1300 }}
          />
        </div>

        <Modal
          title={t('setting-k3E95YSVUzmA')}
          className="processHistoryModalWrap"
          open={logStatus}
          destroyOnClose
          // okText="确认"
          // onOk={hideLogModal}
          confirmLoading={historyLoading}
          onCancel={hideLogModal}
          footer={null}
        >
          <div>
            <Table
              dataSource={historyTableSource}
              pagination={hisToryPagination}
              columns={columnsHistory}
              loading={historyLoading}
              onChange={handleHistoryTableChange}
              scroll={{ x: 400 }}
              rowKey="id"
            />
          </div>
        </Modal>

        <Modal
          title={t('setting-7PmTMs6VX3KP')}
          className="processModalWrap"
          open={open}
          destroyOnClose
          okText={t('setting-zEHn5erM6FA2')}
          onOk={() => onSubmit()}
          confirmLoading={confirmLoading}
          onCancel={handleCancel}
        >
          <div className="processForm">
            <Form
              name="basic"
              layout="vertical"
              form={form}
              colon={false}
              confirmLoading={confirmLoading}
              initialValues={{
                name: null,
                id: null,
                opinion: null,
                status: null
              }}
            >
              <Form.Item label={t('setting-eWAzU92rgk2o')} name="name">
                <Select disabled />
              </Form.Item>

              <Form.Item
                label={t('setting-wKhqT9w6Gwph')}
                name="opinion"
                rules={[{ max: 100, message: t('setting-vtWMPjEvQlUC') }]}
              >
                <TextArea
                  placeholder={t('setting-pBCbUmdgHmgo')}
                  autoSize={{
                    minRows: 2,
                    maxRows: 6
                  }}
                />
              </Form.Item>
            </Form>
          </div>
        </Modal>
      </div>
    </div>
  );
}
