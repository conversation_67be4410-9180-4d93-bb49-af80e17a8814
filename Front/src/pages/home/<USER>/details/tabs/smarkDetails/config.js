import React from 'react';
import dayjs from 'dayjs';
import { Tag } from 'antd';
import { t } from 'utils/translation';

export default {
  // query表单配置
  elements: {
    ID: {
      type: 'input',
      label: t('operationCenter-7WIigVOEnNrN'),
      operator: 'LIKE',
      width: 8,
      componentOptions: {
        allowClear: true,
        placeholder: t('operationCenter-wAqR9c5YjsfL')
      }
    },
    taskId: {
      type: 'input',
      label: t('operationCenter-wbhRtyrdO6NU'),
      operator: 'EQ',
      width: 8,
      componentOptions: {
        allowClear: true,
        placeholder: t('operationCenter-xvl9qpzAACfK')
      }
    },
    taskName: {
      type: 'input',
      label: t('operationCenter-DDOgobJifANR'),
      operator: 'EQ',
      width: 8,
      componentOptions: {
        allowClear: true,
        placeholder: t('operationCenter-uLJTSOl0LdKN')
      }
    },
    userId: {
      type: 'input',
      label: t('operationCenter-LyesYaCKdSoG'),
      operator: 'EQ',
      width: 8,
      componentOptions: {
        allowClear: true,
        placeholder: t('operationCenter-4tRu0gfyWbSE')
      }
    }
    // userPhone: {
    //   type: 'input',
    //   label: '用户手机号',
    //   operator: 'EQ',
    //   width: 8,
    //   componentOptions: {
    //     allowClear: true,
    //     placeholder: '请输入用户手机号'
    //   }
    // }
    // count: {
    //   type: 'selectInput',
    //   label: '总点击次数',
    //   operator: 'EQ',
    //   width: 8,
    //   componentOptions: {
    //     allowClear: true,
    //     placeholder: '请选择总点击次数',
    //     noStyle: true,
    //     arrName: 'count'
    //     // filterOption: (input, option) => {
    //     //   return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    //     // },
    //     // options: []
    //   }
    // }
  },
  // 表单数据
  formData: {},
  columns: [
    {
      title: t('operationCenter-Evp91GWKL10M'),
      dataIndex: 'userStatus',
      width: 120,
      render: (text) => {
        return <Tag color={text === 'ENABLE' ? 'green' : 'red'}>{text === 'ENABLE' ? t('operationCenter-rdHPrMAxYhTV') : t('operationCenter-L9VGqMKgNF0Z')}</Tag>;
      }
    },
    {
      title: t('operationCenter-uCKfh8pKxI6D'),
      dataIndex: 'scenario_code',
      width: 160
    },
    {
      title: t('operationCenter-wbhRtyrdO6NU'),
      dataIndex: 'campaign_id',
      width: 160
    },
    {
      title: t('operationCenter-DDOgobJifANR'),
      dataIndex: 'campaign_name',
      width: 320
    },
    {
      title: t('operationCenter-LyesYaCKdSoG'),
      dataIndex: 'dt_id',
      width: 240,
      render: (text, val) => {
        return <span>{val.userStatus === 'DISABLE' ? '-' : text}</span>;
      }
    },
    // {
    //   title: '用户手机号',
    //   dataIndex: 'mobile',
    //   width: 240,
    //   render: (text, val) => {
    //     return (
    //       <span>{val.userStatus === 'DISABLE' ? '-' : text}</span>
    //     );
    //   }
    // },
    {
      title: t('operationCenter-sUZ9nxImvYz9'),
      dataIndex: 'clickCount',
      width: 142
    }
  ],
  clickOnTime: [
    {
      title: t('operationCenter-Zl9QHugZlWUe'),
      dataIndex: 'id',
      width: 100
    },
    {
      title: t('operationCenter-A8vqHzUH2eEn'),
      dataIndex: 'clickTime',
      width: 332,
      // defaultSortOrder: 'descend',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    }
  ]
};
