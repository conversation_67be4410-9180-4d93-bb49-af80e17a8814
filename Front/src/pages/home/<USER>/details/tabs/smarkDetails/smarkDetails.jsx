import CheckAuth from '@/utils/checkAuth';
import { InfoCircleOutlined, LinkOutlined } from '@ant-design/icons';
import { useMount, useRequest } from '@umijs/hooks';
import { Button, Form, Modal, Radio, Table, Tooltip, message } from 'antd';
import FormCom from 'components/featurecoms/formcom/index';
import CustomRangePicker from 'components/featurecoms/rangepicker/index';
import ColumnActionCom from 'components/featurecoms/tableactioncom/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import SmartUrlService from 'service/SmartUrlService';
import UserGroupService from 'service/UserGroupService';
import UserService from 'service/UserService';
import CampaignOverviewService from 'service/campaignOverviewService';
import Log from 'utils/log';
import ClickTime from './clickTime';
import config from './config';
import creatConfig from './createConfig';
import QueryForList from './queryForList';
import './smarkDetails.scss';
import { t } from 'utils/translation';

const campaignOverviewService = new CampaignOverviewService();
const smartUrlService = new SmartUrlService();
const userGroupService = new UserGroupService();
const userService = new UserService();

const log = Log.getLogger('UserManage');

const { confirm } = Modal;

function Example(props) {
  const { smarkData } = props;
  const [search, setSearch] = useState([]);
  const elements = config.elements;
  const [columns, setColumns] = useState(config.columns);
  const [createElements, setCreateElements] = useState(creatConfig.elements);
  const [show, setShow] = useState(false);
  const [details, setDetails] = useState(false);
  const formNode = useRef(null);
  const [form] = Form.useForm();
  const [scenarioList, setScenarioList] = useState([]);
  const [resultData, setResultData] = useState([]);
  const [userList, setUserList] = useState([]);
  const [record, setRecord] = useState(null);
  // 定义一个key  保证每次打开modal重新请求接口
  const [modalKey, setModalKey] = useState(0);
  const [loading, setLoading] = useState(false);
  const [validDateType, setValidDateType] = useState('FOREVER');
  const [date, setDate] = useState({
    validBeginTime: new Date().getTime(),
    validEndTime: null
  });

  const getTableList = async (params) => {
    const { page, size } = params;
    const data = await smartUrlService.getClickDetail({
      page,
      size,
      taskId: smarkData.id,
      typeCode: 'CAMPAIGN',
      filters: search
    });
    _.map(data.data, (item) => {
      item.dt_id = item.var0;
      item.campaign_id = item.var1;
      item.scenario_code = item.var4;
      item.campaign_name = item.var5;
      item.mobile = item.var6;
    });
    setUserList(data);
    return {
      total: data.total,
      list: data.data
    };
  };

  useMount(() => {
    // 定义在里面，防止每次渲染都定义
    async function init() {
      localStorage.removeItem('clickTime');
      log.debug('useMount', elements);
      const data = [
        {
          operator: 'EQ',
          propertyName: 'projectId',
          value: localStorage.getItem('projectId')
        }
      ];
      const resultData = await campaignOverviewService.get(data); // 当前projectId
      setResultData(resultData);
      const _createElements = _.cloneDeep(createElements);
      const arr = _.map(resultData, (item) => ({
        key: item.code,
        value: item.code,
        text: `${item.name}[${item.code}]`
      }));
      _createElements.scenarioId.componentOptions.options = arr;
      setCreateElements(_createElements);
      setScenarioList(arr);
    }
    init();
  });

  const saveRecordInfo = async (id) => {
    const userInfo = await userService.getCurrentUser();
    await userGroupService.saveUserOperationRecord({
      targetId: id,
      targetType: 'SEGMENT',
      type: 'RECENT',
      createUserId: userInfo.id,
      updateUserId: userInfo.id,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
  };

  // 表格操作列点击处理
  const onColumnActionClick = async (key, feature, record) => {
    if (key === 'details') {
      setDetails(true);
      setRecord(record);
    }
  };

  // 表格右侧的更多
  const actionColumn = {
    title: t('operationCenter-xqjLQJ9wQY6s'),
    className: 'td-set',
    width: 122,
    fixed: 'right',
    render: (text, record) => {
      let featureData = {};
      if (record.userStatus === 'ENABLE') {
        featureData = { details: { text: t('operationCenter-BfUnUEMVl1w9') } };
      } else {
        featureData = { disable: { text: t('operationCenter-BfUnUEMVl1w9') } };
      }
      return <ColumnActionCom featureData={featureData} record={record} onClick={onColumnActionClick} />;
    }
  };

  // 增加操作列
  useEffect(() => {
    const _columns = _.cloneDeep(columns);
    _columns.push(actionColumn);
    setColumns(_columns);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 使用封装的分页逻辑，并组装参数
  // eslint-disable-next-line no-unused-vars
  const { tableProps, params, refresh } = useRequest(
    ({ current, pageSize, sorter: s, filters: f }) => {
      const p = { page: current, size: pageSize };
      if (s?.field && s?.order) {
        p.sorts = [
          {
            direction: _.includes(s?.order, 'desc') ? 'desc' : 'asc',
            propertyName: Array.isArray(s?.field) ? s?.field.join('.') : s?.field || 'updateTime'
          }
        ];
      } else if (!s) {
        p.sorts = [
          {
            direction: 'desc',
            propertyName: 'updateTime'
          }
        ];
      }
      if (f) {
        Object.entries(f).forEach(([filed, value]) => {
          p[filed] = value;
        });
      }
      return getTableList({ ...p, search });
    },
    {
      paginated: true,
      defaultPageSize: 10,
      refreshDeps: [search]
    }
  );

  const createUserGroup = () => {
    formNode.current.validateFields(async (err, value) => {
      try {
        const { scenarioId } = value;
        const formValue = await form.validateFields();
        if (!scenarioId) {
          return message.error(t('operationCenter-I7qT1gdaYPcA'));
        }
        formNode.current.resetFields();
        formNode.current.setFieldsValue({
          scenarioId: formValue.scenario_code
        });
        setShow(true);
      } catch (err) {
        return message.error(t('operationCenter-I7qT1gdaYPcA'));
      }
    });
  };

  const showPromiseConfirm = (data) => {
    confirm({
      title: t('operationCenter-OEMlzzLmClY1'),
      className: 'processSaveModal',
      icon: <LinkOutlined />,
      okText: t('operationCenter-8z9Gh94pvjQ0'),
      cancelText: t('operationCenter-iWIMAOz5dLY5'),
      async onOk() {
        const userInfo = await userService.getCurrentUser();

        const processRes = await userGroupService.approvalProcessInfo({
          contentId: data.id,
          contentName: data.name,
          contentType: 'SEGMENT',
          promoterId: userInfo.id,
          projectId: localStorage.getItem('projectId')
        });

        const saveParams = {
          id: data.id,
          approvalNo: processRes.approvalNo,
          approvalStatus: 'RUNNING'
        };

        await userGroupService.updateApprovalNoAndStatus(saveParams);

        message.success(t('operationCenter-qEjNfiQlzRw3'));
        // history.push('/aimarketer/home/<USER>/userGroup');
      }
    });
  };

  const saveUserGroup = async () => {
    const value1 = form.getFieldsValue();
    const value2 = await formNode.current.validateFields();
    const processAuth = await userGroupService.getProcessByType({
      type: 'SEGMENT',
      projectId: localStorage.getItem('projectId'),
      status: 'ENABLE'
    });
    setLoading(true);
    if (!value1.scenario_code) {
      setLoading(false);
      return message.error(t('operationCenter-i0GUVrxoiVJX'));
    } else {
      if (validDateType === 'TEMPORARY') {
        if (!date.validBeginTime || !date.validEndTime) {
          setLoading(false);
          return message.error(t('operationCenter-DDJcWmgWlcgC'));
        }
      }

      const data = [];
      const _value1 = _.cloneDeep(value1);
      const scenario = _.find(resultData, (item) => item.code === value2.scenarioId);
      const count = {
        column: 'dt_id',
        function: 'COUNT',
        operator: value1.conditions,
        value: value1.count
      };
      delete value1.conditions;
      delete value1.count;
      _.map(value1, (item, index) => {
        if (item) {
          data.push({ column: index, operator: 'EQ', value: item });
        }
      });

      let params = {
        filters: [...data],
        ...value2,
        typeCode: 'CAMPAIGN',
        approvalStatus: processAuth && processAuth.status === 'ENABLE' ? 'PENDING' : undefined,
        taskId: smarkData.id,
        scenarioCode: scenario.code || null,
        scenarioId: scenario.id || null,
        validDateType,
        validBeginTime: date.validBeginTime && new Date().getTime(),
        validEndTime: date.validEndTime && date.validEndTime.valueOf(),
        deptId: window.getDeptId()
      };
      if (params.validDateType === 'FOREVER') params = _.omit(params, ['validBeginTime', 'validEndTime']);
      if (_value1.conditions && _value1.count) params.filters = [...data, count];

      const result = await smartUrlService.saveAsSegment(params);

      if (result.header.message) {
        message.error(result.header.message);
      } else {
        saveRecordInfo(result.body.id);
        if (processAuth && processAuth.status === 'ENABLE') {
          showPromiseConfirm(result.body);
        }
        message.success(t('operationCenter-pMdqhAfyCdpT'));
      }

      // 清空两个form disable重置
      setDate({
        validBeginTime: new Date().getTime(),
        validEndTime: null
      });
      formNode.current.resetFields();
      form.resetFields();
      setSearch([]);
      const _createElements = _.cloneDeep(createElements);
      _createElements.scenarioId.componentOptions.disabled = false;
      setCreateElements(_createElements);
      setShow(false);
      setLoading(false);
    }
  };

  const changeRangePicker = (dates) => {
    setDate({
      validBeginTime: dates[0],
      validEndTime: dates[1]
    });
  };

  const count = form.getFieldsValue()?.count;

  return (
    <div className="accessList">
      {smarkData?.isDatatistId === 'ENABLE' && (
        <QueryForList
          formNode={formNode}
          setSearch={setSearch}
          createElements={createElements}
          setCreateElements={setCreateElements}
          scenarioList={scenarioList}
          refresh={refresh}
          form={form}
        />
      )}
      <main>
        <div className="Board">
          <div className="title">
            <span className="left">
              {t('operationCenter-cPkzi1oUSP1e')}
              <span className="content">{t('operationCenter-cIXrsayUlUb5')}{userList.accessUserTotal}</span>
              <span className="content" hidden={count}>
                {t('operationCenter-UfYGOlnsjJAc')}{userList.accessClickCountTotal}
              </span>
            </span>

            {smarkData?.isDatatistId === 'ENABLE' && (
              <CheckAuth code="aim_segment_edit">
                <Button className="creat" onClick={createUserGroup} type="primary">
                  <Tooltip placement="left" title={t('operationCenter-qsLlZuGhPVo4')}>
                    <InfoCircleOutlined /> {t('operationCenter-uT4RkPUghtRx')}
                  </Tooltip>
                </Button>
              </CheckAuth>
            )}
          </div>
          <Table
            columns={columns}
            rowKey="id"
            {...tableProps}
            pagination={{
              ...tableProps.pagination,
              showQuickJumper: false,
              showSizeChanger: true,
              showLessItems: true,
              pageSizeOptions: ['10', '20', '50']
            }}
            scroll={{ x: 1300 }}
          />
        </div>
      </main>
      <Modal
        title={t('operationCenter-uT4RkPUghtRx')}
        width={800}
        open={show}
        destroyOnClose
        getContainer={document.getElementsByClassName('accessList')[0]}
        forceRender
        footer={[
          <Button
            key="back"
            onClick={() => {
              setDate({
                validBeginTime: new Date().getTime(),
                validEndTime: null
              });
              setShow(false);
              formNode.current.setFieldsValue({ name: '', memo: '' });
            }}
          >
            {t('operationCenter-OCaG7mA7BQRH')}
          </Button>,
          <Button key="submit" type="primary" loading={loading} onClick={saveUserGroup}>
            {t('operationCenter-FOBUnmAs201x')}
          </Button>
        ]}
        onCancel={() => {
          setDate({
            validBeginTime: new Date().getTime(),
            validEndTime: null
          });
          setShow(false);
          formNode.current.setFieldsValue({ name: '', memo: '' });
        }}
        onOk={saveUserGroup}
      >
        <FormCom colon elements={createElements} ref={formNode} />
        <div className="analysisGroupType">
          <span>{t('operationCenter-1pmCxLn7u4lx')}</span>
          <span>
            <Radio.Group onChange={(e) => setValidDateType(e.target.value)} defaultValue="FOREVER">
              <Radio value="FOREVER">{t('operationCenter-Rnz4UcsIyt7B')}</Radio>
              <Radio value="TEMPORARY">{t('operationCenter-rfYjLiOeSb9J')}</Radio>
            </Radio.Group>
          </span>
          {validDateType === 'TEMPORARY' && (
            <CustomRangePicker
              limitTime
              className="userGroupPicker"
              value={[
                date.validBeginTime ? dayjs(date.validBeginTime) : null,
                date.validEndTime ? dayjs(date.validEndTime) : null
              ]}
              disableStartTime
              onChange={changeRangePicker}
              showTime={false}
              format="YYYY-MM-DD"
              style={{ marginTop: 12, marginLeft: 70 }}
              timeDiff={1}
              closeBefore
            />
          )}
        </div>
      </Modal>

      <Modal
        width={700}
        open={details}
        title={t('operationCenter-5FVmOxJni1V4')}
        onCancel={() => {
          setDetails(false);
          setModalKey(modalKey + 1);
        }}
        footer={null}
        key={modalKey}
      >
        <ClickTime record={record} />
      </Modal>
    </div>
  );
}
export default Example;
