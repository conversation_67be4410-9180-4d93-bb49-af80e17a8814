import CampaignV2Service from 'service/CampaignV2Service';
import { t } from 'utils/translation';

const campaignV2Service = new CampaignV2Service();

const handleValidator = async (rule, val) => {
  if (val) {
    const res = await campaignV2Service.ensureUnique({ name: val });
    if (!res) {
      return Promise.reject(new Error(rule.message));
    }
  }
  return Promise.resolve();
};

export default {
  // 表单配置
  elements: {
    scenarioId: {
      type: 'select',
      label: t('operationCenter-l3GDMTzkeRlQ'),
      operator: 'EQ',
      width: 20,
      labelWidth: 6,
      rules: [{ required: true, message: t('operationCenter-8AyTwTdo7d2G') }],
      componentOptions: {
        disabled: false,
        allowClear: true,
        showSearch: true,
        placeholder: t('operationCenter-4g2G4WYPxkGq'),
        filterOption: (input, option) => {
          return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        options: []
      }
    },
    name: {
      type: 'input',
      label: t('operationCenter-9okElrtdGmRT'),
      width: 20,
      labelWidth: 6,
      // wrapperWidth: 14,
      rules: [
        { required: true, message: t('operationCenter-GvG0K5IVAxDd') },
        { max: 60, message: t('operationCenter-V9KzCzGIrtla') },
        {
          pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
          message: t('operationCenter-51nQCCfr03pW')
        },
        { validator: handleValidator, message: t('operationCenter-BNumF8GGE4Xb') }
      ],
      componentOptions: {
        allowClear: true,
        // showSearch: true,
        placeholder: t('operationCenter-GvG0K5IVAxDd')
      }
    },
    memo: {
      type: 'textArea',
      label: t('operationCenter-W4ddYUuiEwZW'),
      operator: 'LIKE',
      width: 20,
      // wrapperWidth: 12,
      labelWidth: 6,
      rules: [{ max: 150, message: t('operationCenter-i53ixecFZaXb') }],
      componentOptions: {
        allowClear: true,
        placeholder: t('operationCenter-c5Z2Nrc1yNO1')
      }
    }
  },
  // 表单数据
  formData: {}
};
