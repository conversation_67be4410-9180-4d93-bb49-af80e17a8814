import { Button, Col, DatePicker, Form, Input, InputNumber, message, Row, Select } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useCallback, useEffect } from 'react';
import './smarkDetails.scss';
import { t } from 'utils/translation';

const { RangePicker } = DatePicker;
const { Option } = Select;

const initLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 17 }
};

/**
 * @description 组装好点击时间的查询条件
 * @param {Array} data
 * @returns {Object}
 */
const getClickTimeParams = (data) => {
  return {
    column: 'clickTime',
    operator: 'DATE_BETWEEN',
    value: data.map((time) => dayjs(time).valueOf().toString()).join(',')
  };
};

function Example(props) {
  const { formNode, setSearch, createElements, setCreateElements, scenarioList, form } = props;

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const queryData = useCallback((data) => setSearch([...data]), []);

  useEffect(() => {
    return () => {
      localStorage.removeItem('clickTime');
    };
  }, []);

  const query = () => {
    localStorage.removeItem('clickTime');
    // 设置创建用户分群默认值
    const value = form.getFieldsValue();
    formNode.current.setFieldsValue({ scenarioId: value.scenario_code });
    const _createElements = _.cloneDeep(createElements);
    _createElements.scenarioId.componentOptions.disabled = !!value.scenario_code;
    setCreateElements(_createElements);

    // 设置查询条件  删除没有用到的查询条件
    const data = [];
    const clickCount = {
      column: 'dt_id',
      function: 'COUNT',
      operator: value.conditions || '',
      value: value.count || ''
    };
    const _value = _.cloneDeep(value);
    if (value.conditions && !value.count) {
      return message.error(t('operationCenter-BaSaTql61FeJ'));
    } else if (!value.conditions && value.count) {
      return message.error(t('operationCenter-BaSaTql61FeJ'));
    }
    delete value.conditions;
    delete value.count;
    _.map(value, (item, index) => {
      if (item && index === 'clickTime') {
        data.push(getClickTimeParams(item));
        localStorage.setItem('clickTime', JSON.stringify(getClickTimeParams(item)));
      } else if (item) {
        data.push({ column: index, operator: 'EQ', value: item || '' });
      }
    });
    if (_value.conditions && _value.count) {
      queryData([...data, clickCount]);
    } else {
      queryData([...data]);
    }
  };

  return (
    <div className="query_for_list">
      <Form {...initLayout} form={form}>
        <Row>
          <Col span={8}>
            <Form.Item
              name="scenario_code"
              label={t('operationCenter-l3GDMTzkeRlQ')}
              rules={[{ required: true, message: t('operationCenter-8AyTwTdo7d2G') }]}
            >
              <Select placeholder={t('operationCenter-4g2G4WYPxkGq')} allowClear showSearch optionFilterProp="children">
                {_.map(scenarioList, (item) => {
                  return (
                    <Option key={item.key} value={item.key}>
                      {item.text}
                    </Option>
                  );
                })}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="dt_id" label={t('operationCenter-OjDwgSODU4rb')}>
              <Input allowClear placeholder={t('operationCenter-BteObYfmcmRK')} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="campaign_id" label={t('operationCenter-mq3aXd7XN9h3')}>
              <Input allowClear placeholder={t('operationCenter-BteObYfmcmRK')} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="campaign_name" label={t('operationCenter-LV7RpHMVHo1M')}>
              <Input allowClear placeholder={t('operationCenter-BteObYfmcmRK')} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              // name="count"
              label={t('operationCenter-6NIahwVO8lLf')}
            >
              <Input.Group compact>
                <Form.Item name="conditions" noStyle>
                  <Select allowClear style={{ width: '45%', marginRight: '8px' }} placeholder={t('operationCenter-4g2G4WYPxkGq')}>
                    <Option value="EQ">{t('operationCenter-NQhJ2nXzwSc7')}</Option>
                    <Option value="GT">{t('operationCenter-WIcD9OapbqES')}</Option>
                    <Option value="LT">{t('operationCenter-Bog2u3X3HT74')}</Option>
                    <Option value="GTE">{t('operationCenter-suX10BZTXYgx')}</Option>
                    <Option value="LTE">{t('operationCenter-azqZYJlsakg2')}</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="count" noStyle>
                  <InputNumber allowClear placeholder={t('operationCenter-BteObYfmcmRK')} controls="false" min={0} style={{ width: '50%' }} />
                </Form.Item>
              </Input.Group>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name="clickTime" label={t('operationCenter-KLY5nJbBEynH')}>
              <RangePicker allowClear style={{ width: '100%' }} format="YYYY-MM-DD HH:mm:ss" showTime />
            </Form.Item>
          </Col>
          <Col span={24}>
            <div className="buttons">
              <Button
                className="reset"
                onClick={() => {
                  form.resetFields();
                  query();
                  localStorage.removeItem('clickTime');
                }}
              >
                {t('operationCenter-AHGdZYcR9p9I')}
              </Button>
              <Button type="primary" onClick={query}>
                {t('operationCenter-PfK79lsGtkke')}
              </Button>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
}
export default Example;
