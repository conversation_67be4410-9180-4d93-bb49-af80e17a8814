import { InfoCircleOutlined } from '@ant-design/icons';
import { Descriptions, Spin, Tag, Tooltip, Typography, message } from 'antd';
import copy from 'copy-to-clipboard';
import React, { useEffect, useState } from 'react';
import { t } from 'utils/translation';

const { Text } = Typography;

const Details = (props) => {
  const { data, count } = props;
  const [isCustomParamsRule, setIsCustomParamsRule] = useState(false);
  const [isUtmRule, setIsUtmRule] = useState(false);

  const [customParams, setCustomParams] = useState([]);
  const [utmParams, setUtmParams] = useState({});

  useEffect(() => {
    if (data && data.targetUrl) {
      // 处理自定义参数
      const matches = data.targetUrl.match(/\{([^}]+)\}/g);
      if (matches) {
        setIsCustomParamsRule(true);
        const params = matches.map((match) => match.slice(1, -1));
        setCustomParams(params.filter((item) => item !== 'datatistid'));
      }

      // 处理UTM参数
      const utmParams = {};
      const utmFields = ['utmSource', 'utmMedium', 'utmCampaign', 'utmContent', 'utmTerm'];
      let hasUtm = false;

      utmFields.forEach((field) => {
        const regex = new RegExp(`${field}=([^&]*)`, 'i');
        const match = data.targetUrl.match(regex);
        if (match) {
          hasUtm = true;
          utmParams[field] = match[1];
        }
      });

      if (hasUtm) {
        setIsUtmRule(true);
        setUtmParams(utmParams);
      }
    }
  }, [data]);

  const indexEnum = { 1: t('operationCenter-vKE84c4CyUAK'), 2: t('operationCenter-ROJ2wnoD2ALs'), 3: t('operationCenter-ORM9mRHMvKKg'), 4: t('operationCenter-COoLswOEvwRy'), 5: t('operationCenter-5PUeEAB3YAPg') };

  return (
    <Spin spinning={!data}>
      <div className="basicContentWrap">
        <div className="shortInfo">
          <div className="preview">
            <Descriptions column={1}>
              <Descriptions.Item label={t('operationCenter-84ktTmyD1kmi')} style={{ color: 'rgba(0, 0, 0, 0.65)' }}>
                <Text style={{ width: 240 }} ellipsis={{ tooltip: data?.targetUrl }}>
                  {data?.targetUrl}
                </Text>
                {data?.targetUrl && (
                  <span
                    className="copy"
                    onClick={() => {
                      copy(data?.targetUrl);
                      message.success(t('operationCenter-k6U9NdanczGy'));
                    }}
                  >
                    {t('operationCenter-n7JqbmcaOXlj')}
                  </span>
                )}{' '}
              </Descriptions.Item>
            </Descriptions>
            <Descriptions column={1}>
              <Descriptions.Item label={t('operationCenter-LfuR4TWG18bF')} style={{ paddingBottom: '0' }}>
                {data?.shortLinkPreview}{' '}
                {data?.shortLinkPreview && (
                  <span
                    className="copy"
                    onClick={() => {
                      copy(data?.shortLinkPreview);
                      message.success(t('operationCenter-k6U9NdanczGy'));
                    }}
                  >
                    {t('operationCenter-n7JqbmcaOXlj')}
                  </span>
                )}
              </Descriptions.Item>
            </Descriptions>
          </div>
          <div className="title">{t('operationCenter-R5qHYOb6VOvm')}</div>
          <div className="getMessage">
            <Descriptions column={3}>
              {/* <Descriptions.Item label="已调用次数"><span className="span">-</span></Descriptions.Item> */}
              <Descriptions.Item label={t('operationCenter-BLuFVam3d0pB')} style={{ paddingBottom: '0' }}>
                <span className="span">{count}</span>
              </Descriptions.Item>
            </Descriptions>
          </div>
          <div className="title">{t('operationCenter-yJJLEc8CvDvp')}</div>
          <div className="getSmartUrlMessage">
            <Descriptions column={3} title={t('operationCenter-3iOcydm6c2MI')}>
              <Descriptions.Item label={t('operationCenter-vMOpfdW7OJZY')}>
                <Tag className="on" color={data?.isDatatistId === 'ENABLE' ? 'success' : 'error'}>
                  {data?.isDatatistId === 'ENABLE' ? t('operationCenter-PuixzPdSxhBO') : t('operationCenter-bEXzXmY7LjWO')}
                </Tag>
              </Descriptions.Item>

              {data?.isDatatistId === 'ENABLE' ? (
                <Descriptions.Item
                  label={
                    <Tooltip title={t('operationCenter-5QHWxofk4KX3')}>
                      {t('operationCenter-8AjrbN20rdN8')} <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  {data?.encryptDatatistId === 1 ? t('operationCenter-hAR7W1GaPkiE') : t('operationCenter-0W5pMMZBl0nl')}
                </Descriptions.Item>
              ) : (
                <Descriptions.Item></Descriptions.Item>
              )}
            </Descriptions>
            <Descriptions column={3} title={t('operationCenter-2OQJ9ZcIvRyz')}>
              <Descriptions.Item label={t('operationCenter-vMOpfdW7OJZY')}>
                <Tag className="on" color={isCustomParamsRule ? 'success' : 'error'}>
                  {isCustomParamsRule ? t('operationCenter-PuixzPdSxhBO') : t('operationCenter-bEXzXmY7LjWO')}
                </Tag>
              </Descriptions.Item>

              {isCustomParamsRule ? (
                <Descriptions.Item
                  span={2}
                  label={
                    <Tooltip title={t('operationCenter-VOikbdYRClwa')}>
                      {t('operationCenter-ltJX82If4RoM')} <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  {data?.encryptCustomParams === 1 ? t('operationCenter-hAR7W1GaPkiE') : t('operationCenter-0W5pMMZBl0nl')}
                </Descriptions.Item>
              ) : (
                <Descriptions.Item></Descriptions.Item>
              )}

              {/* {data?.isCustomParamsRule === 'ENABLE' ? (
              <Descriptions.Item label="参数一">
                <Text
                  style={{ width: 240 }}
                  ellipsis={{ tooltip: data?.utmContent }}
                >
                  {data?.utmSource}
                </Text>
              </Descriptions.Item>
            ) : (
              <Descriptions.Item></Descriptions.Item>
            )} */}

              {isCustomParamsRule ? (
                customParams.map((item, index) => (
                  <Descriptions.Item label={`${t('operationCenter-T4aXoE3vTb5l')}${indexEnum[index + 1]}`}>
                    <Text style={{ width: 240 }}>{item}</Text>
                  </Descriptions.Item>
                ))
              ) : (
                <Descriptions.Item></Descriptions.Item>
              )}
            </Descriptions>
            <Descriptions column={3} title={t('operationCenter-cUl9uQiVYUI5')}>
              <Descriptions.Item label={t('operationCenter-vMOpfdW7OJZY')} span={3}>
                {data?.isUtmRule ? (
                  <Tag className="on" color={data?.isUtmRule === 'ENABLE' ? 'success' : 'error'}>
                    {data?.isUtmRule === 'ENABLE' ? t('operationCenter-PuixzPdSxhBO') : t('operationCenter-bEXzXmY7LjWO')}
                  </Tag>
                ) : (
                  <Tag className="on" color={isUtmRule ? 'success' : 'error'}>
                    {isUtmRule ? t('operationCenter-PuixzPdSxhBO') : t('operationCenter-bEXzXmY7LjWO')}
                  </Tag>
                )}
              </Descriptions.Item>

              {data?.isUtmRule ? (
                data?.isUtmRule === 'ENABLE' ? (
                  <Descriptions.Item
                    label={
                      <Tooltip title={t('operationCenter-Y2zyxPqFA41E')}>
                        {t('operationCenter-u28flUpYp35b')} <InfoCircleOutlined />
                      </Tooltip>
                    }
                  >
                    <Text style={{ width: 240 }} ellipsis={{ tooltip: data?.utmSource }}>
                      {data?.utmSource}
                    </Text>
                  </Descriptions.Item>
                ) : (
                  <Descriptions.Item />
                )
              ) : isUtmRule ? (
                <Descriptions.Item
                  label={
                    <Tooltip title={t('operationCenter-Y2zyxPqFA41E')}>
                      {t('operationCenter-u28flUpYp35b')} <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <Text style={{ width: 240 }} ellipsis={{ tooltip: utmParams.utmSource }}>
                    {utmParams.utmSource}
                  </Text>
                </Descriptions.Item>
              ) : (
                <Descriptions.Item />
              )}

              {data?.isUtmRule ? (
                data?.isUtmRule === 'ENABLE' ? (
                  <Descriptions.Item
                    label={
                      <Tooltip title={t('operationCenter-4w5VTL5j3O1O')}>
                        {t('operationCenter-9xvG9a5DFLkB')} <InfoCircleOutlined />
                      </Tooltip>
                    }
                  >
                    <Text style={{ width: 240 }} ellipsis={{ tooltip: data?.utmMedium }}>
                      {data?.utmMedium}
                    </Text>
                  </Descriptions.Item>
                ) : (
                  <Descriptions.Item />
                )
              ) : isUtmRule ? (
                <Descriptions.Item
                  label={
                    <Tooltip title={t('operationCenter-4w5VTL5j3O1O')}>
                      {t('operationCenter-9xvG9a5DFLkB')} <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <Text style={{ width: 240 }} ellipsis={{ tooltip: utmParams.utmMedium }}>
                    {utmParams.utmMedium}
                  </Text>
                </Descriptions.Item>
              ) : (
                <Descriptions.Item />
              )}

              {data?.isUtmRule ? (
                data?.isUtmRule === 'ENABLE' ? (
                  <Descriptions.Item
                    label={
                      <Tooltip title={t('operationCenter-whJsdTqWrMd0')}>
                        {t('operationCenter-wuSx1OtwtmWQ')}
                        <InfoCircleOutlined />
                      </Tooltip>
                    }
                  >
                    <Text style={{ width: 240 }} ellipsis={{ tooltip: data?.utmCampaign }}>
                      {data?.utmCampaign}
                    </Text>
                  </Descriptions.Item>
                ) : (
                  <Descriptions.Item />
                )
              ) : isUtmRule ? (
                <Descriptions.Item
                  label={
                    <Tooltip title={t('operationCenter-whJsdTqWrMd0')}>
                      {t('operationCenter-wuSx1OtwtmWQ')} <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <Text style={{ width: 240 }} ellipsis={{ tooltip: utmParams.utmCampaign }}>
                    {utmParams.utmCampaign}
                  </Text>
                </Descriptions.Item>
              ) : (
                <Descriptions.Item />
              )}

              {data?.isUtmRule ? (
                data?.isUtmRule === 'ENABLE' ? (
                  <Descriptions.Item
                    label={
                      <Tooltip title={t('operationCenter-5uxft37ciIbA')}>
                        {t('operationCenter-cWyNIokAdaGN')}
                        <InfoCircleOutlined />
                      </Tooltip>
                    }
                  >
                    <Text style={{ width: 240 }} ellipsis={{ tooltip: data?.utmContent }}>
                      {data?.utmContent}
                    </Text>
                  </Descriptions.Item>
                ) : (
                  <Descriptions.Item />
                )
              ) : isUtmRule ? (
                <Descriptions.Item
                  label={
                    <Tooltip title={t('operationCenter-5uxft37ciIbA')}>
                      {t('operationCenter-cWyNIokAdaGN')} <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <Text style={{ width: 240 }} ellipsis={{ tooltip: utmParams.utmContent }}>
                    {utmParams.utmContent}
                  </Text>
                </Descriptions.Item>
              ) : (
                <Descriptions.Item />
              )}

              {data?.isUtmRule ? (
                data?.isUtmRule === 'ENABLE' ? (
                  <Descriptions.Item
                    label={
                      <Tooltip title={t('operationCenter-o9awiUq0eA4q')}>
                        {t('operationCenter-BjnTBzCpUSsB')}
                        <InfoCircleOutlined />
                      </Tooltip>
                    }
                  >
                    <Text style={{ width: 240 }} ellipsis={{ tooltip: data?.utmTerm }}>
                      {data?.utmTerm}
                    </Text>
                  </Descriptions.Item>
                ) : (
                  <Descriptions.Item />
                )
              ) : isUtmRule ? (
                <Descriptions.Item
                  label={
                    <Tooltip title={t('operationCenter-o9awiUq0eA4q')}>
                      {t('operationCenter-BjnTBzCpUSsB')} <InfoCircleOutlined />
                    </Tooltip>
                  }
                >
                  <Text style={{ width: 240 }} ellipsis={{ tooltip: utmParams.utmTerm }}>
                    {utmParams.utmTerm}
                  </Text>
                </Descriptions.Item>
              ) : (
                <Descriptions.Item />
              )}
            </Descriptions>
          </div>
        </div>
      </div>
    </Spin>
  );
};

export default Details;
