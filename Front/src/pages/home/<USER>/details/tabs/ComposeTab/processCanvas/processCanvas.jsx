import { Modal, Table, Tooltip, message } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { Link, useHistory } from 'react-router-dom';
import { calcPageNo } from 'utils/universal';

import ColumnActionCom from 'components/featurecoms/tableactioncom/index';

import CampaignV2Service from 'service/CampaignV2Service';
import CampaignsService from 'service/CampaignsService';
import { t } from 'utils/translation';
import { initParam, phaseList, ruleList } from '../config';

const campaignsService = new CampaignsService();
const campaignV2Service = new CampaignV2Service();

const pagination = {
  showTotal: (totals) => `${t('dataCenter-oVHOMzdWo1X7')} ${totals} ${t('dataCenter-GviPAx92Kah6')}`,
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const featureData = {
  detail: {
    text: t('dataCenter-90Y7Jp6WSHIM')
  },
  dropdownData: {
    delete: {
      text: t('dataCenter-xOyEm7ZaT0yb'),
      code: 'aim_campaigns_edit'
    }
  }
};

const Process = (props) => {
  const { state, campaignId, dispatch, campaignsData } = props;
  const { saveParams, situationRefresh } = state;
  const history = useHistory();

  const [param, setParam] = useState(_.cloneDeep(initParam));
  const [composeList, setComposeList] = useState([]);
  // const [search, setSearch] = useState([]);
  const [loading, setLoading] = useState(false);
  // const [campaignList, setCampaignList] = useState([]);
  const columnsList = [
    {
      title: t('dataCenter-wOrWUKw38zJq'),
      width: '10%',
      dataIndex: 'id',
      sorter: true
    },
    {
      title: t('dataCenter-FoLyafNiJDGj'),
      render: (text, val) => {
        if (val.phase === 'DRAFT') {
          return (
            <Tooltip title={val.name}>
              <span className="tableName">{val.name}</span>
            </Tooltip>
          );
        } else {
          return (
            <Tooltip title={val.name}>
              <Link to={`/aimarketer/home/<USER>/detail?id=${val.id}`}>
                <span className="tableName">{val.name}</span>
              </Link>
            </Tooltip>
          );
        }
      },
      // ellipsis: true,
      width: 320,
      height: 100
    },
    {
      title: t('dataCenter-e86EGKDm8zQp'),
      width: '10%',
      render: (text, record) => <div>{record?.scenario.name}</div>
    },
    {
      title: t('dataCenter-bi57K6780bDk'),
      render: (text, record) => {
        return (
          <div className="status">
            {/* <span className="circle" style={{ backgroundColor: _.filter(phaseList, (v) => v.value === record.campaignV2.phase)[0]?.color }} /> */}
            {record.phase ? _.filter(phaseList, (v) => v.value === record.phase)[0]?.name : '-'}
          </div>
        );
      },
      width: 120
    },
    // {
    //   title: '最后运行状态',
    //   dataIndex: 'phase',
    //   render: (text, record) => {
    //     return (
    //       <div className="status">
    //         <span
    //           className="circle"
    //           style={{
    //             backgroundColor: _.filter(lastCalcStatus, (v) => v.value === record.lastCalcStatus)[0]?.color
    //           }}
    //         />
    //         {record.lastCalcStatus ? _.filter(lastCalcStatus, (v) => v.value === record?.lastCalcStatus)[0]?.name : '-'}
    //       </div>
    //     );
    //   },
    //   width: 120
    // },
    {
      title: t('dataCenter-m8Onea86dn6a'),
      render: (text, record) => {
        return (
          <div className="status">
            {record.calcRule ? _.filter(ruleList, (v) => v.value === record.calcRule)[0]?.name : '-'}
          </div>
        );
      },
      width: 110
    },
    {
      title: t('dataCenter-M81OG6BvRbUi'),
      dataIndex: 'beginTime',
      render: (text, record) => dayjs(record.beginTime).format('YYYY-MM-DD HH:mm:ss'),
      width: 180,
      sorter: true
    },
    {
      title: t('dataCenter-xKjzTfv9o44p'),
      dataIndex: 'endTime',
      render: (text, reocrd) => (reocrd.endTime ? dayjs(reocrd.endTime).format('YYYY-MM-DD HH:mm:ss') : '-'),
      width: 180,
      sorter: true
    }
  ];

  //   useEffect(() => {
  //     const getCampainsData = async () => {
  //       const getCampainsList = await campaignsService.findCampaigns([{ operator: 'EQ', propertyName: 'campaignsId', value: campaignId }]);
  //       setCampaignList(getCampainsList);
  //     };
  //     getCampainsData();
  //   }, [param, saveParams]);

  useEffect(() => {
    const getComposeData = async () => {
      setLoading(true);
      const getCampainsList = await campaignsService.findCampaigns([
        { operator: 'EQ', propertyName: 'campaignsId', value: campaignId }
      ]);
      // setCampaignList(getCampainsList);
      const campaignIdList = [];
      getCampainsList.forEach((item) => {
        if (item.campaignV2) {
          campaignIdList.push(item.campaignV2.id);
        }
      });
      const finalParam = _.cloneDeep(param);
      if (campaignIdList.length) {
        finalParam.search = [
          {
            propertyName: 'id',
            operator: 'IN',
            value: campaignIdList.join(',')
          },
          { propertyName: 'phase', operator: 'NE', value: 'DRAFT' },
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: campaignsData?.deptId || window.getDeptId()
          }
        ];
      } else {
        finalParam.search = [
          { propertyName: 'id', operator: 'EQ', value: 0 },
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: campaignsData?.deptId || window.getDeptId()
          }
        ];
      }

      const getCampaingsV2List = await campaignV2Service.query2(finalParam);
      pagination.total = getCampaingsV2List.totalElements;
      pagination.current = param.page;
      pagination.pageSize = param.size;
      setComposeList(getCampaingsV2List.content);
      setLoading(false);
    };
    getComposeData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [param, saveParams]);

  const onTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }

    setParam({ ...param });
  };

  const delCampaign = async (id) => {
    const getCampainsList = await campaignsService.findCampaigns([
      { operator: 'EQ', propertyName: 'campaignsId', value: campaignId }
    ]);
    const delId = _.find(getCampainsList, (o) => o.campaignId === id).id;

    await campaignsService.delCampaignsDetail(delId);
    const page = calcPageNo(pagination.total, pagination.current, pagination.pageSize);

    dispatch({ situationRefresh: !situationRefresh });
    setParam({ ...param, page });

    message.success(t('dataCenter-P4l2EuJg2KfA'));
  };

  const onColumnActionClick = (key, feature, record) => {
    // setEditValue(record);
    const { id } = record;
    if (key === 'detail') {
      history.push(`/aimarketer/home/<USER>/detail?id=${id}`);
    } else if (key === 'delete') {
      Modal.confirm({
        // title: '移除画布',
        className: 'campainsDelWrap',
        content: <p className="confirmDelete">{t('dataCenter-lskb3yOh0T4V')}</p>,
        okText: t('dataCenter-xmbk4yliJtB9'),
        okType: 'danger',
        cancelText: t('dataCenter-zKkxyLwAsR94'),
        async onOk() {
          delCampaign(id);
        },
        onCancel() {}
      });
    }
  };

  const actionColumn = {
    title: t('dataCenter-pIZ7h8Bi1IaI'),
    className: 'td-set',
    width: 150,
    fixed: 'right',
    render: (text, record) => {
      return <ColumnActionCom closeDropdown featureData={featureData} record={record} onClick={onColumnActionClick} />;
    }
  };

  const [columns, setColumns] = useState(columnsList);

  // 增加操作列
  useEffect(() => {
    const _columns = _.cloneDeep(columns);
    _columns.push(actionColumn);
    setColumns(_columns);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="processWrap">
      <div className="tableWrap">
        <div className="tableTitle">{t('dataCenter-4o7LW1KDLhzS')}</div>
        <Table
          columns={columns}
          onChange={onTableChange}
          dataSource={composeList}
          rowKey="id"
          // {...tableProps}
          pagination={pagination}
          loading={loading}
          scroll={{ x: 1280 }}
        />
      </div>
    </div>
  );
};

export default Process;
