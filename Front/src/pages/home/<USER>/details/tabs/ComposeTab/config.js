import { t } from 'utils/translation';

export const initParam = {
  page: 1,
  search: [],
  size: 10,
  sorts: [{ propertyName: 'updateTime', direction: 'desc' }]
};

// 计算规则
export const ruleList = [
  {
    name: t('dataCenter-ueGMKxVnAI6x'),
    text: t('dataCenter-ueGMKxVnAI6x'),
    value: 'ONCE',
    key: 'ONCE'
  },
  {
    name: t('dataCenter-H0LJiwZaTd3E'),
    text: t('dataCenter-H0LJiwZaTd3E'),
    value: 'SCHEDULE',
    key: 'SCHEDULE'
  }
];

export const phaseList = [
  {
    name: t('dataCenter-73S3N9BskhZh'),
    text: t('dataCenter-73S3N9BskhZh'),
    value: 'DRAFT',
    key: 'DRAFT',
    color: 'rgba(0, 0, 0, 0)'
  },
  {
    name: t('dataCenter-dctVVtgOKVcz'),
    text: t('dataCenter-dctVVtgOKVcz'),
    value: 'TESTING',
    key: 'TESTING',
    color: '#52c41a'
  },
  {
    name: t('dataCenter-0aj2xrB4xF18'),
    text: t('dataCenter-0aj2xrB4xF18'),
    value: 'TEST_SUC',
    key: 'TEST_SUC',
    color: '#1890ff'
  },
  {
    name: t('dataCenter-57kwY3CsvUYS'),
    text: t('dataCenter-57kwY3CsvUYS'),
    value: 'ENABLE',
    key: 'ENABLE',
    color: '#f5222d'
  },
  {
    name: t('dataCenter-PL9eL5DBoXWu'),
    text: t('dataCenter-PL9eL5DBoXWu'),
    value: 'STOPPED',
    key: 'STOPPED',
    color: '#faad14'
  }
];

export const lastCalcStatus = [
  {
    name: t('dataCenter-yFwfuJbIToxO'),
    text: t('dataCenter-yFwfuJbIToxO'),
    value: 'NOTRUN',
    key: 'NOTRUN',
    color: '#D9D9D9'
  },
  {
    name: t('dataCenter-ES0rJPaHSrk7'),
    text: t('dataCenter-ES0rJPaHSrk7'),
    value: 'RUNNING',
    key: 'RUNNING',
    color: '#1890FF'
  },
  {
    name: t('dataCenter-VPzek5eW4CKJ'),
    text: t('dataCenter-VPzek5eW4CKJ'),
    value: 'STOPPING',
    key: 'STOPPING',
    color: '#FFE58F'
  },
  {
    name: t('dataCenter-KgATyeABEuyN'),
    text: t('dataCenter-KgATyeABEuyN'),
    value: 'STOPPED',
    key: 'STOPPED',
    color: '#52C41A'
  },
  {
    name: t('dataCenter-fiONBsulYUTb'),
    text: t('dataCenter-fiONBsulYUTb'),
    value: 'TERMINATED',
    key: 'TERMINATED',
    color: '#FAAD14'
  },
  {
    name: t('dataCenter-GJNIysqS0xfZ'),
    text: t('dataCenter-GJNIysqS0xfZ'),
    value: 'FAIL',
    key: 'FAIL',
    color: '#FF4D4F'
  },
  {
    name: t('dataCenter-DqyVG98wofzS'),
    text: t('dataCenter-DqyVG98wofzS'),
    value: 'COMPLETE_FAIL',
    key: 'COMPLETE_FAIL',
    color: '#FF4D4F'
  },
  {
    name: t('dataCenter-iEqbLr3jf3th'),
    text: t('dataCenter-iEqbLr3jf3th'),
    value: 'RECALLING',
    key: 'RECALLING',
    color: '#FFADD2'
  },
  {
    name: t('dataCenter-olYIGrzQrh4T'),
    text: t('dataCenter-olYIGrzQrh4T'),
    value: 'RECALL_SUC',
    key: 'RECALL_SUC',
    color: '#F759AB'
  }
];
