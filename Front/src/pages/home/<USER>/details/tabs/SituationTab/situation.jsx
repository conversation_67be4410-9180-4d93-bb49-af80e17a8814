import { CloseOutlined } from '@ant-design/icons';
import { Button, Collapse, Drawer, Form, Input, InputNumber, message, Spin, Table, Tooltip, TreeSelect } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';

import CampaignsService from 'service/CampaignsService';
import ScenarioService from 'service/ScenarioService';

import CheckAuth from '@/utils/checkAuth';
import { t } from 'utils/translation';
import { setNativeValue } from 'utils/universal';
import ProcessPanel from './processCanvasPanel/processCanvasPanel';

import './situation.scss';

const { SHOW_PARENT } = TreeSelect;
const { Panel } = Collapse;

const campaignsService = new CampaignsService();

const Details = (props) => {
  const { campaignId, state, userId, editValue, data } = props;
  const searchRef = useRef();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [treeValue, setTreeValue] = useState([]);
  const [scenarioTreeList, setScenarioTreeList] = useState([]);
  const [normVisibile, setNormVisibile] = useState(false);
  const [normLoading, setNormLoading] = useState(false);
  const [tableList, setTableList] = useState([]);
  const [selectedList, setSelectedList] = useState([]);
  const [normChecked, setNormChecked] = useState([]);
  const [reflash, setReflash] = useState(false);
  const [activeKey, setActiveKey] = useState(['1']);
  const [campaignsList, setCampaignList] = useState([]);
  const [panelData, setPanelData] = useState([]);
  const [totalCount, setTotalCount] = useState([]);
  const [campaingsMetricConfigList, setCampaingsMetricConfigList] = useState([]);
  const [PanelList, setPanelList] = useState([
    {
      type: 'processCanvas',
      name: t('dataCenter-L5mtfBn4Pa5O'),
      realName: t('dataCenter-L5mtfBn4Pa5O'),
      key: '1'
    }
    // {
    //   type: 'page',
    //   name: '落地页',
    //   realName: '落地页',
    //   key: '2'
    // }
  ]);

  const { saveParams, situationRefresh } = state;

  // const mock = [
  //   {
  //     name: 'test',
  //     dataType: 'INT',
  //     memo: 'testmemo'
  //   },
  //   {
  //     name: 'test',
  //     dataType: 'INT',
  //     memo: 'testmemo'
  //   }
  // ];

  const columns = [
    {
      title: t('dataCenter-L3WjKGhRtzat'),
      dataIndex: 'name',
      ellipsis: true,
      width: '200px',
      render: (text, record) => (
        <Tooltip title={record.name}>
          <span className="tableTextElipsis">{record.name}</span>
        </Tooltip>
      )
    },
    {
      title: t('dataCenter-s7ksxNecBOmj'),
      dataIndex: 'dataType',
      width: '120px'
    },
    {
      title: t('dataCenter-aOHnftY2uGjD'),
      dataIndex: 'memo',
      render: (text, record) => (
        <Tooltip title={record.memo}>
          <span className="tableTextElipsis">{record.memo}</span>
        </Tooltip>
      ),
      ellipsis: true
    }
  ];

  useEffect(() => {
    const init = async () => {
      const scenarioList = await ScenarioService.scenarioList([]);
      setTreeValue(scenarioList.map((item) => item.id));
      let resTreeData;
      if (scenarioList.length === 1) {
        resTreeData = scenarioList.map((item) => {
          return {
            title: item.name,
            value: item.id,
            key: item.id
          };
        });
      } else {
        resTreeData = [
          {
            title: t('dataCenter-JoqBBF9MXTcr'),
            value: scenarioList.map((item) => item.id).join(','),
            key: scenarioList.map((item) => item.id).join(','),
            children: scenarioList.map((item) => {
              return {
                title: item.name,
                value: item.id,
                key: item.id
              };
            })
          }
        ];
      }

      setScenarioTreeList(resTreeData);
    };
    init();
  }, []);

  // useEffect(() => {
  //   setPanelData(PanelList);
  // }, [campaignsList]);

  useEffect(() => {
    const getSituationData = async () => {
      setLoading(true);
      if (treeValue.length) {
        try {
          const getCampainsList = await campaignsService.findCampaigns([
            { operator: 'EQ', propertyName: 'campaignsId', value: campaignId },
            {
              operator: 'IN',
              propertyName: 'scenarioId',
              value: treeValue.toString()
            },
            {
              operator: 'EQ',
              propertyName: 'deptId',
              value: data && data.deptId
            }
          ]);

          const campaignIdList = [];
          const newGetCampainsList = [];
          getCampainsList.forEach((item) => {
            if (item.campaignV2) {
              campaignIdList.push(item.campaignV2.id);
              newGetCampainsList.push(item);
            }
          });
          // let normsList = [];
          // if (campaignList.length) {
          //   normsList = await campaignsService.findAllByCampaignsId(campaignList);
          // }

          const campaignsMetricTotalList = await campaignsService.getCampaignsMetricsTotal({
            campaignsId: campaignId,
            campaignIds: campaignIdList
          });
          setTotalCount(campaignsMetricTotalList);

          // const campaingsMetricConfigList = await campaignsService.getCampaignsMetricsConfig([{ operator: 'EQ', propertyName: 'campaignsId', value: campaignId }]);
          const _panelData = _.cloneDeep(PanelList);
          _panelData.forEach((item) => {
            switch (item.type) {
              case 'processCanvas':
                item.data = newGetCampainsList;
                item.content = [
                  {
                    name: t('dataCenter-Un5dxgxCHGea'),
                    // value: Number(_.find(campaignsMetricTotalList, (o) => o.key === 'passed_count')?.value).toLocaleString()
                    value: _.find(campaignsMetricTotalList, (o) => o.key === 'passed_count')
                      ? Number(
                          _.find(campaignsMetricTotalList, (o) => o.key === 'passed_count')?.value
                        ).toLocaleString()
                      : 0
                  },
                  {
                    name: t('dataCenter-Un5dxgxCHGea'),
                    // value: Number(_.find(campaignsMetricTotalList, (o) => o.key === 'join_count')?.value).toLocaleString(),
                    value: _.find(campaignsMetricTotalList, (o) => o.key === 'join_count')
                      ? Number(_.find(campaignsMetricTotalList, (o) => o.key === 'join_count')?.value).toLocaleString()
                      : 0
                  }
                ];
                return;
              // case 'page':
              //   item.data = mock;
              //   item.content = [
              //     {
              //       name: '访客',
              //       value: '287,659'
              //     },
              //     {
              //       name: '登录用户',
              //       value: '175,295'
              //     }
              //   ];
              //   return;
              default:
                item.data = [];
            }
          });

          _panelData.forEach((item, index) => {
            const Index = _.find(activeKey, (o) => item.key === o);
            if (Index) {
              _panelData[index].name = `${item.realName}(${item.data.length})`;
            } else {
              _panelData[index].name = (
                <div key={item.key}>
                  <div>{`${item.realName}(${item.data.length})`}</div>
                  <div className="contentTitle">
                    {item.content.map((contentItem) => (
                      <div className="contentValue">
                        <div className="name">{contentItem.name}</div>
                        <div className="value">{contentItem.value}</div>
                      </div>
                    ))}
                  </div>
                </div>
              );
            }
          });
          setPanelData(_panelData);
          setPanelList(_panelData);
          // setPanelData(PanelList);

          // setCampaingsMetricConfigList(campaingsMetricConfigList);
          setCampaignList(newGetCampainsList);
          // setTableList(normsList);
          setLoading(false);
        } catch {
          setLoading(false);
        }
      } else {
        const _panelData = _.cloneDeep(PanelList);
        _panelData.forEach((item) => {
          switch (item.type) {
            case 'processCanvas':
              item.data = [];
              item.content = [
                {
                  name: t('dataCenter-Un5dxgxCHGea'),
                  // value: Number(_.find(campaignsMetricTotalList, (o) => o.key === 'passed_count')?.value).toLocaleString()
                  value: 0
                },
                {
                  name: t('dataCenter-T7hekLSnvNlk'),
                  // value: Number(_.find(campaignsMetricTotalList, (o) => o.key === 'join_count')?.value).toLocaleString(),
                  value: 0
                }
              ];
              return;
            // case 'page':
            //   item.data = mock;
            //   item.content = [
            //     {
            //       name: '访客',
            //       value: '287,659'
            //     },
            //     {
            //       name: '登录用户',
            //       value: '175,295'
            //     }
            //   ];
            //   return;
            default:
              item.data = [];
          }
        });

        _panelData.forEach((item, index) => {
          const Index = _.find(activeKey, (o) => item.key === o);
          if (Index) {
            _panelData[index].name = `${item.realName}(${item.data.length})`;
          } else {
            _panelData[index].name = (
              <div key={item.key}>
                <div>{`${item.realName}(${item.data.length})`}</div>
                <div className="contentTitle">
                  {item.content.map((contentItem) => (
                    <div className="contentValue">
                      <div className="name">{contentItem.name}</div>
                      <div className="value">{contentItem.value}</div>
                    </div>
                  ))}
                </div>
              </div>
            );
          }
        });
        setPanelData(_panelData);
        setPanelList(_panelData);
        setTotalCount([]);
        setCampaignList([]);
        setLoading(false);
      }
    };
    getSituationData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [treeValue, reflash, saveParams, situationRefresh]);

  useEffect(() => {
    const getNormData = async () => {
      let normsList = [];
      const campaignIdList = [];
      const getCampainsList = await campaignsService.findCampaigns([
        { operator: 'EQ', propertyName: 'campaignsId', value: campaignId },
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: data && data.deptId
        }
      ]);
      getCampainsList.forEach((item) => {
        if (item.campaignV2) {
          campaignIdList.push(item.campaignV2.id);
        }
      });

      if (campaignIdList.length) {
        normsList = await campaignsService.findAllByCampaignsId(campaignIdList);
      }

      const campaingsMetricConfigList = await campaignsService.getCampaignsMetricsConfig([
        { operator: 'EQ', propertyName: 'campaignsId', value: campaignId }
      ]);

      setCampaingsMetricConfigList(campaingsMetricConfigList);
      setTableList(normsList);
    };
    getNormData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [campaignsList]);

  const saveRecordInfo = async (id, userId) => {
    await campaignsService.saveUserOperationRecord({
      targetId: id,
      id: editValue?.recentUserOperationRecord?.id,
      targetType: 'CAMPAIGNS',
      type: 'RECENT',
      createUserId: userId,
      updateUserId: userId,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
  };

  const onTreeChange = (newValue) => {
    if (_.isArray(newValue[0])) {
      setTreeValue(newValue[0]);
    } else {
      setTreeValue(newValue);
    }
  };

  const tProps = {
    treeData: scenarioTreeList,
    value: treeValue,
    onChange: onTreeChange,
    showArrow: true,
    showSearch: false,
    treeDefaultExpandAll: true,
    treeCheckable: true,
    showCheckedStrategy: SHOW_PARENT,
    style: { minWidth: '110px' }
  };

  const onNormCanel = () => {
    setNormVisibile(false);
  };

  const onNormVisible = async () => {
    if (searchRef.current) {
      onTextChange('');
      setNativeValue([searchRef.current.input], '');
    }
    setNormVisibile(true);
    if (campaingsMetricConfigList.length) {
      setNormChecked(campaingsMetricConfigList.map((item) => item.keyUp));
      form.setFieldsValue({
        selectedData: campaingsMetricConfigList.map((item) => {
          return {
            normName: item.name,
            key: item.keyUp,
            targetVal: item.target
          };
        })
      });
    } else {
      setNormChecked([]);
      form.setFieldsValue({
        selectedData: []
      });
    }
  };

  const onTextChange = _.debounce(async (val) => {
    let normsList = [];
    const campaignIdList = [];
    campaignsList.forEach((item) => {
      campaignIdList.push(item.campaignV2.id);
    });

    if (campaignIdList.length) {
      normsList = await campaignsService.findAllByCampaignsId(campaignIdList);
      const filterNormList = [];
      normsList.forEach((item) => {
        if (item.name.indexOf(val) >= 0) {
          filterNormList.push(item);
        }
      });
      setTableList(filterNormList);
    }
  }, 500);

  const onSave = async () => {
    const { selectedData } = form.getFieldValue();
    if (!selectedData.length) {
      message.error(t('dataCenter-yfqxP5l5EVMY'));
      return;
    }

    setNormLoading(true);
    const params = [];
    const _selectedData = _.cloneDeep(selectedData);

    _selectedData.forEach((item) => {
      if (Object.prototype.toString.call(item) === '[object Object]') {
        params.push({
          name: item.normName,
          type: 'SIMPLE',
          target: item.targetVal,
          campaignsId: campaignId,
          keyUp: item.key,
          id: _.find(campaingsMetricConfigList, (o) => o.keyUp === item.key)
            ? _.find(campaingsMetricConfigList, (o) => o.keyUp === item.key).id
            : undefined
        });
      }
    });

    try {
      await campaignsService.saveAllMetric(params);
      saveRecordInfo(campaignId, userId);
      message.success(t('dataCenter-0dSW9rjOTgW5'));
      setNormLoading(false);
      setNormVisibile(false);
      setReflash(!reflash);
    } catch (error) {
      setNormLoading(false);
    }
  };

  const delMetricChange = async (metricId) => {
    if (!metricId || !metricId.length) {
      return;
    }

    if (_.isArray(metricId)) {
      await campaignsService.delAllMetric(metricId);
    } else {
      await campaignsService.delMetric(metricId);
    }
    setReflash(!reflash);
  };

  const rowSelection = {
    preserveSelectedRowKeys: true,
    selectedRowKeys: normChecked,
    onChange: (selectedRowKeys, selectedRows) => {
      setNormChecked(selectedRowKeys);
      setSelectedList(selectedRows);
      selectedRows.forEach((item) => {
        if (form.getFieldValue().selectedData) {
          item.val = form.getFieldValue().selectedData?.find((selectItem) => selectItem.key === item.key)
            ? form.getFieldValue().selectedData?.find((selectItem) => selectItem.key === item.key).targetVal
            : undefined;
        }
      });

      form.setFieldsValue({
        selectedData: selectedRows.map((item) => {
          return {
            normName: item.name,
            key: item.key,
            targetVal: item.val
          };
        })
      });
    },

    onSelect: async (record, selected) => {
      if (!selected) {
        const metricId = _.find(campaingsMetricConfigList, (o) => o.keyUp === record.key)?.id;
        delMetricChange(metricId);
      }
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      if (!selected) {
        const selectAllId = [];
        changeRows.forEach((item) => {
          if (_.find(campaingsMetricConfigList, (o) => o.keyUp === item.key)) {
            selectAllId.push(_.find(campaingsMetricConfigList, (o) => o.keyUp === item.key)?.id);
          }
        });
        delMetricChange(selectAllId);
      }
    }
  };

  const onPanelChange = (key) => {
    const _panelData = _.cloneDeep(PanelList);

    _panelData.forEach((item, index) => {
      const Index = _.find(key, (o) => item.key === o);
      if (Index) {
        _panelData[index].name = `${item.realName}(${item.data.length})`;
      } else {
        _panelData[index].name = (
          <div key={item.key}>
            <div>{`${item.realName}(${item.data.length})`}</div>
            <div className="contentTitle">
              {item.content.map((contentItem) => (
                <div className="contentValue" key={contentItem.name}>
                  <div className="name">{contentItem.name}</div>
                  <div className="value">{contentItem.value}</div>
                </div>
              ))}
            </div>
          </div>
        );
      }
    });
    setPanelData(_panelData);
    setActiveKey(key);
  };

  const renderNode = (type) => {
    switch (type) {
      case 'processCanvas':
        return <ProcessPanel campaignsList={campaignsList} totalCount={totalCount} />;
      // case 'page': return <LandingPage campaignsList={mock} />;
      default:
        return <ProcessPanel />;
    }
  };

  return (
    <div className="situationWrap">
      <Spin spinning={loading}>
        <div className="userIdTypeWrap">
          <span style={{ color: 'rgba(0, 0, 0, 0.65)' }}>{t('dataCenter-vn2lLFsZxP7e')}</span>
          <TreeSelect {...tProps} className="scenarioTreeWrap" popupClassName="treeDropdownWrap" bordered={false} />
        </div>

        <div className="basicNormWrap">
          <div className="basicNorm">
            <div className="basicHeader">
              <span className="title">{t('dataCenter-HsuFeML7oehR')}</span>
              <CheckAuth code="aim_campaigns_edit">
                <a className="setting" onClick={onNormVisible}>
                  {t('dataCenter-sQtzcOG15qZ5')}
                </a>
              </CheckAuth>
            </div>
            <div className="basicContent">
              <div className="reach-count-wrapper">
                {totalCount?.map((item) => (
                  <div className="reachCount-item" key={item.key}>
                    <div className="reachCount-top">
                      <div className="reachCount-name">
                        {item.targetCompleteRate ? `${item.name} / ${t('dataCenter-wkrsGmw5DmEv')}` : item.name}
                      </div>
                    </div>
                    <div className="reachCountText">
                      {
                        <div>
                          <span>{item.value ? Number(item.value).toLocaleString() : 0}</span>
                          {item.targetCompleteRate ? <span> / {item.targetCompleteRate}</span> : null}
                        </div>
                      }
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="contentsWrap">
          <Collapse activeKey={activeKey} ghost onChange={onPanelChange} showArrow={false} collapsible="header">
            {panelData.map((item) => (
              <Panel header={item.name} key={item.key}>
                {renderNode(item.type)}
              </Panel>
            ))}
          </Collapse>
        </div>
        <Drawer
          title={t('dataCenter-sQtzcOG15qZ5')}
          open={normVisibile}
          onClose={onNormCanel}
          // onOk={onSave}
          // confirmLoading={normLoading}
          width={1110}
          footer={
            <div style={{ float: 'right' }}>
              <Button style={{ marginRight: '8px' }} onClick={onNormCanel}>
                {t('dataCenter-xujEOCdXqerA')}
              </Button>
              <Button type="primary" onClick={onSave} loading={normLoading}>
                {t('dataCenter-XXjotuP9Tuhb')}
              </Button>
            </div>
          }
          className="normModal"
        >
          <div className="normModalWrap">
            <div className="normTable">
              <div className="tableSearch">
                <Input
                  placeholder={t('dataCenter-ayd28pTMXX73')}
                  onChange={(e) => onTextChange(e.target.value)}
                  style={{ width: '100%' }}
                  ref={searchRef}
                />
              </div>
              <div className="tableWrap">
                <Table
                  columns={columns}
                  dataSource={tableList}
                  pagination={false}
                  rowSelection={{
                    ...rowSelection
                  }}
                  scroll={{ y: 448 }}
                />
              </div>
            </div>
            <div className="normSelected">
              <div className="titleWrap">
                <span className="title">{t('dataCenter-2SCt02X56zDf')}</span>
                <span className="count">[{selectedList.length}/20] {t('dataCenter-zbpRopUTuqWL')}</span>
              </div>
              <div className="selectedWrap">
                <div className="selectHeader">
                  <span className="name">{t('dataCenter-JWhNOB8cNjds')}</span>
                  <span className="target">{t('dataCenter-WZsrM19RTbaU')}</span>
                </div>
                <div className="selectedContet">
                  {/* <div className="content">
                  <span className="name">新用户</span>
                  <span className="targetInput"><InputNumber /><CloseOutlined /></span>
                </div> */}
                  {/* {
                    selectedList.map(item => (
                      <div className="content">
                        <span className="name">{item.name}</span>
                        <span className="targetInput"><InputNumber /><CloseOutlined /></span>
                      </div>
                    ))
                } */}
                  <Form name="seleceted-form" form={form}>
                    <Form.List name="selectedData">
                      {(fields, { remove }) => (
                        <>
                          {fields.map(({ key, name, fieldKey, ...restField }, index) => (
                            <Form.Item required={false} key={key} name={`arr${index}`}>
                              <div className="content">
                                <Form.Item
                                  {...restField}
                                  name={[index, 'normName']}
                                  noStyle
                                  help="Should be combination of numbers"
                                  rules={[
                                    { required: false }
                                    // {
                                    //   validator: onTableValidate
                                    // }
                                  ]}
                                >
                                  <Input bordered={false} disabled className="name" />
                                </Form.Item>

                                <span className="targetInput">
                                  <Form.Item
                                    {...restField}
                                    name={[index, 'targetVal']}
                                    noStyle
                                    help="Should be combination of numbers"
                                    rules={[
                                      { required: false }
                                      // {
                                      //   validator: onTableValidate
                                      // }
                                    ]}
                                  >
                                    <InputNumber min={1} />
                                  </Form.Item>

                                  <CloseOutlined
                                    onClick={async () => {
                                      const metricsId = _.find(
                                        campaingsMetricConfigList,
                                        (o) => o.keyUp === normChecked[index]
                                      ).id;
                                      delMetricChange(metricsId);
                                      remove(name);

                                      setNormChecked(form.getFieldValue().selectedData.map((item) => item.key));
                                      setSelectedList(normChecked.filter((item) => item !== selectedList[index]?.key));
                                    }}
                                  />
                                </span>
                              </div>
                            </Form.Item>
                          ))}
                        </>
                      )}
                    </Form.List>
                  </Form>
                </div>
              </div>
            </div>
          </div>
        </Drawer>
      </Spin>
    </div>
  );
};

export default Details;
