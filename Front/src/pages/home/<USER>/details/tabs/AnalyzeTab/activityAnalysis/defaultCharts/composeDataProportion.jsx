import { HolderOutlined, SwapOutlined } from '@ant-design/icons';
import { Chart } from '@antv/g2';
import { useToggle } from '@umijs/hooks';
import { Button, Empty, Popover, Radio, Spin, Table } from 'antd';
import dayjs from 'dayjs';
import update from 'immutability-helper';
import _ from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { t } from 'utils/translation';

import CampaignsService from 'service/CampaignsService';

import '../index.scss';

const campaignsService = new CampaignsService();

const timeTerm = [
  {
    value: 'DAY',
    label: t('dataCenter-rpMlVXoYKitd'),
    unit: 'days'
  },
  {
    value: 'WEEK',
    label: t('dataCenter-nMVPAdtTu8fw'),
    unit: 'weeks'
  },
  {
    value: 'MONTH',
    label: t('dataCenter-WIcwsQQdMIgk'),
    unit: 'months'
  }
];

const DraggableBodyRow = ({ index, moveRow, className, style, ...restProps }) => {
  const ref = useRef(null);
  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: type,
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};

      if (dragIndex === index) {
        return {};
      }

      return {
        isOver: monitor.isOver(),
        dropClassName: dragIndex < index ? ' drop-over-downward' : ' drop-over-upward'
      };
    },
    drop: (item) => {
      moveRow(item.index, index);
    }
  });

  const [{ isDragging }, drag] = useDrag({
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    }),
    // item 中包含 index 属性，则在 drop 组件 hover 和 drop 是可以根据第一个参数获取到 index 值
    item: { type, index }
    // end: (item) => {
    //   if (!item) {
    //     return;
    //   }
    //   end(item.id, item.index);
    // }
  });
  drop(drag(ref));

  // const style = {
  //   margin: '16px 6px',
  //   // Card 为占位元素是，透明度 0.4，拖拽状态时透明度 0.2，正常情况透明度为 1
  //   opacity: id === -1 ? 0.4 : isDragging ? 0.2 : 1
  // };

  return (
    <tr
      ref={ref}
      className={`${className}${isOver ? dropClassName : ''}`}
      style={{
        cursor: 'move',
        opacity: index === -1 ? 0.4 : isDragging ? 0.2 : 1,
        ...style
      }}
      {...restProps}
    />
  );
};

const type = 'DraggableBodyRow';

const ComposeDataProportion = (props) => {
  const { campaignsData, normData, campaignList, states, dispatch, userId } = props;
  const { state, toggle } = useToggle(false);
  const [chartType, setChartType] = useState('');
  const [dataSource, setDataSource] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectList, setSelectList] = useState([]);
  const [renderSelectList, setRenderSelectList] = useState([]);
  const [chartData, setChartData] = useState([]);
  const [defaultTime, setDefaultTime] = useState([]);
  const [orderNormData] = useState([]);
  const [loading, setLoading] = useState(false);

  const mountNodeRef = useRef(null);
  const chartRef = useRef(null);

  const { dateRange2, chartsReflash, switchState } = states;

  useEffect(() => {
    const getChartData = async () => {
      if (campaignList.length) {
        setLoading(true);
        try {
          const chartDatas = await campaignsService.calcPresetCharts({
            id: campaignsData.id,
            flowIds: campaignList.map((item) => item?.campaignV2?.id).filter((v) => v),
            key: chartType,
            type: 'SEGMENT_RATIO',
            dateRange: dateRange2?.map((item) => {
              return {
                ...item,
                truncateAsDay: true
              };
            })
          });
          setChartData(chartDatas);
          setLoading(false);
        } catch {
          setLoading(false);
        }

        setDefaultTime(dateRange2);
      }
    };
    getChartData();
  }, [chartType, chartsReflash, campaignList]);

  useEffect(() => {
    if (!mountNodeRef.current) {
      return;
    }

    if (_.isEmpty(chartData) || chartRef.current) {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    }
    chartRef.current = new Chart({
      container: mountNodeRef.current,
      autoFit: true,
      height: 350
    });
    chartRef.current.data(chartData);
    chartRef.current.scale('ratio', {
      formatter: (val) => {
        val = `${val}%`;
        return val;
      }
    });
    chartRef.current.coordinate('theta', {
      radius: 0.7,
      innerRadius: 0.7
    });
    chartRef.current.legend(false);
    chartRef.current.tooltip({
      showTitle: false,
      showMarkers: false,
      itemTpl:
        '<li class="g2-tooltip-list-item"><span style="background-color:{color};" class="g2-tooltip-marker"></span>{name}: {value}</li>'
    });

    // 辅助文本
    chartRef.current
      .annotation()
      .text({
        position: ['50%', '47%'],
        content: chartData[0].total.toLocaleString(),
        style: {
          fontSize: 24,
          fill: 'rgba(0, 0, 0, 0.85)',
          fontWeight: 500,
          textAlign: 'center'
        }
      })
      .text({
        position: ['50%', '55%'],
        content: '总计',
        style: {
          fontSize: 12,
          fill: 'rgba(0, 0, 0, 0.85)',
          textAlign: 'center'
        }
      });

    chartRef.current
      .interval()
      .adjust('stack')
      .position('ratio')
      .color('flowId')
      .label('ratio', (percent) => {
        return {
          content: (data) => {
            return `${data.flowId.length > 10 ? `${data.flowId.slice(0, 10)}... ` : data.flowId}\n${percent}%`;
          },
          offset: 24
        };
      })
      .tooltip('flowId*ratio', (item, percent) => {
        percent = `${percent}%`;
        return {
          name: item,
          value: percent
        };
      });
    // chartRef.current.interaction('element-active');

    chartRef.current.render();
  }, [chartData]);

  // 更新数据
  // useEffect(() => {
  //   if (!_.isEmpty(chartRef.current)) {
  //     chartRef.current.changeData(chartData);
  //   }
  // }, [chartData]);

  const columns = [
    {
      title: '指标名称',
      dataIndex: 'name',
      className: 'drag-visible'
    },
    {
      title: '数据类型',
      dataIndex: 'dataType'
    },
    {
      title: '备注',
      dataIndex: 'memo'
    },
    {
      title: '目标',
      dataIndex: 'target',
      render: (text, record) => (
        <div>
          <span style={{ marginRight: '66px' }}>{record?.targetCompleteRate ? '有' : '无'}</span>
          <HolderOutlined />
        </div>
      )
    }
  ];

  const saveRecordInfo = async (id, userId) => {
    await campaignsService.saveUserOperationRecord({
      targetId: id,
      id: campaignsData?.recentUserOperationRecord?.id,
      targetType: 'CAMPAIGNS',
      type: 'RECENT',
      createUserId: userId,
      updateUserId: userId,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
  };

  useEffect(() => {
    const getNormTableData = async () => {
      setChartType(normData[0]?.key);
      setSelectedRowKeys([normData[0]?.key]);
      setRenderSelectList([normData[0]]);
      setSelectList([normData[0]]);
      setDataSource(normData);
    };
    getNormTableData(normData);
  }, [normData, campaignsData, orderNormData]);

  const components = {
    body: {
      row: DraggableBodyRow
    }
  };

  const onTypeChange = (e) => {
    setChartType(e.target.value);
  };

  const getTime = (obj) => {
    let time = dayjs();
    if (obj.type === 'ABSOLUTE') {
      time = dayjs(obj.timestamp);
    } else if (obj.type === 'RELATIVE') {
      const info = timeTerm.find((n) => n.value === obj.timeTerm);
      if (obj.isPast) {
        time = dayjs().subtract(obj.times, info.unit);
      } else {
        time = dayjs().add(obj.times, info.unit);
      }
    }
    return time.valueOf();
  };

  // 改变弹出框可见状态
  const changeVisible = (visible) => {
    dispatch({
      switchState: visible
    });
    toggle(visible);
  };

  const handleVisibleChange = (visible) => {
    dispatch({
      switchState: visible
    });
    toggle(visible);
  };
  const handleColumnsSubmit = async () => {
    // const campaingsMetricConfigList = await campaignsService.getCampaignsMetricsConfig([{ operator: 'EQ', propertyName: 'campaignsId', value: campaignsData.id }]);
    // const _dataSource = _.cloneDeep(dataSource);
    // _dataSource.forEach(item => {
    //   if (_.find(campaingsMetricConfigList, (o) => o.keyUp === item.key && o.target)) {
    //     item.target = _.find(campaingsMetricConfigList, (o) => o.target).target;
    //   }
    // });

    // const newDataSource = _dataSource.map((item, index) => {
    //   return {
    //     orderNo: index,
    //     campaignsId: campaignsData.id,
    //     id: _.find(campaingsMetricConfigList, (o) => o.keyUp === item.key).id,
    //     keyUp: item.key,
    //     name: item.name,
    //     target: item.target,
    //     type: _.find(campaingsMetricConfigList, (o) => o.keyUp === item.key).type
    //   };
    // });

    // const result = await campaignsService.saveAllMetric(newDataSource);
    // setOrderNormData(result);
    saveRecordInfo(campaignsData.id, userId);
    setRenderSelectList(selectList);
    changeVisible(false);
  };

  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      const dragRow = dataSource[dragIndex];
      setDataSource(
        update(dataSource, {
          $splice: [
            [dragIndex, 1],
            [hoverIndex, 0, dragRow]
          ]
        })
      );

      const dragDataSource = update(dataSource, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, dragRow]
        ]
      });

      if (_.find(selectedRowKeys, (o) => o === dragRow.key)) {
        const resultKey = [];
        const resultItem = [];
        dragDataSource.forEach((item) => {
          selectList.forEach((selectItem) => {
            if (item.key === selectItem.key) {
              resultKey.push(item.key);
              resultItem.push(item);
            }
          });
        });
        onSelectChange(resultKey, resultItem, true);
      }
    },
    [dataSource, selectedRowKeys]
  );

  const onSelectChange = (selectedRowKeys, selectedRows, moveStatus) => {
    if (!moveStatus) {
      const _dataSource = _.cloneDeep(dataSource);
      const resultKey = [];
      const resultItem = [];
      _dataSource.forEach((item) => {
        selectedRows.forEach((selectItem) => {
          if (item.key === selectItem.key) {
            resultKey.push(item.key);
            resultItem.push(item);
          }
        });
      });
      setSelectedRowKeys(resultKey);
      setSelectList(resultItem);
    } else {
      setSelectedRowKeys(selectedRowKeys);
      setSelectList(selectedRows);
    }
  };

  const reset = () => {
    setChartType(normData[0]?.key);
    setSelectedRowKeys([normData[0]?.key]);
    setRenderSelectList([normData[0]]);
    setSelectList([normData[0]]);
    setDataSource(normData);
  };

  const rowSelection = {
    hideSelectAll: true,
    selectedRowKeys,
    onChange: onSelectChange
  };

  const renderNormTable = () => {
    return (
      <DndProvider backend={HTML5Backend} key={Math.random()}>
        <div className="normHeader">
          <div className="title">
            <span className="text">{t('dataCenter-az0B29GuyxUG')}</span>
            <span className="desc">{t('dataCenter-JMp2lULbFqi3')}</span>
          </div>
          <div className="reset" onClick={() => reset()}>
            {t('dataCenter-GCOp4Fpa0ipD')}
          </div>
        </div>
        <Table
          pagination={false}
          columns={columns}
          dataSource={dataSource}
          components={components}
          rowSelection={rowSelection}
          onRow={(_, index) => {
            const attr = {
              index,
              moveRow
            };
            return attr;
          }}
        />
        <div className="confirmBar">
          <Button onClick={() => changeVisible(false)} style={{ marginRight: '8px' }}>
            {t('dataCenter-xujEOCdXqerA')}
          </Button>
          <Button
            onClick={handleColumnsSubmit}
            type="primary"
            disabled={selectList.length >= 5 || selectList.length === 0}
          >
            {t('dataCenter-k6rTBmiEDr0H')}
          </Button>
        </div>
      </DndProvider>
    );
  };

  return (
    <div className="composeDataProportion">
      <Spin spinning={loading}>
        <div>
          <div className="titleWrap">
            <div className="titleDate">
              <div className="title">{t('dataCenter-J8QCmeRtvP65')}</div>
              <div className="date">
                {defaultTime?.length
                  ? `${dayjs(getTime(defaultTime[0])).format('YYYY-MM-DD HH:mm:ss')} ~ ${dayjs(
                      getTime(defaultTime[1])
                    ).format('YYYY-MM-DD HH:mm:ss')}`
                  : null}
              </div>
            </div>
            {normData.length ? (
              <div className="switch">
                <Popover
                  mask
                  overlayClassName="norm-setting-popup"
                  placement="bottomRight"
                  content={renderNormTable}
                  trigger="click"
                  open={state}
                  destroyTooltipOnHide
                  onOpenChange={handleVisibleChange}
                >
                  {/* <ColumnsSetting /> */}
                  <Button type="text" onClick={() => changeVisible(true)} disabled={switchState}>
                    <SwapOutlined />
                    <span className="normText">{t('dataCenter-983JHfhOc8PD')}</span>
                  </Button>
                  {/* <div onClick={() => changeVisible(true)}><SwapOutlined /><span className="normText">切换指标</span></div> */}
                </Popover>
              </div>
            ) : null}
          </div>

          <div className="switchChartType">
            <div className="type">
              {normData.length ? (
                <Radio.Group
                  onChange={onTypeChange}
                  value={chartType}
                  className={renderSelectList.length === 1 ? 'radio-only-one' : ''}
                >
                  {renderSelectList.map((item, index) => (
                    <Radio.Button value={item?.key} key={index}>
                      {item?.name}
                    </Radio.Button>
                  ))}
                </Radio.Group>
              ) : null}
            </div>
          </div>

          <div>
            {chartData.length ? (
              <div className="container" ref={mountNodeRef} />
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ position: 'relative', top: '80px' }} />
            )}
          </div>
        </div>
      </Spin>
    </div>
  );
};

export default ComposeDataProportion;
