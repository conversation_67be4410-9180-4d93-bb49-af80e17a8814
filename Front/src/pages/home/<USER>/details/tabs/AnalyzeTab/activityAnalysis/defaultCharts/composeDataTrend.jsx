import { HolderOutlined } from '@ant-design/icons';
import { Chart } from '@antv/g2';
import { useToggle } from '@umijs/hooks';
import { Button, Empty, Popover, Radio, Select, Spin, Table } from 'antd';
import dayjs from 'dayjs';
import update from 'immutability-helper';
import _ from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { t } from 'utils/translation';

import CampaignsService from 'service/CampaignsService';

import '../index.scss';

const campaignsService = new CampaignsService();

const { Option } = Select;

const timeTerm = [
  {
    value: 'DAY',
    label: t('dataCenter-rpMlVXoYKitd'),
    unit: 'days'
  },
  {
    value: 'WEEK',
    label: t('dataCenter-nMVPAdtTu8fw'),
    unit: 'weeks'
  },
  {
    value: 'MONTH',
    label: t('dataCenter-WIcwsQQdMIgk'),
    unit: 'months'
  }
];

const type = 'DraggableBodyRow';

const DraggableBodyRow = ({ index, moveRow, className, style, ...restProps }) => {
  const ref = useRef(null);
  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: type,
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};

      if (dragIndex === index) {
        return {};
      }

      return {
        isOver: monitor.isOver(),
        dropClassName: dragIndex < index ? ' drop-over-downward' : ' drop-over-upward'
      };
    },
    drop: (item) => {
      moveRow(item.index, index);
    }
  });

  const [{ isDragging }, drag] = useDrag({
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    }),
    // item 中包含 index 属性，则在 drop 组件 hover 和 drop 是可以根据第一个参数获取到 index 值
    item: { type, index }
    // end: (item) => {
    //   if (!item) {
    //     return;
    //   }
    //   end(item.id, item.index);
    // }
  });
  drop(drag(ref));

  // const style = {
  //   margin: '16px 6px',
  //   // Card 为占位元素是，透明度 0.4，拖拽状态时透明度 0.2，正常情况透明度为 1
  //   opacity: id === -1 ? 0.4 : isDragging ? 0.2 : 1
  // };

  return (
    <tr
      ref={ref}
      className={`${className}${isOver ? dropClassName : ''}`}
      style={{
        cursor: 'move',
        opacity: index === -1 ? 0.4 : isDragging ? 0.2 : 1,
        ...style
      }}
      {...restProps}
    />
  );
};

const ComposeDataTrend = (props) => {
  const { campaignsData, normData, campaignList, states, dispatch } = props;
  const { state, toggle } = useToggle(false);
  const [chartUnit, setChartUnit] = useState('DAY');
  const [dataSource, setDataSource] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectList, setSelectList] = useState([]);
  const [chartData, setChartData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [renderSelectList, setRenderSelectList] = useState([]);
  const [defaultTime, setDefaultTime] = useState([]);

  const mountNodeRef = useRef(null);
  const chartRef = useRef(null);

  const { dateRange2, chartsReflash } = states;

  useEffect(() => {
    const getChartData = async () => {
      if (campaignList.length) {
        setLoading(true);
        try {
          const chartDatas = await campaignsService.calcPresetCharts({
            id: campaignsData.id,
            flowIds: campaignList.map((item) => item?.campaignV2?.id).filter((v) => v),
            key: 'passed_count',
            type: 'SEGMENT_TREND',
            dateRange: dateRange2?.map((item) => {
              return {
                ...item,
                truncateAsDay: true
              };
            }),
            timeConfig: {
              timeTerm: chartUnit
            }
          });
          setChartData(chartDatas);
          setLoading(false);
        } catch {
          setLoading(false);
        }

        setDefaultTime(dateRange2);
      }
    };
    getChartData();
  }, [chartUnit, chartsReflash, campaignList]);

  useEffect(() => {
    if (!mountNodeRef.current) {
      return;
    }

    if (_.isEmpty(chartData) || chartRef.current) {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    }

    chartRef.current = new Chart({
      container: mountNodeRef.current,
      autoFit: true,
      height: 300
    });

    chartRef.current.data(chartData);
    chartRef.current.scale({
      date: {
        type: 'cat',
        range: [0, 1]
      },
      count: {
        nice: true
      }
    });

    chartRef.current.option('slider', {
      start: 0,
      end: 1
    });

    chartRef.current.tooltip({
      showCrosshairs: true,
      shared: false,
      enterable: true,
      domStyles: {
        'g2-tooltip': {
          maxHeight: '480px',
          overflowY: 'auto'
        }
      }
    });

    chartRef.current.axis('count', {
      label: {
        formatter: (val) => {
          return `${val}`;
        }
      }
    });

    chartRef.current.line().position('date*count').color('campaignName').shape('smooth');

    chartRef.current.point().position('date*count').color('campaignName').shape('circle');

    chartRef.current.render();
  }, [chartData]);

  const columns = [
    {
      title: '指标名称',
      dataIndex: 'name',
      className: 'drag-visible'
    },
    {
      title: '数据类型',
      dataIndex: 'dataType'
    },
    {
      title: '备注',
      dataIndex: 'memo'
    },
    {
      title: '目标',
      dataIndex: 'target',
      render: (text, record) => (
        <div>
          <span style={{ marginRight: '66px' }}>{record?.targetCompleteRate ? '有' : '无'}</span>
          <HolderOutlined />
        </div>
      )
    }
  ];

  useEffect(() => {
    const getNormTableData = async () => {
      setSelectedRowKeys([normData[0]?.key]);
      setRenderSelectList([normData[0]]);
      setSelectList([normData[0]]);
      setDataSource(normData);
    };

    getNormTableData();
  }, [normData]);

  const components = {
    body: {
      row: DraggableBodyRow
    }
  };

  const onUnitChange = (e) => {
    setChartUnit(e);
  };

  // 改变弹出框可见状态
  const changeVisible = (visible) => {
    dispatch({
      switchState: visible
    });
    toggle(visible);
  };

  const handleVisibleChange = (visible) => {
    dispatch({
      switchState: visible
    });
    toggle(visible);
  };
  const handleColumnsSubmit = () => {
    setRenderSelectList(selectList);
    changeVisible(false);
  };

  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      const dragRow = dataSource[dragIndex];

      setDataSource(
        update(dataSource, {
          $splice: [
            [dragIndex, 1],
            [hoverIndex, 0, dragRow]
          ]
        })
      );

      const dragDataSource = update(dataSource, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, dragRow]
        ]
      });

      if (_.find(selectedRowKeys, (o) => o === dragRow.key)) {
        const resultKey = [];
        const resultItem = [];
        dragDataSource.forEach((item) => {
          selectList.forEach((selectItem) => {
            if (item.key === selectItem.key) {
              resultKey.push(item.key);
              resultItem.push(item);
            }
          });
        });
        onSelectChange(resultKey, resultItem, true);
      }
    },
    [dataSource, selectedRowKeys]
  );

  const getTime = (obj) => {
    let time = dayjs();
    if (obj.type === 'ABSOLUTE') {
      time = dayjs(obj.timestamp);
    } else if (obj.type === 'RELATIVE') {
      const info = timeTerm.find((n) => n.value === obj.timeTerm);
      if (obj.isPast) {
        time = dayjs().subtract(obj.times, info.unit);
      } else {
        time = dayjs().add(obj.times, info.unit);
      }
    }
    return time.valueOf();
  };

  const onSelectChange = (selectedRowKeys, selectedRows, moveStatus) => {
    if (!moveStatus) {
      const _dataSource = _.cloneDeep(dataSource);
      const resultKey = [];
      const resultItem = [];
      _dataSource.forEach((item) => {
        selectedRows.forEach((selectItem) => {
          if (item.key === selectItem.key) {
            resultKey.push(item.key);
            resultItem.push(item);
          }
        });
      });
      setSelectedRowKeys(resultKey);
      setSelectList(resultItem);
    } else {
      setSelectedRowKeys(selectedRowKeys);
      setSelectList(selectedRows);
    }
  };

  const reset = () => {
    setSelectedRowKeys([normData[0]?.key]);
    setRenderSelectList([normData[0]]);
    setSelectList([normData[0]]);
    setDataSource(normData);
  };

  const rowSelection = {
    hideSelectAll: true,
    selectedRowKeys,
    onChange: onSelectChange
  };

  const renderNormTable = () => {
    return (
      <DndProvider backend={HTML5Backend}>
        <div className="normHeader">
          <div className="title">
            <span className="text">{t('dataCenter-az0B29GuyxUG')}</span>
            <span className="desc">{t('dataCenter-JMp2lULbFqi3')}</span>
          </div>
          <div className="reset" onClick={() => reset()}>
            {t('dataCenter-GCOp4Fpa0ipD')}
          </div>
        </div>
        <Table
          pagination={false}
          columns={columns}
          dataSource={dataSource}
          components={components}
          rowSelection={rowSelection}
          onRow={(_, index) => {
            const attr = {
              index,
              moveRow
            };
            return attr;
          }}
        />
        <div className="confirmBar">
          <Button onClick={() => changeVisible(false)} style={{ marginRight: '8px' }}>
            {t('dataCenter-xujEOCdXqerA')}
          </Button>
          <Button onClick={handleColumnsSubmit} type="primary">
            {t('dataCenter-k6rTBmiEDr0H')}
          </Button>
        </div>
      </DndProvider>
    );
  };

  return (
    <div className="composeDataTrend">
      <Spin spinning={loading}>
        <div>
          <div className="titleWrap">
            <div className="titleDate">
              <div className="title">{t('dataCenter-n82YzXx5EKY2')}</div>
              <div className="date">
                {defaultTime?.length
                  ? `${dayjs(getTime(defaultTime[0])).format('YYYY-MM-DD HH:mm:ss')} ~ ${dayjs(
                      getTime(defaultTime[1])
                    ).format('YYYY-MM-DD HH:mm:ss')}`
                  : null}
              </div>
            </div>
            {normData.length ? (
              <div className="switch">
                <Popover
                  overlayClassName="norm-setting-popup"
                  placement="bottomRight"
                  content={renderNormTable}
                  trigger="click"
                  open={state}
                  estroyTooltipOnHide
                  onOpenChange={handleVisibleChange}
                >
                  {/* <ColumnsSetting /> */}
                  {/* <Button type="text" onClick={() => changeVisible(true)} disabled={switchState}><SwapOutlined /><span className="normText">切换指标</span></Button> */}
                </Popover>
              </div>
            ) : null}
          </div>

          <div className="switchChartType">
            <div className="type">
              {normData.length ? (
                // ? <Radio.Group onChange={onTypeChange} value={chartType} className={renderSelectList.length === 1 ? 'radio-only-one' : ''}>
                <Radio.Group value="passed_count" className={renderSelectList.length === 1 ? 'radio-only-one' : ''}>
                  {/* {
                      renderSelectList.map((item, index) => (
                        <Radio.Button value={item?.key} key={index}>{item?.name}</Radio.Button>
                      ))
                    } */}
                  <Radio.Button value="passed_count">{t('dataCenter-B2W4KciFOmLr')}</Radio.Button>
                </Radio.Group>
              ) : null}
            </div>
            {/* {
              chartData.length ? <div className="unit">
                <Radio.Group onChange={onUnitChange} value={chartUnit}>
                  <Radio.Button value="HOUR">小时</Radio.Button>
                  <Radio.Button value="DAY">天</Radio.Button>
                  <Radio.Button value="WEEK">周</Radio.Button>
                  <Radio.Button value="MONTH">月</Radio.Button>
                </Radio.Group>
              </div> : null
            } */}

            {chartData.length ? (
              <div className="unit">
                <Select onChange={onUnitChange} value={chartUnit}>
                  <Option value="HOUR">{t('dataCenter-jOyLZmHss7Jf')}</Option>
                  <Option value="DAY">{t('dataCenter-rpMlVXoYKitd')}</Option>
                  <Option value="WEEK">{t('dataCenter-nMVPAdtTu8fw')}</Option>
                  <Option value="MONTH">{t('dataCenter-WIcwsQQdMIgk')}</Option>
                </Select>
              </div>
            ) : null}
          </div>

          <div>
            {chartData.length ? (
              <div className="container" ref={mountNodeRef} />
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ position: 'relative', top: '80px' }} />
            )}
          </div>
        </div>
      </Spin>
    </div>
  );
};

export default ComposeDataTrend;
