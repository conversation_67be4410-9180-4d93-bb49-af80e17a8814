import { Button, Calendar, Input, InputNumber, Select, Tabs } from 'antd';
import locale from 'antd/es/date-picker/locale/zh_CN';
import { createTime } from 'components/dayjs/timeTransformation';
import dayjs from 'dayjs';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { t } from 'utils/translation';
import { isPassObj, scorllUnit, timeTerm } from '../config';
import { DataContext } from '../context';

import 'dayjs/locale/zh-cn';
import './index.scss';

const { TabPane } = Tabs;

const hourArr = [];
const minuteArr = [];
const secondArr = [];

for (let i = 0; i < 60; i++) {
  let str = i.toString();
  if (str.length === 1) str = `0${str}`;
  if (i < 24) {
    hourArr.push(str);
  }
  minuteArr.push(str);
  secondArr.push(str);
}

const selectStyle = {
  // color: '#ff6800',
  // backgroundColor: '#e6f7ff'
  backgroundColor: '#fff1f0'
};

const { Option } = Select;
const initInfo = {
  type: 'ABSOLUTE',
  timeTerm: timeTerm[0].value,
  isPast: true
};

export default ({ save, data }) => {
  const { type, showTime } = useContext(DataContext);
  const [datatype] = useState(type && Array.isArray(type) ? type : ['ABSOLUTE', 'RELATIVE', 'NOW']);
  const [date, setDate] = useState(dayjs());
  const [hour, setHour] = useState('');
  const [minut, setMinut] = useState('');
  const [second, setSecond] = useState('');
  const [info, setInfo] = useState({ ...initInfo });
  const hourRef = useRef(null);
  const minutRef = useRef(null);
  const secondRef = useRef(null);

  useEffect(() => {
    const timestamp = data?.timestamp || dayjs().valueOf();
    setDate(dayjs(timestamp));
    const hourValue = dayjs(timestamp).format('HH');
    setHour(hourValue);
    hourRef.current.scrollTop = parseInt(hourValue) * scorllUnit;
    const minutValue = dayjs(timestamp).format('mm');
    setMinut(minutValue);
    minutRef.current.scrollTop = parseInt(minutValue) * scorllUnit;
    const secondValue = dayjs(timestamp).format('ss');
    setSecond(secondValue);
    secondRef.current.scrollTop = parseInt(secondValue) * scorllUnit;
    if (data) {
      setInfo({ ...data });
    } else {
      setInfo({ ...initInfo });
    }
  }, [data]);

  function onDateChange(value) {
    setDate(value);
  }

  const onSave = () => {
    if (info.type === 'ABSOLUTE') {
      info.timestamp = createTime(`${date.format('YYYY-MM-DD')} ${hour}:${minut}:${second}`);
    }
    save(info);
  };
  return (
    <div className="contentStyle">
      <Tabs activeKey={info.type} onChange={(val) => setInfo({ ...info, type: val })}>
        {datatype.includes('ABSOLUTE') && (
          <TabPane tab={t('dataCenter-FHZ273miY1D6')} key="ABSOLUTE">
            <div className="absoluteDateTime">
              <Calendar
                locale={locale}
                style={{ width: 280 }}
                value={date}
                fullscreen={false}
                onChange={onDateChange}
              />
              <div
                hidden={!showTime}
                style={{
                  backgroundColor: 'var(--ant-primary-color)',
                  width: 1,
                  margin: '0 5px'
                }}
              />
              <div hidden={!showTime} className="timeStyle">
                <div ref={hourRef} className="eachTime">
                  {hourArr.map((n) => (
                    <div
                      className="oneTime"
                      onClick={() => {
                        setHour(n);
                        hourRef.current.scrollTop = parseInt(n) * scorllUnit;
                      }}
                      style={n === hour ? selectStyle : {}}
                      key={n}
                    >
                      {n}
                    </div>
                  ))}
                </div>
                <div style={{ backgroundColor: '#f5f5f5', width: 1 }} />
                <div ref={minutRef} className="eachTime">
                  {minuteArr.map((n) => (
                    <div
                      className="oneTime"
                      onClick={() => {
                        setMinut(n);
                        minutRef.current.scrollTop = parseInt(n) * scorllUnit;
                      }}
                      style={n === minut ? selectStyle : {}}
                      key={n}
                    >
                      {n}
                    </div>
                  ))}
                </div>
                <div style={{ backgroundColor: '#f5f5f5', width: 1 }} />
                <div ref={secondRef} className="eachTime">
                  {secondArr.map((n) => (
                    <div
                      className="oneTime"
                      onClick={() => {
                        setSecond(n);
                        secondRef.current.scrollTop = parseInt(n) * scorllUnit;
                      }}
                      style={n === second ? selectStyle : {}}
                      key={n}
                    >
                      {n}
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="save">
              <Button type="primary" onClick={onSave} disabled={!date || !hour || !minut || !second} size="small">
                {t('dataCenter-XXjotuP9Tuhb')}
              </Button>
            </div>
          </TabPane>
        )}
        {datatype.includes('RELATIVE') && (
          <TabPane tab={t('dataCenter-3QC9quLF9L5C')} key="RELATIVE">
            <div className="relativeDateTime">
              <Input.Group style={{ width: 300 }} compact>
                <Select
                  value={JSON.stringify(info.isPast)}
                  onSelect={(val) => setInfo({ ...info, isPast: JSON.parse(val) })}
                  style={{ width: 100 }}
                >
                  {Object.entries(isPassObj).map((n) => (
                    <Option key={n[0]} value={n[0]}>
                      {n[1]}
                    </Option>
                  ))}
                </Select>
                <InputNumber min={0} value={info.times} onChange={(val) => setInfo({ ...info, times: val })} />
                <Select
                  value={info.timeTerm}
                  onSelect={(val) => setInfo({ ...info, timeTerm: val })}
                  style={{ width: 100 }}
                >
                  {timeTerm.map((n) => (
                    <Option key={n.value} value={n.value}>
                      {n.label}
                    </Option>
                  ))}
                </Select>
              </Input.Group>
            </div>
            <div className="save">
              <Button
                type="primary"
                onClick={onSave}
                disabled={(!info.times && info.times !== 0) || !info.timeTerm || info.isPast === undefined}
                size="small"
              >
                {t('dataCenter-XXjotuP9Tuhb')}
              </Button>
            </div>
          </TabPane>
        )}
        {datatype.includes('NOW') && (
          <TabPane tab={t('dataCenter-IvEssZKEEgH2')} key="NOW">
            <div className="nowDateTime">{t('dataCenter-5y7CjVLE7wpT')}</div>
            <div className="save">
              <Button type="primary" onClick={onSave} disabled={info.type !== 'NOW'} size="small">
                {t('dataCenter-XXjotuP9Tuhb')}
              </Button>
            </div>
          </TabPane>
        )}
      </Tabs>
    </div>
  );
};
