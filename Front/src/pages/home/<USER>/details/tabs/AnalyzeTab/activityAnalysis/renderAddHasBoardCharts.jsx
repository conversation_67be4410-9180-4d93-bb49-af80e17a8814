import { QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Input, Select, Table, Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import CampaignsService from 'service/CampaignsService';
import scenarioService from 'service/ScenarioService';
import UserService from 'service/UserService';
import AnalysisCenterService from 'service/analysisCenterService';
import { useDeepCompareEffect } from 'utils/customhooks';
import { MyIconV2 } from 'utils/myIcon';
import { t } from 'utils/translation';
import ComChart from '../../../../../analysisCenter/database/comChart';
import './index.scss';

const { Option } = Select;
const userService = new UserService();
const campaignsService = new CampaignsService();

const iconList = {
  RETENTION_ANALYSIS: 'icon-icon-retaion',
  NEW_FUNNEL: 'icon-a-icon-funnelplot',
  EVENT_ANALYSIS: 'icon-icon-shijianfenxi'
};
const chartFilterList = [
  {
    key: 'RETENTION_ANALYSIS',
    value: t('dataCenter-wG0vr37v3aaj')
  },
  {
    key: 'NEW_FUNNEL',
    value: t('dataCenter-sfXDlrgEfBgA')
  },
  {
    key: 'EVENT_ANALYSIS',
    value: t('dataCenter-Fj2oa3nAEHiA')
  },
  {
    key: 'TABLE,PIE,BAR,LINE,AREA,COLUMN,RADAR,DONUT,CARD,SCATTER,BUBBLE,FUNNEL',
    value: t('dataCenter-tYhIVdjdbAnO')
  }
];

export default ({ state, dispatch, onClose, save }) => {
  const { selectValue, saveloading, getLoading, addHasSelectValue, campaignsData } = state;
  // const { chartList, selectValue, refresh, loading: refreshLoading } = state;
  const { widgets = [] } = _.cloneDeep(selectValue);
  // let { widgets = [] } = addHasSelectValue;
  const [loading, setLoading] = useState(false);
  const [chartListRow, setChartListRow] = useState([]);
  // const [allChartList, setAllChartList] = useState([]);
  const [lastCheckedValue, setLastCheckedValue] = useState([]);
  const [scenarioData, setScenarioData] = useState([]);
  const [createData, setCreatData] = useState([]);

  const [scenarioVal, setScenarioVal] = useState(undefined);
  const [creatUserVal, setCreateUserVal] = useState(undefined);
  // const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  // const [saveList, setSaveList] = useState([]);

  const [pagination, setPagination] = useState({
    total: 0,
    current: 1,
    pageSize: 12
  });

  const [params, setParams] = useState({
    search: [
      {
        operator: 'EQ',
        propertyName: 'deptId',
        value: campaignsData?.deptId || window.getDeptId()
      }
    ],
    sorts: [
      {
        direction: 'desc',
        propertyName: 'updateTime'
      }
    ]
  });

  const [currentChart, setCurrentChart] = useState(null);

  const columns = [
    {
      title: t('dataCenter-ZGUcWlo2alSg'),
      dataIndex: 'name',
      ellipsis: true,
      render: (text, record) => (
        <div className="chartName">
          {record.chartType === 'RETENTION_ANALYSIS' ||
          record.chartType === 'NEW_FUNNEL' ||
          record.chartType === 'EVENT_ANALYSIS' ? (
            <MyIconV2
              type={iconList[record.chartType]}
              style={{
                fontSize: '24px',
                paddingRight: '10px',
                paddingTop: '4px',
                position: 'relative',
                top: '3px'
              }}
              className="chartIcon"
            />
          ) : (
            <MyIconV2
              type="icon-icon-box"
              style={{
                fontSize: '24px',
                paddingRight: '10px',
                paddingTop: '4px',
                position: 'relative',
                top: '3px'
              }}
              className="chartIcon"
            />
          )}
          {text}
        </div>
      )
    },
    {
      title: t('dataCenter-WJrTxUa0Zy9u'),
      // dataIndex: 'scenario.code',
      width: '120px',
      render: (val, record) => <div>{record.scenario ? record.scenario.code : t('dataCenter-CJsYKFKQgo57')}</div>
    },
    {
      title: t('dataCenter-cN8TrA3MQqVJ'),
      dataIndex: 'createUserName',
      width: '120px'
    },
    {
      title: t('dataCenter-b9Tvxf2Vr7qT'),
      dataIndex: 'updateTime',
      sorter: true,
      width: '200px',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    }
  ];
  // let lastCheckedValue = [];
  // _.forEach(checkedValues, (value) => {
  //   console.log(checkedValues);
  //   lastCheckedValue = _.concat(lastCheckedValue, value);
  // });

  useEffect(() => {
    const init = async () => {
      const scenarioList = await scenarioService.scenarioList([]);
      const createList = await userService.findAllName({ name: '' });
      setScenarioData([...scenarioList, { code: 'null', id: 'null', name: t('dataCenter-CJsYKFKQgo57') }]);
      setCreatData(createList);
    };
    init();
  }, []);
  const okHandle = () => {
    try {
      setLoading(true);
      // let _widgets = widgets || [];

      // lastCheckedValue.forEach(n => {
      //   _widgets.push({
      //     x: (_widgets.length * 4) % (12),
      //     y: widgets[widgets?.length - 1]?.y + 100 || 0,
      //     w: 4,
      //     h: 3,
      //     isResizable: true,
      //     i: `${n.id}`
      //   });
      // });
      // console.log(_widgets);
      // // widgets = _.concat(widgets, _widgets);
      // dispatch({
      //   addHasSelectValue: {
      //     ...addHasSelectValue,
      //     widgets: _widgets
      //   },
      //   chartModalVisible: false,
      //   chartList
      // });
      setLoading(false);
      save();
    } catch {
      setLoading(false);
    }
    // const addItem = {
    //   x: (widgets.length * 3) % (12),
    //   y: Infinity, // puts it at the bottom
    //   w: 3,
    //   h: 2,
    //   i: new Date().getTime().toString()
    // };
  };

  useDeepCompareEffect(() => {
    (async () => {
      try {
        setLoading(true);
        // const chartList = await campaignV2Service.getChartConfigAllList([]);
        const getCampainsList = await campaignsService.findCampaigns([
          {
            operator: 'EQ',
            propertyName: 'campaignsId',
            value: campaignsData.id
          }
        ]);
        // setAllChartList(chartList);
        const result = await AnalysisCenterService.getChartConfigList({
          size: pagination.pageSize,
          page: pagination.current,
          search: params.search,
          sorts: params.sorts
        });
        pagination.total = result.totalElements;
        setPagination({ ...pagination });
        const rowSelectRes = result.content.map((item) => {
          return {
            ...item,
            key: item.id,
            disabled:
              (item.scenario && !_.find(getCampainsList, (o) => o.campaignV2.scenario?.id === item.scenario.id)) ||
              !!_.find(widgets, (o) => {
                return o.i === item.id.toString();
              }),
            checked: !!_.find(widgets, (o) => {
              return o.i === item.id.toString();
            })
          };
        });

        // let selectKeysData = [];
        // rowSelectRes.forEach(item => {
        //   if (item.checked) {
        //     selectKeysData.push(item.key);
        //   }
        // });

        // setSelectedRowKeys(selectKeysData);
        setChartListRow(rowSelectRes);
        setLoading(false);
      } catch {
        setLoading(false);
      }
    })();
  }, [pagination, params]);

  const handleTableChange = (page, pageSize, sorter) => {
    const newParam = { ...pagination };
    newParam.current = page;
    newParam.pageSize = pageSize;
    const direction = sorter.order ? sorter.order.substr(0, sorter.order.length - 3) : 'desc';
    const propertyName = sorter.field
      ? Array.isArray(sorter.field)
        ? sorter.field.join('.')
        : sorter.field
      : 'updateTime';
    setParams({ ...params, sorts: [{ direction, propertyName }] });
    setPagination(newParam.current);
  };

  // const filterChartList = _.filter(chartList, item => {
  //   return item;
  // });

  const onClickCheckbox = (chart) => {
    setCurrentChart(null);
    setTimeout(() => {
      setCurrentChart(chart);
    }, 4);
  };

  const arrayUnique = (arr, name) => {
    const hash = {};
    return arr.reduce((acc, cru, index) => {
      if (!hash[cru[name]]) {
        hash[cru[name]] = { index };
        acc.push(cru);
      } else {
        acc.splice(hash[cru[name]].index, 1, cru);
      }
      return acc;
    }, []);
  };

  const onScenarioChange = (val) => {
    setScenarioVal(val);
    let _params = _.cloneDeep(params);
    _params = {
      ..._params,
      search: [
        ..._params.search,
        {
          operator: val === 'null' ? 'IS_NULL' : 'EQ',
          propertyName: 'scenario.id',
          value: val === 'null' ? '' : val || ''
        }
      ]
    };
    _params.search = arrayUnique(_params.search, 'propertyName');
    setPagination({ ...pagination, current: 1 });
    setParams(_params);
  };

  const onCreateUserChange = (val) => {
    setCreateUserVal(val);
    let _params = _.cloneDeep(params);
    _params = {
      ..._params,
      search: [
        ..._params.search,
        {
          operator: 'IN',
          propertyName: 'createUserId',
          value: val || ''
        }
      ]
    };
    _params.search = arrayUnique(_params.search, 'propertyName');
    setPagination({ ...pagination, current: 1 });
    setParams(_params);
  };

  // const onSearch = () => {
  //   let newParams = {
  //     ...params,
  //     search: [{
  //       operator: 'LIKE',
  //       propertyName: 'name',
  //       value: searchValue
  //     },
  //     {
  //       operator: scenarioVal === 'null' ? 'IS_NULL' : 'EQ',
  //       propertyName: 'scenario.id',
  //       value: scenarioVal === 'null' ? '' : scenarioVal || ''
  //     },
  //     {
  //       operator: 'IN',
  //       propertyName: 'createUserId',
  //       value: creatUserVal || ''
  //     }
  //     ]
  //   };
  //   setParams(newParams);
  // };

  const onChartTypeChange = (val) => {
    let _params = _.cloneDeep(params);
    _params = {
      ..._params,
      search: [
        ..._params.search,
        {
          operator: 'IN',
          propertyName: 'chartType',
          value: val || ''
        }
      ]
    };
    _params.search = arrayUnique(_params.search, 'propertyName');
    setPagination({ ...pagination, current: 1 });
    setParams(_params);
  };

  const rowSelection = {
    preserveSelectedRowKeys: true,
    // selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      const _widgets = widgets || [];

      // let res = [];
      // if (!selectedRows.checked) {
      //   selectedRows.filter(item => !item.checked).forEach(data => {
      //     res.push(data.key);
      //   });
      // }
      // setSelectedRowKeys(selectedRowKeys);
      // setSaveList(res);

      // selectedRows.forEach(n => {
      //   if (!n.checked) {
      //     _widgets.push({
      //       x: (_widgets.length * 4) % (12),
      //       y: widgets[widgets?.length - 1]?.y + 100 || 0,
      //       w: 4,
      //       h: 3,
      //       isResizable: true,
      //       i: `${n.id}`
      //     });
      //   }
      // });

      selectedRows.forEach((n) => {
        _widgets.push({
          x: (_widgets.length * 4) % 12,
          y: widgets[widgets?.length - 1]?.y + 100 || 0,
          w: 4,
          h: 4,
          isResizable: true,
          i: `${n.id}`
        });
      });
      // widgets = _.concat(widgets, _widgets);
      dispatch({
        addHasSelectValue: {
          ...addHasSelectValue,
          widgets: _widgets
        },
        chartModalVisible: false
        // chartList: allChartList
      });
      setLastCheckedValue(selectedRows);
    },
    onSelect: (record) => {
      onClickCheckbox(record);
    },
    getCheckboxProps: (record) => ({
      disabled: record.disabled
    })
  };

  const onInputSearch = _.debounce((e) => {
    let _params = _.cloneDeep(params);
    _params = {
      ..._params,
      search: [
        ..._params.search,
        {
          operator: 'LIKE',
          propertyName: 'name',
          value: e
        }
      ]
    };
    _params.search = arrayUnique(_params.search, 'propertyName');
    setPagination({ ...pagination, current: 1 });
    setParams(_params);
  }, 500);

  return (
    <div className="mainContent">
      <div className="contentWrap" style={{ display: 'flex' }}>
        <div className="contentLeft">
          <div className="filterWrap">
            <Input
              placeholder={t('dataCenter-ayd28pTMXX73')}
              onChange={(e) => onInputSearch(e.target.value)}
              style={{
                width: '280px',
                marginRight: '8px',
                marginBottom: '24px'
              }}
              allowClear
              suffix={<SearchOutlined style={{ color: 'rgba(0, 0, 0, 0.25)' }} />}
            />
            <div className=" absolute top-[4px] left-[320px] text-[rgba(0,0,0,.65)]">
              <Tooltip title={t('dataCenter-iRdltdHJW4ZT')}>
                <QuestionCircleOutlined />
              </Tooltip>
            </div>
            <div>
              <Select
                style={{ marginRight: '8px' }}
                placeholder={t('dataCenter-WJrTxUa0Zy9u')}
                value={scenarioVal}
                onChange={onScenarioChange}
                bordered={false}
                allowClear
              >
                {scenarioData.map((item) => (
                  <Option key={item.id} value={item.id.toString()}>
                    {`${item.name}${item.code === 'null' ? '' : `[${item.code}]`}`}
                  </Option>
                ))}
              </Select>

              <Select placeholder={t('dataCenter-VqHX0aQH4cVk')} bordered={false} allowClear onChange={onChartTypeChange}>
                {chartFilterList.map((item) => (
                  <Option key={item.key} value={item.key}>
                    {item.value}
                  </Option>
                ))}
              </Select>

              <Select
                placeholder={t('dataCenter-pXl1zr5BDuhg')}
                value={creatUserVal}
                onChange={onCreateUserChange}
                bordered={false}
                allowClear
              >
                {createData.map((item) => (
                  <Option key={item.id} value={item.id.toString()}>
                    {`${item.name}`}
                  </Option>
                ))}
              </Select>
            </div>
          </div>

          {/* <Button type="primary" style={{ margin: '0 8px 0 52px' }} onClick={onSearch}>查询</Button>
          <Button style={{ float: 'right' }} onClick={onAllClear}>清空</Button> */}
          {/* <div className="contentLeftHandle">
            <a onClick={() => { dispatch({ refresh: !refresh }); }}>刷新</a>
            <span>{`已选择图表   [${lastCheckedValue.length}/${50 - widgets.length}]`}</span>
          </div> */}
          {/* <Spin spinning={loading}>
          <Checkbox.Group value={lastCheckedValue} style={{ width: '100%' }} onChange={onCheckValueChange}>

            {filterChartList.map(n => <Row key={n.id}><Col style={{ marginBottom: 5 }} className={`${currentChart && currentChart.id === n.id ? 'active' : ''}`} span={24}>
              <Checkbox onClick={() => { onClickCheckbox(n); }} value={n.id}>{n.name}</Checkbox>
            </Col></Row>)}

          </Checkbox.Group>
          <div style={{ margin: '30px 10px', textAlign: 'right' }} hidden={chartList.length === 0}>
            <Pagination onChange={handleTableChange} current={pagination.current} pageSize={pagination.pageSize} total={pagination.total} />
          </div>
        </Spin> */}
          <Table
            columns={columns}
            dataSource={chartListRow}
            pagination={pagination}
            onChange={handleTableChange}
            loading={loading}
            rowSelection={{
              ...rowSelection
            }}
            scroll={{ y: 480 }}
          />
        </div>
        <div className="contentRight smWrap">
          <div className="rightWrapper">{currentChart && <ComChart info={currentChart} />}</div>
        </div>
      </div>

      <div className="optionWrap">
        <footer>
          <div>
            <Button onClick={onClose} style={{ marginRight: '8px' }}>
              {t('dataCenter-xujEOCdXqerA')}
            </Button>
            {/* <Button type="primary" onClick={okHandle} loading={saveloading || getLoading} disabled={!saveList.length}> */}
            <Button
              type="primary"
              onClick={okHandle}
              loading={saveloading || getLoading}
              disabled={!lastCheckedValue.length}
            >
              {t('dataCenter-YqzLKoLR86pw')}
            </Button>
          </div>
        </footer>
      </div>
    </div>
  );
};
