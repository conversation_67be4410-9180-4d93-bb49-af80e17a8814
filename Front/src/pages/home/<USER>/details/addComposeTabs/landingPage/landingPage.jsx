import { Button, Input, Table } from 'antd';
import React, { useEffect, useState } from 'react';

import { SearchOutlined } from '@ant-design/icons';

import { t } from 'utils/translation';
import '../addTabs.scss';

const pagination = {
  showTotal: (totals) => `${t('dataCenter-oVHOMzdWo1X7')} ${totals} ${t('dataCenter-GviPAx92Kah6')}`,
  showSizeChanger: false
};

const LandingPage = (props) => {
  const { state, dispatch, onClose, onSave } = props;
  const [selectedList, setSelectedList] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);

  const [param, setParam] = useState({
    page: 1,
    search: [
      {
        operator: 'EQ',
        propertyName: 'projectId',
        value: localStorage.getItem('projectId')
      },
      {
        operator: 'NE',
        propertyName: 'phase',
        value: 'DRAFT'
      }
    ],
    size: 20,
    sorts: [{ propertyName: 'updateTime', direction: 'desc' }]
  });

  const { saveLoading } = state;

  const columns = [
    {
      title: t('dataCenter-SNMKbUTLmAT0'),
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: t('dataCenter-YBU3RRGQg9ww'),
      dataIndex: 'link',
      key: 'link'
    }
  ];

  useEffect(() => {
    const init = async () => {
      setLoading(true);
      // const campainsList = await campaignsService.query3({ id: Number(campaignId), page: param });
      // const getCampainsList = await campaignsService.findCampaigns([{
      //   operator: 'EQ', propertyName: 'campaignsId', value: campaignId
      // }]);
      // let _campainsList = _.cloneDeep(campainsList);
      // _campainsList.content.forEach(item => {
      //   item.key = item.id;
      //   if (_.find(getCampainsList, (o) => o.campaignV2?.id === item.id) || item.phase === 'DRAFT') {
      //     item.disabled = true;
      //   }
      // });

      // pagination.total = campainsList.totalElements;
      // pagination.current = param.page;
      // pagination.pageSize = param.size;
      // setDataSource(_campainsList.content);
      const mockData = [
        {
          name: '观影福利月月领活动',
          link: 'https://datatist.cn/v/105424?cc=5g755crc',
          key: 1
        },
        {
          name: '暖冬福利大放送幸运抽奖大转盘',
          link: 'https://datatist.cn/v/105310?cc=QzgXNe8w',
          key: 2
        },
        {
          name: '签到畅玩我买单赢礼品手绘动物打卡签到',
          link: 'https://datatist.cn/gs/xRTZFXtT',
          key: 3
        },
        {
          name: '钱袋保卫战，好友助力赢大奖',
          link: 'https://datatist.cn/gs/lTHOXB1e',
          key: 4
        },
        {
          name: '“银行”小微企业贷款融资需求调查问卷',
          link: 'https://datatist.cn/v/105317?cc=bgAcmS4h',
          key: 5
        }
      ];
      setDataSource(mockData);
      setLoading(false);
    };

    init();
  }, [param]);

  const onTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setParam({ ...param });
  };

  const rowSelection = {
    preserveSelectedRowKeys: true,
    onChange: (selectedRowKeys, selectedRows) => {
      dispatch({
        addComposeData: selectedRows
      });
      setSelectedList(selectedRows);
    },
    getCheckboxProps: (record) => ({
      disabled: record.disabled
    })
  };

  return (
    <div className="processCanvasWrap">
      <div className="searchWrap">
        {/* <Input placeholder="搜索" onChange={(e) => onTextChange(e.target.value)} style={{ width: '100%' }} suffix={<SearchOutlined />} /> */}
        <Input.Group compact>
          {/* <Select style={{ width: '11%' }} value={searchSelectVal} onSelect={onSelect}>
            <Option value="name">名称</Option>
            <Option value="id"> ID</Option>
          </Select> */}

          <Input suffix={<SearchOutlined />} style={{ borderRadius: '6px' }} />
          {/* <Input onChange={(e) => onTextChange(e.target.value)} suffix={<SearchOutlined />} ref={searchRef} /> */}
        </Input.Group>
      </div>

      <div className="tableWrap">
        <Table
          columns={columns}
          onChange={onTableChange}
          dataSource={dataSource}
          pagination={pagination}
          loading={loading}
          rowSelection={{
            ...rowSelection
          }}
          scroll={{ y: 'calc(100vh - 292px)' }}
        />
      </div>

      <footer>
        <Button className="bor_ra-6" style={{ marginRight: '8px' }} onClick={onClose}>
          {t('dataCenter-xujEOCdXqerA')}
        </Button>
        <Button
          className="bor_ra-6"
          type="primary"
          onClick={onSave}
          loading={saveLoading || loading}
          disabled={!selectedList.length}
        >
          {t('dataCenter-XXjotuP9Tuhb')}
        </Button>
      </footer>
    </div>
  );
};

export default LandingPage;
