import React, { useEffect, useState } from 'react';
import { Spin, Descriptions, Typography } from 'antd';
import { Link, useParams } from 'react-router-dom';
import SmartUrlService from 'service/SmartUrlService';
import dayjs from 'dayjs';
import _ from 'lodash';
import BasicContent from './tabs/basicContent/basicContent';
import SmarkDetails from './tabs/smarkDetails/smarkDetails';
import './details.scss';

import { t } from 'utils/translation';

const { Text } = Typography;
const smartUrlService = new SmartUrlService();

const Details = () => {
  const { id } = useParams();
  const [data, setData] = useState(null);
  const [tabKey, setTabKey] = useState(1);
  const [count, setCount] = useState(null);

  useEffect(() => {
    const init = async () => {
      const data = await smartUrlService.getSmarkUrl(parseInt(id));
      const count = await smartUrlService.getClickCount({ taskId: data.id });
      setData(data);
      setCount(count.clickTotal);
    };
    init();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const calc = (day1, day2) => {
    const StartTime = Date.parse(_.replace(day1, /-/g, '/'));
    const EndTime = Date.parse(_.replace(day2, /-/g, '/'));
    const oneDay = 1000 * 60 * 60 * 24;
    const d1 = new Date(StartTime).getTime();
    const d2 = new Date(EndTime).getTime() + 86400000;
    const day = {
      remainTime: parseInt((d2 - d1) / oneDay),
      nowTime: new Date().getTime(),
      endTime: d2
    };

    if (day.nowTime > day.endTime) {
      return '0';
    }
    return `${parseInt((d2 - d1) / oneDay)}`;
  };

  return (
    <>
      <Spin tip="Loading..." spinning={false}>
        <div className="datails">
          <div className="smartUrlDetails">
            <div className="Breadcrumbs">
              <Link to="/aimarketer/home/<USER>/" style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
                {t('operationCenter-ZUZVPZ2kYTBp')}
              </Link>
              <span> / </span>
              <span>{t('operationCenter-5fVYuU9aoKZZ')}</span>
            </div>
            <div className="title">
              <h2>{data?.taskName}</h2>
              {/* <Button onClick={() => props.history.push(`/aimarketer/home/<USER>/edit/${getIdFromUrl()}`)}>编辑</Button> */}
            </div>
            <Descriptions column={4}>
              <Descriptions.Item label={t('operationCenter-b6R36rNnVw1y')}>{data?.id}</Descriptions.Item>
              <Descriptions.Item label={t('operationCenter-wIHBuOTLOwuh')}>{data?.createUserName}</Descriptions.Item>
              <Descriptions.Item label={t('operationCenter-MMDCRBxhU000')}>{data?.updateUserName}</Descriptions.Item>
              <Descriptions.Item label={t('operationCenter-BIRlJnjfEjiq')} className="status">
                <span
                  className="on"
                  style={{
                    background: data?.status === 'ENABLE' ? '#52C41A' : '#D9D9D9'
                  }}
                />
                {data?.status === 'ENABLE' ? t('operationCenter-YYXgG73MFtRz') : t('operationCenter-QolUQbqBxRQU')}
              </Descriptions.Item>
              <Descriptions.Item label={t('operationCenter-puloU6H6ROvD')}>
                <Text
                  style={{ width: 220 }}
                  ellipsis={{
                    tooltip: `${data?.effectiveStartTime}${t('operationCenter-Dzmn0Cmwu3Fz')}${data?.effectiveEndTime} ${t('operationCenter-w16rnsySFGfi')}${calc(
                      data?.effectiveStartTime,
                      data?.effectiveEndTime
                    )} ${t('operationCenter-bC3Eb6DM8mAA')}`
                  }}
                >
                  {data?.effectiveStartTime} {t('operationCenter-Dzmn0Cmwu3Fz')} {data?.effectiveEndTime} {t('operationCenter-w16rnsySFGfi')} {calc(data?.effectiveStartTime, data?.effectiveEndTime)} {t('operationCenter-bC3Eb6DM8mAA')}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label={t('operationCenter-WoPK9Ayiyoqz')}>
                {dayjs(data?.createTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label={t('operationCenter-99yiwr6NqMWM')}>
                {dayjs(data?.updateTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label={t('operationCenter-kVCH7mTFSJ8G')}>
                <Text style={{ width: 150 }} ellipsis={{ tooltip: data?.memo }}>
                  {data?.memo}
                </Text>
              </Descriptions.Item>
            </Descriptions>

            <p className="rule">
              <span
                className={tabKey === 1 ? 'selectSpan' : ''}
                onClick={() => {
                  setTabKey(1);
                }}
                style={{ marginRight: 20 }}
              >
                {t('operationCenter-lDnQhggCtRCi')}
              </span>
              <span
                className={tabKey === 2 ? 'selectSpan' : ''}
                onClick={() => {
                  setTabKey(2);
                }}
                style={{ marginRight: 20 }}
              >
                {t('operationCenter-tKIyOBtxR40G')}
              </span>
            </p>
          </div>
          <div>{tabKey === 1 ? <BasicContent data={data} count={count} /> : <SmarkDetails smarkData={data} />}</div>
        </div>
      </Spin>
    </>
  );
};

export default Details;
