import { useStore } from '@/store/share';
import { t } from 'utils/translation';

import CheckAuth from '@/utils/checkAuth';
import {
  Breadcrumb,
  Button,
  Descriptions,
  Drawer,
  Dropdown,
  Modal,
  Spin,
  Statistic,
  Tabs,
  Typography,
  message
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useReducer, useState } from 'react';
import { useParams } from 'react-router-dom';
import CampaignsService from 'service/CampaignsService';
import UserService from 'service/UserService';
import AddCampains from '../addCampains';
import ProcessCanvas from './addComposeTabs/processCanvas/processCanvas';
import CompaignsActivityAnalysis from './tabs/AnalyzeTab/activityAnalysis/index';
import Compose from './tabs/ComposeTab/compose';
import Situation from './tabs/SituationTab/situation';

import { getDeptPath } from '../../setting/dataPermissions/config';
import './details.scss';

const { Text } = Typography;
const { Countdown } = Statistic;

const campaignsService = new CampaignsService();
const userService = new UserService();

const reducer = (state, action) => ({ ...state, ...action });
const CampainsDetails = (props) => {
  const { id } = useParams();
  const [data, setData] = useState(null);
  const [tabKey, setTabKey] = useState('1');
  const [addComposeVisible, setAddComposeVisible] = useState(false);
  const [visible, setVisible] = useState(false);
  const [isShowEdit, setIsShowEdit] = useState({ status: false, idEdit: null });
  const [editValue, setEditValue] = useState({});
  const [Refresh, setRefresh] = useState(false);
  const [userId, setUserId] = useState(null);
  const [collectStatus, setCollectStatue] = useState(null);
  const [loading, setLoading] = useState(false);
  const [deptPath, setDeptPath] = useState(getDeptPath());
  const [projectShareStatus, setProjectShareStatus] = useState(false);
  const { dispatchShare: setStore } = useStore();

  const [state, dispatch] = useReducer(reducer, {
    addComposeData: [],
    saveParams: [],
    saveLoading: false,
    situationRefresh: false
  });

  useEffect(() => {
    const isActivity = localStorage.getItem('activityCache');
    const isActivityAnalysis = localStorage.getItem('isActivityAnalysis');
    if (isActivity || isActivityAnalysis) {
      setTabKey('2');
    }
    (async () => {
      const projectShareRes = await campaignsService.projectShareAuth({ projectId: localStorage.getItem('projectId') });
      const authShare = await userService.getActionGroupByRoleShare({
        id: Number(localStorage.getItem('roleId'))
      });

      if (projectShareRes && !_.isEmpty(authShare) && authShare.find((item) => item.code === 'STRATEGY').checked) {
        setProjectShareStatus(true);
        localStorage.setItem('projectShareStatus', 'ENABLE');
      } else {
        setProjectShareStatus(false);
        localStorage.setItem('projectShareStatus', 'DISABLE');
      }
    })();
    localStorage.removeItem('activityCache');
    localStorage.removeItem('isActivityAnalysis');
    return () => localStorage.removeItem('projectShareStatus');
  }, []);

  const getCollectionStatus = async (id, userId) => {
    const res = await campaignsService.findCondition({
      targetId: id,
      type: 'FAVORITE',
      targetType: 'CAMPAIGNS',
      loginId: userId
    });

    setCollectStatue(res);
  };

  useEffect(() => {
    let time = null;
    const init = async () => {
      // const data = await campaignsService.getCampaigns(parseInt(id));
      const data = await campaignsService.getCampaignsV2(
        {
          id: Number(id),
          deptId: window.getDeptId()
        },
        true
      );
      const userInfo = await userService.getCurrentUser();
      setDeptPath(getDeptPath(data.deptId));
      setUserId(userInfo.id);
      getCollectionStatus(id, userInfo.id);
      // saveRecordInfo(id, userInfo.id);
      setEditValue(data);
      setIsShowEdit({ status: true, idEdit: id });
      setData(data);
    };
    init();
    time = setInterval(init, 60000);
    return () => clearInterval(time);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [Refresh]);

  //   const calc = (day1, day2) => {
  //     let StartTime = Date.parse(_.replace(day1, /-/g, '/'));
  //     let EndTime = Date.parse(_.replace(day2, /-/g, '/'));
  //     const oneDay = 1000 * 60 * 60 * 24;
  //     const d1 = new Date(StartTime).getTime();
  //     const d2 = new Date(EndTime).getTime() + 86400000;
  //     let day = { remainTime: parseInt((d2 - d1) / oneDay), nowTime: new Date().getTime(), endTime: d2 };

  //     if (day.nowTime > day.endTime) {
  //       return '0';
  //     }
  //     return `${parseInt((d2 - d1) / oneDay)}`;
  //   };

  const saveRecordInfo = async (id, userId) => {
    setLoading(true);
    await campaignsService.saveUserOperationRecord({
      targetId: id,
      id: data?.recentUserOperationRecord?.id,
      targetType: 'CAMPAIGNS',
      type: 'RECENT',
      createUserId: userId,
      updateUserId: userId,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
    setLoading(false);
  };

  const saveCollect = async (val, userId) => {
    const { id } = val;
    try {
      await campaignsService.saveUserOperationRecord({
        targetId: id,
        targetType: 'CAMPAIGNS',
        type: 'FAVORITE',
        createUserId: userId,
        updateUserId: userId,
        createTime: dayjs().valueOf(),
        updateTime: dayjs().valueOf()
      });
      setRefresh(!Refresh);
      getCollectionStatus(id, userId);
      message.success(t('dataCenter-LFxqyM8cSkTl'));
    } catch (error) {
      console.error(error);
    }
  };

  const delCollect = async (val, userId) => {
    const { id } = val;
    try {
      await campaignsService.delByCondition({
        targetId: id,
        targetType: 'CAMPAIGNS',
        type: 'FAVORITE',
        loginId: userId
      });

      await campaignsService.saveUserOperationRecord({
        targetId: id,
        id: val?.recentUserOperationRecord?.id,
        targetType: 'CAMPAIGNS',
        type: 'RECENT',
        createUserId: userId,
        updateUserId: userId,
        createTime: dayjs().valueOf(),
        updateTime: dayjs().valueOf()
      });

      setRefresh(!Refresh);
      getCollectionStatus(id, userId);
      message.success(t('dataCenter-B6lvXXsxVR4F'));
    } catch (error) {
      console.error(error);
    }
  };

  const onDrawerClose = () => {
    setAddComposeVisible(false);
  };

  const onSave = async () => {
    const { addComposeData } = state;
    if (!addComposeData.length) {
      message.error(t('dataCenter-ddBN55hdgKVt'));
      return;
    }
    dispatch({ saveLoading: true });
    const params = addComposeData.map((item) => {
      return {
        campaignsId: Number(id),
        scenarioId: item.scenario?.id,
        campaignId: item.id
      };
    });

    try {
      await campaignsService.saveCampains(params);
      dispatch({
        saveParams: params
      });
      setAddComposeVisible(false);
      dispatch({ saveLoading: false });
      saveRecordInfo(data.id, userId);
      message.success(t('dataCenter-XGvv6yjVCgTE'));
    } catch (e) {
      setAddComposeVisible(false);
      dispatch({ saveLoading: false });
      message.error(e);
    }
  };

  const getGroupButtons = (phase) => {
    const commonItems = [
      {
        label: (
          <span
            onClick={() => onColumnActionClick(collectStatus ? 'cancelCollect' : 'collect', null, data)}
            style={{ display: 'inline-block', width: '100%' }}
          >
            {collectStatus ? t('dataCenter-xLvnNxjXKFdm') : t('dataCenter-cfTOoA5HtfRd')}
          </span>
        ),
        key: 'collect'
      },
      CheckAuth.checkAuth('analyzer_campaigns_share_view') &&
        projectShareStatus &&
        data?.createUserId === userId && {
          label: (
            <span
              onClick={() => {
                setStore({ shareOpen: true, shareInfo: { type: 'STRATEGY', id: data.id } });
              }}
            >
              {t('dataCenter-Us97mkjH08Rv')}
            </span>
          ),
          key: 'share'
        },
      CheckAuth.checkAuth('analyzer_campaigns_share_view') &&
        projectShareStatus &&
        data?.createUserId === userId && {
          label: (
            <span
              onClick={() => {
                setStore({ cooperateOpen: true, shareInfo: { type: 'STRATEGY', id: data.id } });
              }}
            >
              {t('dataCenter-GoO16YlUWypI')}
            </span>
          ),
          key: 'cooperateManage'
        }
    ];

    const terminatedItems = [
      CheckAuth.checkAuth('aim_campaigns_delete') && {
        label: <span onClick={() => onColumnActionClick('delete', null, data)}>{t('dataCenter-xmbk4yliJtB9')}</span>,
        key: 'delete'
      }
    ];

    const activeItems = [
      CheckAuth.checkAuth('aim_campaigns_edit') && {
        label: (
          <span
            onClick={() => onColumnActionClick('edit', null, data)}
            style={{ display: 'inline-block', width: '100%' }}
          >
            {t('dataCenter-Mxh6ORwjfxy8')}
          </span>
        ),
        key: 'edit'
      },
      CheckAuth.checkAuth('aim_campaigns_stop') && {
        label: (
          <span
            onClick={() => onColumnActionClick('stop', null, data)}
            style={{ display: 'inline-block', width: '100%' }}
          >
            {t('dataCenter-abWKlKA4FILq')}
          </span>
        ),
        key: 'stop'
      },
      CheckAuth.checkAuth('aim_campaigns_delete') && {
        label: (
          <span
            onClick={() => onColumnActionClick('delete', null, data)}
            style={{ display: 'inline-block', width: '100%' }}
          >
            {t('dataCenter-xmbk4yliJtB9')}
          </span>
        ),
        key: 'delete'
      }
    ];

    const items = phase === 'TERMINATED' ? [...commonItems, ...terminatedItems] : [...commonItems, ...activeItems];

    return (
      <>
        <CheckAuth code="aim_campaigns_edit">
          <Button
            type="primary"
            className="bor_ra-6"
            style={{ marginRight: '8px' }}
            onClick={() => setAddComposeVisible(true)}
          >
            {t('dataCenter-Jo9SXvtW0ozm')}
          </Button>
        </CheckAuth>

        <Dropdown menu={{ items }} className="bor_ra-6">
          <Button>{t('dataCenter-h37ccAmRnJwG')}</Button>
        </Dropdown>
      </>
    );
  };

  const onColumnActionClick = (key, feature, record) => {
    setEditValue(record);
    const { id } = record;
    if (key === 'edit') {
      setIsShowEdit({ status: true, idEdit: id });
      // changeVisible(true);
      setVisible(true);
    } else if (key === 'collect') {
      saveCollect(data, userId);
    } else if (key === 'cancelCollect') {
      delCollect(data, userId);
    } else if (key === 'delete') {
      Modal.confirm({
        title: t('dataCenter-xmbk4yliJtB9'),
        className: 'campainsDelWrap',
        content: <p className="confirmDelete">{t('dataCenter-FtDbOIPFB2EG')}{record.name}{t('dataCenter-vFoNQoB8mBBR')}</p>,
        okText: t('dataCenter-JfW4IvqvHB9a'),
        okType: 'danger',
        cancelText: t('dataCenter-xujEOCdXqerA'),
        async onOk() {
          await campaignsService.delCampaigns(data?.id);
          message.success(t('dataCenter-P4l2EuJg2KfA'));
          props.history.push('/aimarketer/home/<USER>');
          // setSearch([...searchRef.current]);
        },
        onCancel() {}
      });
    } else if (key === 'stop') {
      Modal.confirm({
        title: t('dataCenter-RKFpao6EtOou'),
        className: 'campainsDelWrap',
        content: <p className="confirmDelete">{t('dataCenter-Yg5zEf09TGAJ')}{record.name}{t('dataCenter-vFoNQoB8mBBR')}</p>,
        okText: t('dataCenter-DEcnj5BxqJ4d'),
        okType: 'danger',
        cancelText: t('dataCenter-zKkxyLwAsR94'),
        async onOk() {
          await campaignsService.stopCampaigns(data?.id);
          saveRecordInfo(id, userId);
          message.success(t('dataCenter-90PwsQx1hSUa'));
          setRefresh(!Refresh);
          // setSearch([...searchRef.current]);
        },
        onCancel() {}
      });
    }
  };

  const reflash = () => {
    setRefresh(!Refresh);
  };

  const onTabChange = (key) => {
    saveRecordInfo(id, userId);
    setTabKey(key);
  };

  const handleBack = () => {
    props.history.push('/aimarketer/home/<USER>');
  };

  const TabsItems = [
    {
      label: t('dataCenter-YTtvGkbNU0dZ'),
      key: '1',
      children: (
        <Situation
          campaignId={id}
          state={state}
          dispatch={dispatch}
          userId={userId}
          editValue={editValue}
          situationRefresh={state.situationRefresh}
          data={data}
        />
      ),
      disabled: loading
    },
    {
      label: t('dataCenter-0axEPDrcGyFw'),
      key: '2',
      children: (
        <CompaignsActivityAnalysis
          campaignId={id}
          state={state}
          dispatch={dispatch}
          history={props.history}
          campaignsData={data}
          userId={userId}
          situationRefresh={state.situationRefresh}
          editValue={editValue}
        />
      ),
      disabled: loading
    },
    {
      label: t('dataCenter-JnmaVuyaROdA'),
      key: '3',
      children: (
        <Compose
          campaignId={id}
          state={state}
          campaignsData={data}
          dispatch={dispatch}
          history={props.history}
          userId={userId}
          situationRefresh={state.situationRefresh}
        />
      ),
      disabled: loading
    }
  ];

  const composeTabsItems = [
    {
      label: t('dataCenter-L5mtfBn4Pa5O'),
      key: '1',
      children: (
        <ProcessCanvas
          dispatch={dispatch}
          state={state}
          onClose={onDrawerClose}
          onSave={onSave}
          campaignId={id}
          data={data}
        />
      )
    }
    // {
    //   label: '落地页',
    //   key: '2',
    //   children: (
    //     <LandingPage
    //       dispatch={dispatch}
    //       state={state}
    //       onClose={onDrawerClose}
    //       onSave={onSave}
    //       campaignId={id}
    //     />
    //   )
    // },
    // {
    //   label: '表单',
    //   key: '3',
    //   children: (
    //     <FormPage
    //       dispatch={dispatch}
    //       state={state}
    //       onClose={onDrawerClose}
    //       onSave={onSave}
    //       campaignId={id}
    //     />
    //   )
    // },
    // {
    //   label: '广告推广',
    //   key: '4',
    //   children: (
    //     <AdExpand
    //       dispatch={dispatch}
    //       state={state}
    //       onClose={onDrawerClose}
    //       onSave={onSave}
    //       campaignId={id}
    //     />
    //   )
    // }
  ];

  return (
    <>
      <Spin tip="Loading..." spinning={false}>
        <div className="campainsDatails">
          <div className="Details">
            <div className="Breadcrumbs">
              <Breadcrumb>
                <Breadcrumb.Item>
                  <a onClick={handleBack}>{t('dataCenter-7BE1IfMkoTRi')}</a>
                </Breadcrumb.Item>
                <Breadcrumb.Item>
                  <span>{t('dataCenter-6up6MpJih0Rv')}</span>
                </Breadcrumb.Item>
              </Breadcrumb>
            </div>
            <div className="title">
              <h2>
                {data?.name}
                {data?.status === 'RUNNING' ? (
                  <span className="countDown">
                    {t('dataCenter-DEBGuy77ILry')}：
                    <Countdown value={Date.now() + (data?.endTime - Date.now())} format={t('dataCenter-du4dcbYDKjvn')} />
                  </span>
                ) : null}
              </h2>
              {/* <Button onClick={() => props.history.push(`/aimarketer/home/<USER>/edit/${getIdFromUrl()}`)}>编辑</Button> */}
              <div className="btn-group">{getGroupButtons(data?.status, data)}</div>
            </div>
            <Descriptions column={2}>
              <Descriptions.Item label="ID">{data?.orderNo}</Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-uFbxstBNuEdA')}>{deptPath}</Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-cN8TrA3MQqVJ')}>{data?.createUserName}</Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-VOKP4acEnLPI')}>{data?.owner?.name}</Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-1TCxnKzxrhtJ')}>
                {dayjs(data?.createTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-M81OG6BvRbUi')}>{dayjs(data?.startTime).format('YYYY年MM月DD日')}</Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-kfEwnWA3RUwf')}>{data?.updateUserName}</Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-xKjzTfv9o44p')}>{dayjs(data?.endTime).format('YYYY年MM月DD日')}</Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-b9Tvxf2Vr7qT')}>
                {dayjs(data?.updateTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-mQJBoDmwOkhR')}>
                <Text style={{ width: 150 }} ellipsis={{ tooltip: data?.memo }}>
                  {data?.memo}
                </Text>
              </Descriptions.Item>
            </Descriptions>

            {/* <p className="rule">
              <span className={tabKey === 1 ? 'selectSpan' : ''} onClick={() => { setTabKey(1); }} style={{ marginRight: 20 }}>情况</span>
              <span className={tabKey === 2 ? 'selectSpan' : ''} onClick={() => { setTabKey(2); }} style={{ marginRight: 20 }}>分析</span>
              <span className={tabKey === 3 ? 'selectSpan' : ''} onClick={() => { setTabKey(3); }} style={{ marginRight: 20 }}>组成</span>
              <span className={tabKey === 4 ? 'selectSpan' : ''} onClick={() => { setTabKey(4); }} style={{ marginRight: 20 }}>任务</span>
            </p> */}
          </div>
          <section>
            {/* {tabKey === 1 ? <BasicContent data={data} count={count} /> : <SmarkDetails smarkData={data} />} */}
            {/* {renderTab(tabKey)} */}
            {/* <Tabs
              activeKey={tabKey}
              onChange={onTabChange}
              className="tabsWrap"
              items={renderTab}
            /> */}
            <Tabs activeKey={tabKey} items={TabsItems} onTabClick={onTabChange} />
          </section>
          {visible && (
            <AddCampains
              visible={visible}
              history={props.history}
              action={() => setVisible(false)}
              editValue={editValue}
              isShowEdit={isShowEdit}
              userId={userId}
              refresh={reflash}
              onClose={() => {
                setVisible(false);
                setIsShowEdit({ status: false, idEdit: null });
                setEditValue({});
              }}
            />
          )}

          <Drawer
            title={t('dataCenter-qpwGRPenDLNX')}
            width={960}
            open={addComposeVisible}
            destroyOnClose
            onClose={onDrawerClose}
            className="addComposeDrawer"
          >
            <Tabs defaultActiveKey="1" tabPosition="left" items={composeTabsItems} />
          </Drawer>

          {/* {visible && <AddCampains
            visible={visible}
            history={props.history}
            action={() => setVisible(false)}
            editValue={editValue}
            isShowEdit={isShowEdit}
            refresh={refresh}
            onClose={() => {
              setVisible(false);
              setIsShowEdit({ status: false, idEdit: null });
              setEditValue({});
            }}
          />} */}
        </div>
      </Spin>
    </>
  );
};

export default CampainsDetails;
