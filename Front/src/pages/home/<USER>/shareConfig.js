import dayjs from 'dayjs';
import { t } from 'utils/translation';

export default {
  // query表单配置
  elements: {
    name: {
      type: 'input',
      label: t('dataCenter-6FoXyfGEDqoH'),
      operator: 'LIKE',
      width: 12,
      componentOptions: {
        allowClear: true,
        placeholder: t('dataCenter-6FoXyfGEDqoH')
      }
    },
    ownerId: {
      type: 'select',
      label: t('dataCenter-xLSzBlw6Jjyt'),
      operator: 'EQ',
      width: 12,
      componentOptions: {
        allowClear: true,
        showSearch: true,
        placeholder: t('dataCenter-evJu3MfhuuVB'),
        filterOption: (input, option) => {
          return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        options: []
      }
    },
    status: {
      type: 'select',
      label: t('dataCenter-wWucwAIyZLHW'),
      operator: 'EQ',
      width: 12,
      componentOptions: {
        allowClear: true,
        showSearch: true,
        placeholder: t('dataCenter-evJu3MfhuuVB'),
        filterOption: (input, option) => {
          return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        options: []
      }
    },
    createUserId: {
      type: 'select',
      label: t('dataCenter-pXl1zr5BDuhg'),
      operator: 'EQ',
      width: 12,
      componentOptions: {
        allowClear: true,
        showSearch: true,
        placeholder: t('dataCenter-evJu3MfhuuVB'),
        filterOption: (input, option) => {
          return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        options: []
      }
    },
    startTime: {
      type: 'dateRange',
      label: t('dataCenter-iI8lGxP6hQV9'),
      labelWidth: 100,
      width: 12,
      operator: 'DATE_BETWEEN',
      componentOptions: {
        allowClear: true
      }
    },
    endTime: {
      type: 'dateRange',
      labelWidth: 100,
      label: t('dataCenter-lOGVSeUJALGN'),
      width: 12,
      operator: 'DATE_BETWEEN',
      componentOptions: {
        allowClear: true
      }
    },
    orderNo: {
      type: 'select',
      label: 'id',
      operator: 'IN',
      width: 12,
      componentOptions: {
        allowClear: true,
        placeholder: 'id',
        mode: 'tags'
      }
    }
  },

  normIconList: {
    join_count: 'icon-icon-mannumber',
    passed_count: 'icon-icon-mantime'
  },
  initPagination: {
    total: 0,
    current: 1,
    pageSize: 10
  },
  cardDragDownMenu: [
    {
      label: t('dataCenter-tQLdaSkiYVDr'),
      key: 'copyUrl'
    }
  ],
  cardDragStopDownMenu: [
    {
      label: t('dataCenter-tQLdaSkiYVDr'),
      key: 'copyUrl'
    }
  ],
  // 表单数据
  formData: {},
  columns: [
    {
      title: 'ID',
      dataIndex: 'campainId',
      width: 100
    },
    {
      title: t('dataCenter-6FoXyfGEDqoH'),
      dataIndex: 'name',
      ellipsis: true,
      width: 320,
      height: 100
    },
    {
      title: t('dataCenter-fS70aLJlRbOH'),
      dataIndex: 'dataType',
      width: 100
    },
    {
      title: t('dataCenter-51pXus9GlhiC'),
      dataIndex: 'businessTable',
      width: 120,
      render: (text) => {
        if (text) {
          return t('dataCenter-XnMbLtgqyz19');
        } else {
          return t('dataCenter-iLxFIhrzo2Z6');
        }
      }
    },
    {
      title: t('dataCenter-mQJBoDmwOkhR'),
      dataIndex: 'remark',
      width: 250,
      ellipsis: true
    },
    {
      title: t('dataCenter-pXl1zr5BDuhg'),
      dataIndex: 'createUserName',
      width: 100
    },
    {
      title: t('dataCenter-weonAZYCeMqd'),
      dataIndex: 'createTime',
      width: 200,
      // defaultSortOrder: 'descend',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: t('dataCenter-Y1yMsZlacWJ0'),
      dataIndex: 'updateUserName',
      width: 100
    },
    {
      title: t('dataCenter-bVdAQVxspxys'),
      dataIndex: 'updateTime',
      width: 200,
      sorter: (a, b) => a.updateTime - b.updateTime,
      defaultSortOrder: 'descend',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    }
  ],

  status: [
    {
      name: t('dataCenter-2A88tWvZAbIh'),
      text: t('dataCenter-2A88tWvZAbIh'),
      value: 'DRAFT',
      key: 'DRAFT',
      color: '#D9D9D9',
      background: 'rgba(217, 217, 217, 0.1)'
    },
    {
      name: t('dataCenter-ES0rJPaHSrk7'),
      text: t('dataCenter-ES0rJPaHSrk7'),
      value: 'RUNNING',
      key: 'RUNNING',
      color: '#37C377',
      background: 'rgba(55, 195, 119, 0.1)'
    },
    {
      name: t('dataCenter-KgATyeABEuyN'),
      text: t('dataCenter-KgATyeABEuyN'),
      value: 'ENDED',
      key: 'ENDED',
      color: '#1890FF',
      background: 'rgba(24, 144, 255, 0.1)'
    },
    {
      name: t('dataCenter-AopYirrlqOod'),
      text: t('dataCenter-AopYirrlqOod'),
      value: 'TERMINATED',
      key: 'TERMINATED',
      color: '#FAAD14',
      background: 'rgba(250, 173, 20, 0.1)'
    }
  ]
};
