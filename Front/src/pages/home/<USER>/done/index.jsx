import { Button, Form, Input, message, Modal, Select, Space, Table, Tooltip } from 'antd';
import TableSearch from 'components/bussinesscoms/tableSearch/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { t } from '@/utils/translation';
import MyToDoListService from 'service/myToDoListService';
import UserService from 'service/UserService';
import { elements, initHistoryParam, initParam } from '../config';

import myToDoListService from '../../../../service/myToDoListService';
import '../index.scss';

const userService = new UserService();

const { TextArea } = Input;

const pagination = {
  showTotal: (totals) => `共 ${totals} 条`,
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const hisToryPagination = {
  showTotal: (totals) => `共 ${totals} 条`,
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const statusList = {
  RUNNING: t('setting-gU5qEwJhl7n2'),
  FINISH: t('setting-9lgNjdtnk0E1'),
  REJECT: t('setting-CTSCJMH0wGYi'),
  BACKOUT: t('setting-kYjhgwaNgx1a'),
  CANCEL: t('setting-IV5TwpDxn4LT'),
  DRAFT: t('setting-w6FGx98Sm2Df')
};
const typeList = {
  CAMPAIGN: t('setting-gx1muiY6Z4Gg'),
  SEGMENT: t('setting-XdQBqKGRRvsg'),
  MARKET_WORKS: t('setting-cCSTeEYAkhxo'),
  LABEL: t('setting-G0rT12SNqeam')
};
const detailUrlList = {
  CAMPAIGN: '/aimarketer/home/<USER>',
  SEGMENT: '/aimarketer/home/<USER>/userGroup'
};

export default function MyToDoListDone({ props, tabKey }) {
  const {
    location: { state }
  } = props;
  const [form] = Form.useForm();

  const [dataSource, setDataSource] = useState([]);
  const [historyTableSource, setHistoryTableSource] = useState([]);
  const [userId, setUserId] = useState(undefined);
  const [loading, setLoading] = useState(false);
  const [param, setParam] = useState(_.cloneDeep(state?.paramDone || initParam));
  const [historyParam, setHistoryParam] = useState(initHistoryParam);
  //   const [tabKey, setTabKey] = useState(1);
  const [approveStatus, setApproveStatus] = useState({
    CAMPAIGN: false,
    SEGMENT: false
  });
  const [open, setOpen] = useState(false);
  const [logStatus, setLogStatus] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [userList, setUserList] = useState([]);

  const renderOptions = (record) => (
    <Space>
      <a style={{ marginRight: '8px' }} onClick={() => onColumnActionClick('detail', record)}>
        查看
      </a>
      <Button onClick={() => onColumnActionClick('log', record)} type="link">
        查看审批历史
      </Button>
    </Space>
  );
  const columns = [
    {
      title: '审批编号',
      key: 'processNo',
      dataIndex: 'processNo',
      sorter: true,
      width: 150
    },
    {
      title: '审批事项名称',
      key: 'name',
      width: 200,
      dataIndex: 'name',
      render: (text, record) => (
        <Tooltip title={text}>
          <a onClick={() => onColumnActionClick('detail', record)}>{text}</a>
        </Tooltip>
      )
    },
    {
      title: '类型',
      key: 'type',
      width: 150,
      dataIndex: 'type',
      render: (text) => <span>{typeList[text]}</span>
    },
    {
      title: '发起人',
      key: 'createUserName',
      width: 150,
      dataIndex: 'createUserName'
    },
    {
      title: '申请时间',
      width: 200,
      dataIndex: 'createTime',
      sorter: true,
      key: 'createTime',
      render: (text, record) => (record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: '审批状态',
      width: 100,
      key: 'status',
      render: (text) => <span>{statusList[text.status]}</span>
    },
    {
      title: '审批意见',
      key: 'opinion',
      width: 200,
      render: (record) => (
        <Tooltip title={record.opinion ? record.opinion : ''}>
          <span className="optionWrap">{record.opinion ? record.opinion : '-'}</span>
        </Tooltip>
      )
    },
    {
      title: '审批人',
      key: 'dealUserId ',
      width: 100,
      render: (record) => <span>{record.dealUserName ? record.dealUserName : '-'}</span>
    },
    {
      title: '审批时间',
      width: 200,
      dataIndex: 'dealTime',
      sorter: true,
      key: 'dealTime',
      render: (text, record) => (record.dealTime ? dayjs(record.dealTime).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: '操作',
      width: 200,
      fixed: 'right',
      render: (text, record) => <Space>{renderOptions(record)}</Space>
    }
  ];

  const columnsHistory = [
    {
      title: '发起人',
      key: 'createUserName',
      width: 150,
      dataIndex: 'createUserName',
      render: (text, record) => (
        <div>{(record.createTime && record.status === 'RUNNING') || record.status === 'BACKOUT' ? text : '-'}</div>
      )
    },
    {
      title: '申请时间',
      width: 200,
      dataIndex: 'createTime',
      sorter: true,
      key: 'createTime',
      render: (text, record) =>
        (record.createTime && record.status === 'RUNNING') || record.status === 'BACKOUT'
          ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '-'
    },
    {
      title: '审批状态',
      width: 100,
      key: 'status',
      render: (text) => <span>{statusList[text.status]}</span>
    },
    {
      title: '审批人',
      key: 'dealUserName ',
      width: 100,
      render: (record) => <span>{record.dealUserName ? record.dealUserName : '-'}</span>
    },
    {
      title: '审批意见',
      key: 'opinion',
      width: 240,
      render: (record) => (
        <Tooltip title={record.opinion ? record.opinion : ''}>
          <span className="optionWrap">{record.opinion ? record.opinion : '-'}</span>
        </Tooltip>
      )
    },
    {
      title: '审批时间',
      width: 200,
      dataIndex: 'dealTime',
      sorter: true,
      key: 'dealTime',
      render: (text, record) => (record.dealTime ? dayjs(record.dealTime).format('YYYY-MM-DD HH:mm:ss') : '-')
    }
  ];

  useEffect(() => {
    const init = async () => {
      const redata = await userService.listBy([]);
      const { id } = await userService.getCurrentUser();
      const campaignRes = await myToDoListService.getProcessAuthority({
        type: 'CAMPAIGN'
      });
      const segmentRes = await myToDoListService.getProcessAuthority({
        type: 'SEGMENT'
      });
      const marketRes = await myToDoListService.getProcessAuthority({
        type: 'MARKET_WORKS'
      });
      const labelRes = await myToDoListService.getProcessAuthority({
        type: 'LABEL'
      });
      setUserList(redata);
      setApproveStatus({
        CAMPAIGN: campaignRes,
        SEGMENT: segmentRes,
        MARKET_WORKS: marketRes,
        LABEL: labelRes
      });
      setUserId(id);
    };
    init();
  }, []);

  const handleTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setParam({ ...param });
  };

  const handleHistoryTableChange = (lastpagination, filtersArg, sorter) => {
    historyParam.page = lastpagination.current;
    historyParam.size = lastpagination.pageSize;
    if (sorter.field) {
      historyParam.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setHistoryParam({ ...historyParam });
  };

  useEffect(() => {
    const getTableData = async () => {
      setLoading(true);
      if (userId) {
        if (approveStatus.CAMPAIGN || approveStatus.SEGMENT || approveStatus.MARKET_WORKS || approveStatus.LABEL) {
          const processType = [];
          _.forIn(approveStatus, (value, key) => {
            if (value) {
              processType.push(key);
            }
          });
          const finalParam = _.cloneDeep(param);
          finalParam.search = [
            ...finalParam.search,
            {
              operator: 'EQ',
              propertyName: 'projectId',
              value: localStorage.getItem('projectId')
            },
            { operator: 'IN', propertyName: 'status', value: 'FINISH,REJECT' },
            {
              operator: 'IN',
              propertyName: 'type',
              value: processType.join(',')
            }
          ];

          const result = await MyToDoListService.query(finalParam);
          pagination.total = result.totalElements;
          pagination.current = param.page;
          pagination.pageSize = param.size;
          props.history.replace({
            state: { ...state, paramRunning: param, tabKey }
          });
          setDataSource(result.content);
        }
        setLoading(false);
      }
    };
    getTableData();
  }, [param, tabKey, userId, approveStatus]);

  useEffect(() => {
    const getHistoryTableData = async () => {
      setHistoryLoading(true);
      const finalParam = _.cloneDeep(historyParam);
      const result = await MyToDoListService.processHistory(finalParam);
      hisToryPagination.total = result.content.filter((item) => item.status !== 'DRAFT').totalElements;
      hisToryPagination.current = historyParam.page;
      hisToryPagination.pageSize = historyParam.size;
      setHistoryTableSource(result.content.filter((item) => item.status !== 'DRAFT'));
      setLoading(false);
      setHistoryLoading(false);
    };
    getHistoryTableData();
  }, [historyParam]);

  const showModal = () => {
    setOpen(true);
  };

  const handleCancel = () => {
    setOpen(false);
  };

  const showLogModal = async (id) => {
    setHistoryParam({
      ...historyParam,
      search: [{ operator: 'EQ', propertyName: 'instanceId', value: id }]
    });
    setLogStatus(true);
  };

  const hideLogModal = () => {
    setLogStatus(false);
  };

  const onSubmit = async () => {
    setConfirmLoading(true);
    const { id, name, opinion, status, businessId, definitionId, processNo, type } = form.getFieldValue();
    await myToDoListService.saveProcessInstance({
      businessId,
      id,
      name,
      dealUserId: userId,
      dealTime: dayjs().valueOf(),
      status,
      processNo,
      definitionId,
      opinion,
      projectId: localStorage.getItem('projectId'),
      type
    });

    message.success('审批成功');
    setConfirmLoading(false);
    handleCancel();
    setParam({ ...param });
  };

  const onColumnActionClick = async (key, record) => {
    const { id, businessId, definitionId, processNo, type, shareUrl } = record;
    if (key === 'back') {
      Modal.confirm({
        title: '撤回',
        className: 'backWrap',
        content: (
          <p className="backDesc">
            您将撤回审批流程：{record.name}
            ，撤销后，您可再次编辑后重新提交审批。
          </p>
        ),
        okText: '确认撤回',
        okType: 'primary',
        cancelText: '取消',
        async onOk() {
          await myToDoListService.saveProcessInstance({
            businessId,
            id,
            definitionId,
            processNo,
            status: 'BACKOUT',
            projectId: localStorage.getItem('projectId'),
            type
          });
          message.success('撤回成功');
          setParam({ ...param });
        },
        onCancel() {}
      });
    } else if (key === 'detail') {
      if (type === 'MARKET_WORKS') {
        const result = await myToDoListService.getAuthorization({
          loginId: userId,
          projectId: localStorage.getItem('projectId')
        });

        const newUrl =
          shareUrl.indexOf('?') >= 0 ? `${shareUrl}&Authorization=${result}` : `${shareUrl}?Authorization=${result}`;
        window.open(newUrl, '_blank');
      } else if (type === 'LABEL') {
        window.open(shareUrl, '_self');
      } else {
        props.history.push(`${detailUrlList[type]}/detail?id=${businessId}&definition=${true}&status=${true}`);
      }
    } else if (key === 'finish') {
      form.setFieldsValue({
        name: record.name,
        id: record.id,
        definitionId: record.definitionId,
        processNo: record.processNo,
        businessId,
        status: 'FINISH',
        opinion: null,
        type
      });
      showModal();
    } else if (key === 'reject') {
      form.setFieldsValue({
        name: record.name,
        businessId,
        definitionId: record.definitionId,
        processNo: record.processNo,
        id: record.id,
        status: 'REJECT',
        opinion: null,
        type
      });
      showModal();
    } else if (key === 'log') {
      showLogModal(record.id);
    }
  };
  return (
    <div className="todoList">
      {/* <header>
        <h1>待办事项</h1>
      </header> */}
      {/* <p className="rule">
        <span className={tabKey === 1 ? 'selectSpan' : ''} onClick={() => { setTabKey(1); }} style={{ marginRight: 20 }}>我发起的</span>
        {
          approveStatus ? <span className={tabKey === 2 ? 'selectSpan' : ''} onClick={() => { setTabKey(2); }} style={{ marginRight: 20 }}>待审批的</span> : null
        }

        <span className={tabKey === 3 ? 'selectSpan' : ''} onClick={() => { setTabKey(3); }} style={{ marginRight: 20 }}>已审批的</span>
      </p> */}
      <div className="search">
        <TableSearch
          elements={elements(3, userList)}
          onChange={(data) => setParam({ ...param, ...data })}
          span={8}
          initialValues={state?.paramDone?.search}
        />
      </div>
      <div className="content">
        <div className="tableWrap">
          <Table
            dataSource={dataSource}
            columns={columns}
            loading={loading}
            onChange={handleTableChange}
            rowKey="id"
            pagination={pagination}
            scroll={{ x: 1300 }}
          />
        </div>

        <Modal
          title="审批历史"
          className="processHistoryModalWrap"
          open={logStatus}
          destroyOnClose
          // okText="确认"
          // onOk={hideLogModal}
          confirmLoading={historyLoading}
          footer={null}
          onCancel={hideLogModal}
        >
          <div>
            <Table
              dataSource={historyTableSource}
              pagination={hisToryPagination}
              columns={columnsHistory}
              loading={historyLoading}
              onChange={handleHistoryTableChange}
              scroll={{ x: 400 }}
              rowKey="id"
            />
          </div>
        </Modal>

        <Modal
          title="审批流程"
          className="processModalWrap"
          open={open}
          destroyOnClose
          okText="确认通过"
          onOk={() => onSubmit()}
          confirmLoading={confirmLoading}
          onCancel={handleCancel}
        >
          <div className="processForm">
            <Form
              name="basic"
              layout="vertical"
              form={form}
              colon={false}
              confirmLoading={confirmLoading}
              initialValues={{
                name: null,
                id: null,
                opinion: null,
                status: null
              }}
            >
              <Form.Item label="审批事项名称" name="name">
                <Select disabled />
              </Form.Item>

              <Form.Item label="审批意见" name="opinion" rules={[{ max: 100, message: '最大长度限制为100位字符' }]}>
                <TextArea
                  placeholder="请输入审批意见"
                  autoSize={{
                    minRows: 2,
                    maxRows: 6
                  }}
                />
              </Form.Item>
            </Form>
          </div>
        </Modal>
      </div>
    </div>
  );
}
