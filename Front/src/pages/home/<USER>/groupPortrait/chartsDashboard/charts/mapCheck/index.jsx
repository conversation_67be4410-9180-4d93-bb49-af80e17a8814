import { Scene } from '@antv/l7';
import { DrillDownLayer } from '@antv/l7-district';
import { Mapbox } from '@antv/l7-maps';
import { Breadcrumb, Select, Spin, Table } from 'antd';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';

import UserGroupPortraitService from 'service/userGroupPortraitService';
import { t } from 'utils/translation';

const { Option } = Select;

const userGroupPortraitService = new UserGroupPortraitService();

const default_cn = [
  {
    columnName: 'COUNTRY',
    operator: 'EQ',
    value: '中国'
  }
];

const default_cn_column = 'PROVINCE';

const TGIColumns = [
  {
    key: 'order',
    dataIndex: 'order',
    title: t('portraitCenter-2xicdnTSEncu')
  },
  {
    key: 'name',
    dataIndex: 'name',
    title: t('portraitCenter-vzHO5LQ4PjjG')
  },
  {
    key: 'ratio',
    dataIndex: 'ratio',
    title: t('portraitCenter-d8SmuskC3RxO'),
    render: (text) => <span>{`${parseFloat((text * 100).toFixed(2))}%`}</span>
  },
  {
    key: 'tgi',
    dataIndex: 'tgi',
    title: 'TGI',
    render: (text) => <span>{`${parseFloat((text * 100).toFixed(2))}%`}</span>
  }
];

const columns = [
  {
    key: 'order',
    dataIndex: 'order',
    title: t('portraitCenter-2xicdnTSEncu')
  },
  {
    key: 'name',
    dataIndex: 'name',
    title: t('portraitCenter-vzHO5LQ4PjjG')
  },
  {
    key: 'ratio',
    dataIndex: 'ratio',
    title: t('portraitCenter-d8SmuskC3RxO'),
    render: (text) => <span>{`${parseFloat((text * 100).toFixed(2))}%`}</span>
  }
];

let drillLayer;
let scene;
let provinceData = [];
let cityDatas = [];

export default function MapCanvasCheck(props) {
  const { scenarioData, state, thema, segmentId, mapId, showTgi, segmentCount, force } = props;
  const { selectType, userGroupCheck, TGIChecked, scenarioList } = state;

  const [data, setData] = useState([]);
  const [selectValue, setSelectValue] = useState(7);
  const [loading, setLoading] = useState(false);
  const [searchList, setSearchList] = useState(default_cn);
  const [column, setColumn] = useState(default_cn_column);
  const [city, setCity] = useState(null);
  const [reflash, setReflash] = useState(false);
  const [TGIData, setTGIData] = useState([]);
  const [drill, setDrill] = useState(null);
  const [ratioTotal, setRatioTotal] = useState(null);

  const timer = useRef(null);

  useEffect(() => {
    // let provinceData = [];
    // provinceData = data;
    scene = new Scene({
      id: mapId,
      logoVisible: false,
      map: new Mapbox({
        pitch: 0,
        style: 'blank',
        center: [107.054293, 35.246265],
        zoom: 4.056
      })
    });

    scene.on('loaded', () => {
      drillLayer = new DrillDownLayer(scene, {
        provinceData,
        cityData: cityDatas,
        joinBy: ['NAME_CHN', 'name'],
        viewStart: 'Country',
        viewEnd: 'Province',
        label: false,
        province: {
          provinceStroke: '#8C8C8C',
          provinceStrokeWidth: 0.3,
          chinaNationalStroke: '#8C8C8C',
          chinaNationalWidth: 0.4
        },
        fill: {
          color: {
            field: 'ratio',
            values: (value) => {
              const maxRatio = _.maxBy(provinceData, (o) => {
                return o.ratio;
              })?.ratio;
              const minRatio = _.minBy(provinceData, (o) => {
                return o.ratio;
              })?.ratio;

              const level1 = value >= minRatio && value < minRatio + (maxRatio - minRatio) / 4;
              const level2 =
                value >= minRatio + (maxRatio - minRatio) / 4 &&
                value < minRatio + (maxRatio - minRatio) / 4 + (maxRatio - minRatio) / 4;
              const level3 =
                value >= minRatio + (maxRatio - minRatio) / 4 + (maxRatio - minRatio) / 4 &&
                value < minRatio + (maxRatio - minRatio) / 4 + (maxRatio - minRatio) / 4 + (maxRatio - minRatio) / 4;
              const level4 =
                value >= minRatio + (maxRatio - minRatio) / 4 + (maxRatio - minRatio) / 4 + (maxRatio - minRatio) / 4 &&
                value <= maxRatio;
              if (thema === 'content ') {
                if (level1) return '#D9E9FF';
                if (level2) return '#B0D0FF';
                if (level3) return '#87B3FF';
                if (level4) return '#5B8FF9';
                else return '#e6f7ff';
              } else {
                if (level1) return '#E6FFF2';
                if (level2) return '#B3F2D5';
                if (level3) return '#85E6BC';
                if (level4) return '#5AD8A6';
                else return '#E6FFF2';
              }
            }
          }
        },
        drillDownEvent: (properties) => {
          setColumn('CITY');
          const _searchList = _.cloneDeep(searchList);
          _searchList.push({
            columnName: 'PROVINCE',
            operator: 'EQ',
            value: properties.NAME_CHN
          });
          setCity(properties.NAME_CHN);
          setSearchList(_searchList);
        },
        popup: {
          enable: true,
          Html: (props) => {
            if (props.total > 0) {
              return `<div style="maxWidth:300"><span style="font-Weight:600">${
                props.NAME_CHN
              }</span><br/><span>${props.total}人</span><br><span>${(props.ratio * 100).toFixed(2)}%</span></div>`;
            } else {
              return `<div><span style="font-Weight:600">${props.NAME_CHN}</span><br/><span>暂无数据</span></div>`;
            }
          }
        }
      });
      setDrill(drillLayer);
    });
  }, [reflash]);

  const doRequest = async (data) => {
    try {
      const res = await userGroupPortraitService.calcSegmentChart2(data);
      if (!res) {
        // dispatch({ loading: false });
        setLoading(false);
      } else if (res.header.code === 0) {
        if (!_.isEmpty(res.body.chartData)) {
          const { dataList = [], total } = res.body.chartData;
          const _dataList = _.cloneDeep(dataList);
          const orderDataList = _dataList.map((item, index) => {
            return {
              order: index + 1,
              ...item
            };
          });
          setTGIData(orderDataList.slice(0, selectValue));
          // if (!ratioTotal) {
          //   setRatioTotal(total);
          // }
          setRatioTotal(total);
          setData(orderDataList);
          provinceData = _dataList;
          cityDatas = _dataList;

          try {
            drillLayer.updateData('province', provinceData);
            drillLayer.updateData('city', cityDatas);
          } catch (err) {
            console.error(err);
          }
          setLoading(false);
        }
        // dispatch({ loading: false });
      } else if (res.header.code === 210) {
        // dispatch({loading: false});
        timer.current = setTimeout(async () => {
          data.force = false;
          await doRequest(data);
        }, 3000);
      }
    } catch (error) {
      // responseData[selectedChart] = {rowList: [], columnList: []};
      // dispatch({ loading: false });
      setLoading(false);
    }
  };

  useEffect(() => {
    if (drillLayer) {
      const getChartsData = async () => {
        setLoading(true);
        if (scenarioData.length) {
          const params = {
            projectId: localStorage.getItem('projectId'),
            chartType: 'MAP',
            queryTable: 'USER',
            segmentId,
            columnName: column,
            queryType: 'SEGMENT_PORTRAIT',
            scenario: scenarioList,
            force: force?.status,
            searchList
          };
          doRequest(params);
          //   const charstData = await userGroupPortraitService.calcSegmentChart({
          //     projectId: localStorage.getItem('projectId'),
          //     chartType: 'MAP',
          //     queryTable: 'USER',
          //     segmentId,
          //     column,
          //     scenario: scenarioList,
          //     searchList
          //   });
          //   const { dataList = [], total } = charstData.chartData;
          //   let _dataList = _.cloneDeep(dataList);
          //   let orderDataList = _dataList.map((item, index) => {
          //     return {
          //       order: index + 1,
          //       ...item
          //     };
          //   });
          //   setTGIData(orderDataList.slice(0, selectValue));
          //   setRatioTotal(total);
          //   setData(orderDataList);
          //   provinceData = _dataList;
          //   cityDatas = _dataList;

          //   if (_dataList.length) {
          //     try {
          //       drillLayer.updateData('province', provinceData);
          //       drillLayer.updateData('city', cityDatas);
          //     } catch {}
          //   }
          //   setLoading(false);
        }
      };
      getChartsData();
      return () => {
        timer.current && clearTimeout(timer.current);
      };
    }
  }, [userGroupCheck, scenarioData, selectType, scenarioList, searchList, drill, force]);

  const reset = () => {
    setSearchList(default_cn);
    setCity(null);
    drillLayer.destroy();
    scene.destroy();
    setReflash(!reflash);
    setColumn(default_cn_column);
  };

  const onSelectChange = (e) => {
    setTGIData(data.slice(0, e));
    setSelectValue(e);
  };

  return (
    <div className="chartWrap">
      {data.length ? (
        <Select value={selectValue} className="countSwitch" onChange={onSelectChange}>
          <Option value={7}>{t('portraitCenter-y5ISQcSRrnEO', { count: 7 })}</Option>
          <Option value={15}>{t('portraitCenter-aEmS5ygwIHuh', { count: 15 })}</Option>
          <Option value={20}>{t('portraitCenter-XpRVt3WjR8qG', { count: 20 })}</Option>
          <Option value={25}>{t('portraitCenter-OuzCF7BPQp4G', { count: 25 })}</Option>
          <Option value={30}>{t('portraitCenter-VqTadgzVULav', { count: 30 })}</Option>
        </Select>
      ) : null}
      {/* {ratioTotal && scenarioTotalCount ? <span style={{ position: 'absolute', top: '35px', color: 'rgba(0, 0, 0, 0.45)' }}>有效人数占比 {(ratioTotal / scenarioTotalCount * 100).toFixed(0)}%</span> : null} */}
      {ratioTotal && segmentCount && !loading ? (
        <span
          style={{
            position: 'absolute',
            top: '35px',
            color: 'rgba(0, 0, 0, 0.45)'
          }}
        >
          {t('portraitCenter-3QwNQd8QMDW8')} parseFloat(((ratioTotal / segmentCount) * 100).toFixed(2))%
        </span>
      ) : null}
      <Spin spinning={loading}>
        <div className="mapWrap">
          <div className="mapItem">
            {city ? (
              <Breadcrumb>
                <Breadcrumb.Item onClick={reset}>{t('portraitCenter-8A0kJ5s2S1dT')}</Breadcrumb.Item>
                <Breadcrumb.Item>{city}</Breadcrumb.Item>
              </Breadcrumb>
            ) : null}
            <div />
            <div id={mapId} style={{ height: '100%', position: 'relative' }} />
            <div className="legend">
              <span
                style={{
                  display: 'block',
                  marginBottom: '8px',
                  color: 'rgba(0, 0, 0, 0.45)',
                  fontSize: '12px'
                }}
              >
                {t('portraitCenter-5SjWQgs9wdNb')}
              </span>
              <span
                style={{
                  display: 'flex',
                  width: '200px',
                  alignItems: 'center'
                }}
              >
                <span
                  style={{
                    marginRight: '8px',
                    color: 'rgba(0, 0, 0, 0.45)',
                    fontSize: '12px'
                  }}
                >
                  {t('portraitCenter-Blm4r95jMxHa')}
                </span>
                <span
                  style={
                    thema === 'content'
                      ? {
                          display: 'block',
                          background: 'linear-gradient(90deg, #BAE7FF 0%, #69C0FF 49.29%, #5B8FF9 100%)',
                          height: '8px',
                          width: '100px'
                        }
                      : {
                          display: 'block',
                          background: 'linear-gradient(90deg, #E6FFF2 0%, #85E6BC 49.29%, #5AD8A6 100%)',
                          height: '8px',
                          width: '100px'
                        }
                  }
                />
                <span
                  style={{
                    marginLeft: '8px',
                    color: 'rgba(0, 0, 0, 0.45)',
                    fontSize: '12px'
                  }}
                >
                  {t('portraitCenter-BP8UVHVIXRRy')}
                </span>
              </span>
            </div>
          </div>
          <div className="tgiItem">
            <Table
              dataSource={TGIData}
              columns={TGIChecked && showTgi ? TGIColumns : columns}
              pagination={false}
              scroll={{ y: 386 }}
              rowKey="order"
              size="middle"
            />
          </div>
        </div>
      </Spin>
    </div>
  );
}
