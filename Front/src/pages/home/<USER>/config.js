import { t } from '@/utils/translation';

export const elements = (tabKey, userList) => {
  if (tabKey === 2) {
    return [
      {
        type: 'input',
        name: 'processNo',
        label: t('setting-n8t0Xou8ummI'),
        operator: 'EQ'
        // componentConfig: {
        //   placeholder: '请输入图标名称'
        // },
        // options:[]
      },
      {
        type: 'input',
        name: 'name',
        label: t('setting-eWAzU92rgk2o'),
        operator: 'LIKE'
      },
      {
        type: 'dateRange',
        name: 'createTime',
        label: t('setting-QHJkuu9Un2QL'),
        operator: 'DATE_BETWEEN'
      },
      {
        name: 'type',
        type: 'select',
        label: t('setting-sKybqPej6fgD'),
        operator: 'EQ',
        options: [
          { key: 'CAMPAIGN', value: 'CAMPAIGN', label: t('setting-gx1muiY6Z4Gg') },
          { key: 'SEGMENT', value: 'SEGMENT', label: t('setting-XdQBqKGRRvsg') },
          { key: 'MARKET_WORKS', value: 'MARKET_WORKS', label: t('setting-cCSTeEYAkhxo') },
          { key: 'LABEL', value: 'LABEL', label: t('setting-G0rT12SNqeam') }
        ]
      }
    ];
  } else if (tabKey === 3) {
    return [
      {
        type: 'input',
        name: 'processNo',
        label: t('setting-n8t0Xou8ummI'),
        operator: 'EQ'
      },
      {
        type: 'input',
        name: 'name',
        label: t('setting-eWAzU92rgk2o'),
        operator: 'LIKE'
      },
      {
        type: 'dateRange',
        name: 'createTime',
        label: t('setting-QHJkuu9Un2QL'),
        operator: 'DATE_BETWEEN'
      },
      {
        name: 'status',
        type: 'select',
        label: t('setting-ipEut3kWr2zN'),
        operator: 'EQ',
        options: [
          { key: 'REJECT', value: 'REJECT', label: t('setting-CTSCJMH0wGYi') },
          { key: 'FINISH', value: 'FINISH', label: t('setting-G94JjR9yt14u') }
        ]
      },
      {
        name: 'type',
        type: 'select',
        label: t('setting-sKybqPej6fgD'),
        operator: 'EQ',
        options: [
          { key: 'CAMPAIGN', value: 'CAMPAIGN', label: t('setting-gx1muiY6Z4Gg') },
          { key: 'SEGMENT', value: 'SEGMENT', label: t('setting-XdQBqKGRRvsg') },
          { key: 'MARKET_WORKS', value: 'MARKET_WORKS', label: t('setting-cCSTeEYAkhxo') },
          { key: 'LABEL', value: 'LABEL', label: t('setting-G0rT12SNqeam') }
        ]
      },
      {
        name: 'dealUserId',
        type: 'select',
        label: t('setting-XYytmk7vjoHD'),
        operator: 'EQ',
        options: userList
          ? userList.map((item) => {
              return { key: item.id, value: item.id, label: item.name };
            })
          : []
      }
    ];
  }
  return [
    {
      type: 'input',
      name: 'processNo',
      label: t('setting-n8t0Xou8ummI'),
      operator: 'EQ'
      // componentConfig: {
      //   placeholder: '请输入图标名称'
      // },
      // options:[]
    },
    {
      type: 'input',
      name: 'name',
      label: t('setting-eWAzU92rgk2o'),
      operator: 'LIKE'
    },
    {
      type: 'dateRange',
      name: 'createTime',
      label: t('setting-QHJkuu9Un2QL'),
      operator: 'DATE_BETWEEN'
    },
    {
      name: 'status',
      type: 'select',
      label: t('setting-ipEut3kWr2zN'),
      operator: 'EQ',
      options: [
        { key: 'RUNNING', value: 'RUNNING', label: t('setting-gU5qEwJhl7n2') },
        { key: 'REJECT', value: 'REJECT', label: t('setting-CTSCJMH0wGYi') },
        { key: 'FINISH', value: 'FINISH', label: t('setting-G94JjR9yt14u') },
        { key: 'BACKOUT', value: 'BACKOUT', label: t('setting-kYjhgwaNgx1a') },
        { key: 'CANCEL', value: 'CANCEL', label: t('setting-IV5TwpDxn4LT') }
      ]
    },
    {
      name: 'type',
      type: 'select',
      label: t('setting-sKybqPej6fgD'),
      operator: 'EQ',
      options: [
        { key: 'CAMPAIGN', value: 'CAMPAIGN', label: t('setting-gx1muiY6Z4Gg') },
        { key: 'SEGMENT', value: 'SEGMENT', label: t('setting-XdQBqKGRRvsg') },
        { key: 'MARKET_WORKS', value: 'MARKET_WORKS', label: t('setting-cCSTeEYAkhxo') },
        { key: 'LABEL', value: 'LABEL', label: t('setting-G0rT12SNqeam') }
      ]
    }
  ];
};

export const initParam = {
  page: 1,
  search: [],
  size: 10,
  sorts: [{ propertyName: 'createTime', direction: 'desc' }]
};

export const initHistoryParam = {
  page: 1,
  search: [],
  size: 10,
  sorts: [{ propertyName: 'createTime', direction: 'asc' }]
};
