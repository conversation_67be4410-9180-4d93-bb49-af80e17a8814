import { t } from '@/utils/translation';

export const elements = (tabKey, userList, dictTypeList) => {
  if (tabKey === 2) {
    return [
      {
        type: 'input',
        name: 'approvalNo',
        label: t('setting-mikfpWLP1Axh'),
        operator: 'LIKE'
        // componentConfig: {
        //   placeholder: '请输入图标名称'
        // },
        // options:[]
      },
      {
        type: 'input',
        name: 'contentName',
        label: t('setting-BYaWcwVLx9VK'),
        operator: 'LIKE'
      },
      {
        type: 'dateRange',
        name: 'createTime',
        label: t('setting-TxuhBhIrNl8A'),
        operator: 'DATE_BETWEEN'
      },
      {
        name: 'contentType',
        type: 'select',
        label: t('setting-BQpV6odEe9du'),
        operator: 'EQ',
        options: dictTypeList.map((item) => {
          return { key: item.value, value: item.value, label: item.label };
        })
      },
      {
        name: 'createUserId',
        type: 'select',
        label: t('setting-gFkdVPngERQF'),
        operator: 'EQ',
        options: userList
          ? userList.map((item) => {
              return { key: item.id, value: item.id, label: item.name };
            })
          : []
      }
    ];
  } else if (tabKey === 3) {
    return [
      {
        type: 'input',
        name: 'approvalNo',
        label: t('setting-mikfpWLP1Axh'),
        operator: 'LIKE'
      },
      {
        type: 'input',
        name: 'contentName',
        label: t('setting-BYaWcwVLx9VK'),
        operator: 'LIKE'
      },
      {
        type: 'dateRange',
        name: 'createTime',
        label: t('setting-TxuhBhIrNl8A'),
        operator: 'DATE_BETWEEN'
      },
      {
        name: 'status',
        type: 'select',
        label: t('setting-cQXthQfXcNMb'),
        operator: 'EQ',
        options: [
          { key: 'REJECT', value: 'REJECT', label: t('setting-6FhEyEQ9TOfo') },
          { key: 'PASS', value: 'PASS', label: t('setting-POW1CSI95rFv') }
        ]
      },
      {
        name: 'contentType',
        type: 'select',
        label: t('setting-BQpV6odEe9du'),
        operator: 'EQ',
        options: dictTypeList.map((item) => {
          return { key: item.value, value: item.value, label: item.label };
        })
      },
      {
        name: 'approverId',
        type: 'select',
        label: t('setting-JTw3dPocWQh3'),
        operator: 'EQ',
        options: userList
          ? userList.map((item) => {
              return { key: item.id, value: item.id, label: item.name };
            })
          : []
      },
      {
        name: 'createUserId',
        type: 'select',
        label: t('setting-gFkdVPngERQF'),
        operator: 'EQ',
        options: userList
          ? userList.map((item) => {
              return { key: item.id, value: item.id, label: item.name };
            })
          : []
      }
    ];
  }
  return [
    {
      type: 'input',
      name: 'approvalNo',
      label: t('setting-mikfpWLP1Axh'),
      operator: 'LIKE'
      // componentConfig: {
      //   placeholder: '请输入图标名称'
      // },
      // options:[]
    },
    {
      type: 'input',
      name: 'contentName',
      label: t('setting-BYaWcwVLx9VK'),
      operator: 'LIKE'
    },
    {
      type: 'dateRange',
      name: 'createTime',
      label: t('setting-TxuhBhIrNl8A'),
      operator: 'DATE_BETWEEN'
    },
    {
      name: 'status',
      type: 'select',
      label: t('setting-cQXthQfXcNMb'),
      operator: 'EQ',
      options: [
        { key: 'RUNNING', value: 'RUNNING', label: t('setting-YYJxXNtyy2hw') },
        { key: 'REJECT', value: 'REJECT', label: t('setting-6FhEyEQ9TOfo') },
        { key: 'PASS', value: 'PASS', label: t('setting-POW1CSI95rFv') },
        { key: 'BACKOUT', value: 'BACKOUT', label: t('setting-o5W3oX5UsDx2') },
        { key: 'CANCEL', value: 'CANCEL', label: t('setting-keCa96EDRxaS') }
      ]
    },
    {
      name: 'contentType',
      type: 'select',
      label: t('setting-BQpV6odEe9du'),
      operator: 'EQ',
      options: dictTypeList.map((item) => {
        return { key: item.value, value: item.value, label: item.label };
      })
    },
    {
      name: 'createUserId',
      type: 'select',
      label: t('setting-gFkdVPngERQF'),
      operator: 'EQ',
      options: userList
        ? userList.map((item) => {
            return { key: item.id, value: item.id, label: item.name };
          })
        : []
    }
  ];
};

export const initParam = {
  page: 1,
  search: [],
  size: 10,
  sorts: [{ propertyName: 'createTime', direction: 'desc' }]
};

export const initHistoryParam = {
  page: 1,
  search: [],
  size: 10,
  sorts: [{ propertyName: 'createTime', direction: 'asc' }]
};
