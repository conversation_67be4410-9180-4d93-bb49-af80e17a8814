import { ExclamationCircleOutlined, FilterOutlined } from '@ant-design/icons';
import { useMount, useRequest } from '@umijs/hooks';
import QueryForList from 'components/bussinesscoms/queryforlist/index';
import ColumnActionCom from 'components/featurecoms/tableactioncom/index';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { t } from 'utils/translation';

import { connect } from 'react-redux';
import getMenuTitle from 'utils/menuTitle';

import CheckAuth from '@/utils/checkAuth';
import { Button, Modal, Table, message } from 'antd';
import _ from 'lodash';
import SmartUrlService from 'service/SmartUrlService';
import UserService from 'service/UserService';
import Log from 'utils/log';
import config from './config';
import './newList.scss';

const smartUrlService = new SmartUrlService();
const userService = new UserService();

const log = Log.getLogger('UserManage');
const { confirm } = Modal;

const stateToProps = (state) => {
  return {
    meunData: state.meunData.menus.main
  };
};

async function getTableList(params) {
  const data = await smartUrlService.list(params);
  return {
    total: data.totalElements,
    list: data.content
  };
}

function Example(props) {
  const history = useHistory();
  const [isFold, setIsFold] = useState(false);
  const [search, setSearch] = useState([]);
  const [elements, setElements] = useState(config.elements);
  const [columns, setColumns] = useState(config.columns);
  const searchRef = useRef(null);

  // const queryData = (data) => setSearch([...data]);
  const queryData = useCallback((data) => setSearch([...data]), []);

  useMount(() => {
    // 定义在里面，防止每次渲染都定义
    async function init() {
      log.debug('useMount', elements);
      const redata = await Promise.all([userService.listBy([])]);
      const _elements = _.cloneDeep(elements);
      _elements.updateUserId.componentOptions.options = _.map(redata[0], (item) => ({
        key: item.id,
        value: item.id,
        text: item.name
      }));
      _elements.createUserId.componentOptions.options = _.map(redata[0], (item) => ({
        key: item.id,
        value: item.id,
        text: item.name
      }));
      setElements(_elements);
    }
    init();
  });

  // 表格右侧的更多
  const actionColumn = {
    title: t('operationCenter-xqjLQJ9wQY6s'),
    className: 'td-set',
    width: 122,
    fixed: 'right',
    render: (text, record) => {
      const featureData = {
        // 特征
        view: {
          text: t('operationCenter-rt5p4QNGI8kf')
        },
        dropdownData: {
          edit: {
            text: t('operationCenter-FShbEobgS24r'),
            code: 'aim_short_link_edit'
          },
          status: {
            text: `${record.status === 'ENABLE' ? t('operationCenter-8HGLxqSF9eU6') : t('operationCenter-YYXgG73MFtRz')}`,
            code: 'aim_short_link_edit'
          },
          delete: {
            text: t('operationCenter-qpsgfpNrKBjd'),
            code: 'aim_short_link_edit'
          }
        }
      };
      return <ColumnActionCom closeDropdown featureData={featureData} record={record} onClick={onColumnActionClick} />;
    }
  };

  // 增加操作列
  useEffect(() => {
    const _columns = _.cloneDeep(columns);
    _columns.push(actionColumn);
    setColumns(_columns);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 使用封装的分页逻辑，并组装参数
  // eslint-disable-next-line no-unused-vars
  const { tableProps, params, refresh } = useRequest(
    ({ current, pageSize, sorter: s, filters: f }) => {
      const p = { page: current || 1, size: pageSize };
      if (s?.field && s?.order) {
        p.sorts = [
          {
            direction: _.includes(s?.order, 'desc') ? 'desc' : 'asc',
            propertyName: Array.isArray(s?.field) ? s?.field.join('.') : s?.field
          }
        ];
      } else if (!s) {
        p.sorts = [
          {
            direction: 'desc',
            propertyName: 'update_time'
          }
        ];
      } else {
        p.sorts = [];
      }
      if (f) {
        Object.entries(f).forEach(([filed, value]) => {
          p[filed] = value;
        });
      }

      const arr = [];
      let obj = {};
      if (p.sorts.length >= 1 && p.sorts[0].propertyName === 'updateTime') {
        p.sorts[0].propertyName = 'update_time';
      } else if (p.sorts.length >= 1 && p.sorts[0].propertyName === 'createTime') {
        p.sorts[0].propertyName = 'create_time';
      }

      _.map(search, (item) => {
        // 这里判断是否有搜索条件
        if (item.value && item.propertyName === 'updateTime') {
          arr.push({
            updateTimeStart: parseInt(item.value.split(',')[0]),
            updateTimeEnd: parseInt(item.value.split(',')[1])
          });
        } else if (item.value && item.propertyName === 'createTime') {
          arr.push({
            createTimeStart: parseInt(item.value.split(',')[0]),
            createTimeEnd: parseInt(item.value.split(',')[1])
          });
        } else if (item.value) {
          arr.push({ [item.propertyName]: item.value });
        }
      });
      _.map(arr, (item) => (obj = _.assign(obj, item)));
      return getTableList({ ...p, ...obj });
    },
    {
      paginated: true,
      defaultPageSize: 10,
      refreshDeps: [search]
    }
  );

  searchRef.current = search;

  // 表格操作列点击处理
  const onColumnActionClick = (key, feature, record) => {
    const { id, status } = record;

    if (key === 'view') {
      history.push(`/aimarketer/home/<USER>/detailURL/${id}`);
    } else if (key === 'edit') {
      history.push(`/aimarketer/home/<USER>/edit/${id}`);
    } else if (key === 'status') {
      const newStartTime = Date.parse(_.replace(record.effectiveStartTime, /-/g, '/'));
      let newEndTime = Date.parse(_.replace(record.effectiveEndTime, /-/g, '/'));
      if (newStartTime === newEndTime) newEndTime += 86400000;
      confirm({
        title: <span style={{ fontWeight: '600' }}>{status === 'ENABLE' ? t('operationCenter-8HGLxqSF9eU6') : t('operationCenter-YYXgG73MFtRz')}{t('operationCenter-IMMalqx8JOSW')}</span>,
        icon: <ExclamationCircleOutlined />,
        content: (
          <div>
            <div>
              {t('operationCenter-MO6GfqTnDRlC')}{status === 'ENABLE' ? t('operationCenter-8HGLxqSF9eU6') : t('operationCenter-YYXgG73MFtRz')}{t('operationCenter-jVDoHAmMYHct')}
              {record.taskName}
            </div>
            <div>
              {status === 'ENABLE' ? (
                <div>
                  {t('operationCenter-TBTdf6n7WJXO')}
                  <span style={{ color: 'red' }}>{t('operationCenter-q3uJLZyPpeuW')}</span>
                </div>
              ) : null}
            </div>
          </div>
        ),
        onOk() {
          const init = async () => {
            // 有效期内才可停用启用
            if (status === 'ENABLE' && newStartTime < new Date().getTime() && new Date().getTime() < newEndTime) {
              await smartUrlService.save({ ...record, status: 'DISABLE' });
            } else if (
              status === 'DISABLE' &&
              newStartTime < new Date().getTime() &&
              new Date().getTime() < newEndTime
            ) {
              await smartUrlService.save({ ...record, status: 'ENABLE' });
            } else {
              return message.error(t('operationCenter-zmdT4sEM1i0B'));
            }
            message.success(status === 'ENABLE' ? t('operationCenter-RHqh6vlgBdKB') : t('operationCenter-d1MUUmdJng0H'));
            const timeer = setTimeout(() => {
              clearTimeout(timeer);
              setSearch([...searchRef.current]);
            }, 1000);
          };
          init();
        },
        onCancel() {}
      });
    } else if (key === 'delete') {
      confirm({
        title: <span style={{ fontWeight: '600' }}>{t('operationCenter-CxhBNEbBPuMj')}</span>,
        icon: <ExclamationCircleOutlined />,
        content: (
          <div>
            <div>{t('operationCenter-Gi8PHWk5wrBc')}：{record.taskName}</div>
            <div>
              {t('operationCenter-AGL20NA4nGau')}<span style={{ color: 'red' }}>{t('operationCenter-6dOG4iSBduq0')}</span>，{t('operationCenter-DoxEf3DSihlp')}。
            </div>
          </div>
        ),
        onOk() {
          const init = async () => {
            await smartUrlService.deleteById(parseInt(id));
            message.success(t('operationCenter-kMMmR5XuTrv9'));
            const timeer = setTimeout(() => {
              clearTimeout(timeer);
              setSearch([...searchRef.current]);
            }, 1000);
          };
          init();
        }
      });
    }
  };

  const smartUrlCreate = () => {
    history.push('/aimarketer/home/<USER>/edit/');
  };

  return (
    <div className="smartUrlList">
      <header>
        <div className="title">{getMenuTitle(props.meunData, props.history.location.pathname)}</div>
        <div className="rightSide">
          <Button onClick={() => setIsFold(!isFold)} icon={<FilterOutlined />} style={{ borderRadius: 6 }}>
            {t('operationCenter-qMU6HwLuuSWa')}
          </Button>
          <CheckAuth code="aim_short_link_edit">
            <Button onClick={smartUrlCreate} style={{ marginLeft: '8px', borderRadius: 6 }} type="primary">
              {t('operationCenter-VcHhTKLrTALv')}
            </Button>
          </CheckAuth>
        </div>
      </header>
      <QueryForList elements={elements} onQuery={queryData} show={isFold} />
      <main>
        <div className="Board">
          <div className="title">
            <span>{t('operationCenter-or4l25zOa0Yq')}</span>
          </div>
          <Table
            columns={columns}
            rowKey="id"
            {...tableProps}
            pagination={{
              ...tableProps.pagination,
              showQuickJumper: true,
              showSizeChanger: true,
              showLessItems: true,
              pageSizeOptions: ['10', '20', '50'],
              showTotal: (e) => `${t('operationCenter-RdaIy2Rxi4zi')} ${e} ${t('operationCenter-y4po0SGRx01R')}`
            }}
            scroll={{ x: 1300 }}
          />
        </div>
      </main>
    </div>
  );
}
export default connect(stateToProps)(Example);
