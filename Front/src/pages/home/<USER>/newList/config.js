import { Tooltip } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React from 'react';
import { Link } from 'react-router-dom';
import { t } from 'utils/translation';

function calc(day1, day2) {
  const StartTime = Date.parse(_.replace(day1, /-/g, '/'));
  const EndTime = Date.parse(_.replace(day2, /-/g, '/'));
  const oneDay = 1000 * 60 * 60 * 24;
  const d1 = new Date(StartTime).getTime();
  const d2 = new Date(EndTime).getTime() + 86400000;
  const day = {
    remainTime: parseInt((d2 - d1) / oneDay),
    nowTime: new Date().getTime(),
    endTime: d2
  };

  if (day.nowTime > day.endTime) {
    return t('operationCenter-BuEH3WrXuaYW');
  }
  return `${parseInt((d2 - d1) / oneDay)}${t('operationCenter-qUiGDZdvyRm3')}`;
}

export default {
  // query表单配置
  elements: {
    taskName: {
      type: 'input',
      label: t('operationCenter-MR87FyokSsX9'),
      operator: 'LIKE',
      width: 8,
      componentOptions: {
        allowClear: true,
        placeholder: t('operationCenter-0beBeBkYfg6j')
      }
    },
    taskId: {
      type: 'input',
      label: t('operationCenter-R4phepUOO5tG'),
      operator: 'EQ',
      width: 8,
      componentOptions: {
        allowClear: true,
        placeholder: t('operationCenter-0beBeBkYfg6j')
      }
    },
    updateUserId: {
      type: 'select',
      label: t('operationCenter-jAsJxfvT4Bof'),
      operator: 'EQ',
      width: 8,
      componentOptions: {
        allowClear: true,
        showSearch: true,
        placeholder: t('operationCenter-SBQg9r0P04gE'),
        filterOption: (input, option) => {
          return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        options: []
      }
    },
    updateTime: {
      type: 'dateRange',
      label: t('operationCenter-JtUZmVSzLSZb'),
      width: 8,
      operator: 'DATE_BETWEEN',
      componentOptions: {
        allowClear: true,
        format: 'YYYY-MM-DD'
      }
    },
    createTime: {
      type: 'dateRange',
      label: t('operationCenter-WoPK9Ayiyoqz'),
      width: 8,
      operator: 'DATE_BETWEEN',
      componentOptions: {
        allowClear: true,
        format: 'YYYY-MM-DD'
      }
    },
    createUserId: {
      type: 'select',
      label: t('operationCenter-wIHBuOTLOwuh'),
      operator: 'EQ',
      width: 8,
      componentOptions: {
        allowClear: true,
        showSearch: true,
        placeholder: t('operationCenter-SBQg9r0P04gE'),
        filterOption: (input, option) => {
          return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
        options: []
      }
    }
  },
  // 表单数据
  formData: {},
  columns: [
    {
      title: t('operationCenter-R4phepUOO5tG'),
      dataIndex: 'id',
      width: 120
    },
    {
      title: t('operationCenter-MR87FyokSsX9'),
      dataIndex: 'taskName',
      width: 280,
      ellipsis: {
        showTitle: false
      },
      render: (text, val) => (
        <Link to={`/aimarketer/home/<USER>/detailURL/${val.id}`}>
          <Tooltip placement="topLeft" title={text}>
            {text}
          </Tooltip>
        </Link>
      )
    },
    {
      title: t('operationCenter-r52kYrJ0AY6V'),
      dataIndex: 'endTime',
      width: 320,
      render: (text, val) =>
        `${val?.effectiveStartTime} ~ ${val?.effectiveEndTime} ${t('operationCenter-w16rnsySFGfi')}${calc(
          val.effectiveStartTime,
          val.effectiveEndTime
        )}`
    },
    {
      title: t('operationCenter-BIRlJnjfEjiq'),
      dataIndex: 'status',
      sorter: true,
      width: 120,
      render: (text) => (
        <div className="status">
          <span className={getStatus(text)} />
          {statusMap[text]}
        </div>
      )
    },
    {
      title: t('operationCenter-wIHBuOTLOwuh'),
      dataIndex: 'createUserName',
      width: 120
    },
    {
      title: t('operationCenter-WoPK9Ayiyoqz'),
      dataIndex: 'createTime',
      width: 200,
      sorter: (a, b) => a.createTime - b.createTime,
      // defaultSortOrder: 'descend',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: t('operationCenter-jAsJxfvT4Bof'),
      dataIndex: 'updateUserName',
      width: 120
    },
    {
      title: t('operationCenter-MD5nfOy0C1jA'),
      dataIndex: 'updateTime',
      width: 200,
      sorter: (a, b) => a.updateTime - b.updateTime,
      defaultSortOrder: 'descend',
      render: (text) => text && dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    }
  ]
};
const statusMap = {
  ENABLE: t('operationCenter-YYXgG73MFtRz'),
  DISABLE: t('operationCenter-8HGLxqSF9eU6')
};

const getStatus = (text) => {
  if (!text) return '';
  if (text === 'ENABLE') return 'circle enable';
  if (text === 'DISABLE') return 'circle disable';
};
