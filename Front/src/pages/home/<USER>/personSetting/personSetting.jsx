import React, { useState } from 'react';
import { Layout, Menu } from 'antd';
import Person from '../person/list';
import ThemeSetting from '../themeSetting/themeSetting';
import { settingList } from './configs';
import { t } from '@/utils/translation';

import './personSetting.scss';

const { Content, Sider } = Layout;

const contentList = {
  accoutSetting: <Person />,
  themeSetting: <ThemeSetting />
};

export default function PersonSetting() {
  const [menuKey, setMenuKey] = useState(['accoutSetting']);

  const onMenuClick = (e) => {
    setMenuKey([e.key]);
  };

  return (
    <Layout className="themeLayout">
      <Sider breakpoint="lg" collapsedWidth="0">
        <div className="sideTitle">{t('setting-dIVc6jvCGdzS')}</div>
        <Menu
          mode="inline"
          selectedKeys={menuKey}
          style={{ height: '100%', background: '#fff', width: 240 }}
          onClick={onMenuClick}
        >
          {settingList.map((item) => (
            <Menu.Item key={item.key}>{item.label}</Menu.Item>
          ))}
        </Menu>
      </Sider>

      <Layout>
        <Content>
          <div
            className="site-layout-background"
            style={{
              padding: 24,
              minHeight: 360
            }}
          >
            {contentList[menuKey]}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
}
