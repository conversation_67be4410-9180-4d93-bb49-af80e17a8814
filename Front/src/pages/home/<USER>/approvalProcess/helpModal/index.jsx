import {
  ArrowRightOutlined,
  AuditOutlined,
  FormOutlined,
  GroupOutlined,
  LogoutOutlined,
  PlayCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import HelpImg1 from 'assets/images/helpImg1.png';
import HelpImg2 from 'assets/images/helpImg2.png';
import HelpImg3 from 'assets/images/helpImg3.png';
import HelpImg4 from 'assets/images/helpImg4.png';
import HelpImg5 from 'assets/images/helpImg5.png';

import { Drawer, Image } from 'antd';
import React from 'react';
import { t } from '@/utils/translation';

const HelpModal = (props) => {
  const { helpOpen, onClose } = props;
  return (
    <Drawer open={helpOpen} onClose={onClose} title={t('setting-VKhwiPx4tFKP')} width={880} className="helpDrawer">
      <div className="flex h-full flex-col">
        <div style={{ marginTop: 8, marginBottom: 40 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            {t('setting-rqZt6BFMSQ4M')}
          </div>
          <div className="flex flex-col gap-[16px]">
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#1EC78B', borderRadius: '50%', width: 24, height: 24 }}
              >
                <PlayCircleOutlined />
              </div>
              <div>{t('setting-1PkwDD6qaKur')}</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#1EC78B', borderRadius: '50%', width: 24, height: 24 }}
              >
                <LogoutOutlined />
              </div>
              <div>{t('setting-XpqgQbjkm7Zl')}</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#1EC78B', borderRadius: 6, width: 24, height: 24 }}
              >
                <UserOutlined />
              </div>
              <div>{t('setting-rndqOM0MlAyc')}</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#1890FF', borderRadius: 6, width: 24, height: 24 }}
              >
                <AuditOutlined />
              </div>
              <div>{t('setting-OMu2FcCtjdw0')}</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#FAAD14', borderRadius: 6, width: 24, height: 24 }}
              >
                <FormOutlined />
              </div>
              <div>{t('setting-qZTCwSraY8gA')}</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#FAAD14', borderRadius: 6, width: 24, height: 24 }}
              >
                <GroupOutlined />
              </div>
              <div>{t('setting-HEAsaw5vYfIA')}</div>
            </div>
            <div className="flex items-center gap-[8px]">
              <div
                className="flex justify-center items-center text-[#fff] text-[14px]"
                style={{ backgroundColor: '#000000A6', borderRadius: '50%', width: 24, height: 24 }}
              >
                <ArrowRightOutlined />
              </div>
              <div>{t('setting-eMTZywrH1JuA')}</div>
            </div>
          </div>
        </div>
        <div style={{ marginTop: 8, marginBottom: 40 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            {t('setting-9RUloodHzQiy')}
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg1} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16 }}>
            <div>{t('setting-AXPxyNxOZwxq')}</div>
            <div>{t('setting-atpk8lKZ2OYU')}</div>
            <div>{t('setting-zUZt4Ty3076Q')}</div>
            <div>{t('setting-K71hz2zpWQHH')}</div>
            <div>4. 连线要正确：除开始和结束节点只有一条连线，其余节点至少有“进入“和”输出“两条线；</div>
          </div>
        </div>
        <div style={{ marginTop: 8, marginBottom: 40 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            {t('setting-pPj1VnUyB1Et')}
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg2} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16 }}>
            <div>{t('setting-42nnq9AtjQm6')}</div>
            <div>{t('setting-jSwsuEZJBuIj')}</div>
            <div>3. 每种类型内部，各类型之间都是”或“的关系，即为“或签”；</div>
          </div>
        </div>
        <div style={{ marginTop: 8, marginBottom: 40 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            {t('setting-Za7JBfSvS8M0')}
          </div>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            {t('setting-5bRcVjMcqA7o')}
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg3} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16 }}>
            <div>{t('setting-bgGWB49uCWxm')}</div>
            <div>{t('setting-jw7rUpXSXlFm')}</div>
            <div>{t('setting-yW7VuRHHhc9p')}</div>
          </div>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8, marginTop: 24 }}>
            {t('setting-gjrzBoET8D5g')}
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg5} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16 }}>
            <div>{t('setting-fGTvLAcjd4HM')}</div>
            <div>{t('setting-0ymzxNFdD3GT')}</div>
            <div>3. 组合框中的审批人节点为“且”的关系，即为会签；</div>
          </div>
        </div>
        <div style={{ marginTop: 8 }}>
          <div className=" text-[16px] font-bold" style={{ marginBottom: 8 }}>
            {t('setting-OMjMttYSKP1Q')}
          </div>
          <div style={{ width: '100%' }}>
            <Image src={HelpImg4} alt="" style={{ width: '100%', height: '100%' }} />
          </div>
          <div style={{ marginTop: 16, marginBottom: 24 }}>
            <div>{t('setting-KxH61C7MGF2G')}</div>
            <div>{t('setting-VChQCaL1J0o1')}</div>
            <div>{t('setting-Pwi5FEZ0FDqX')}</div>
            <div>{t('setting-wOMxOXt6RlyC')}</div>
            <div>{t('setting-fuDEWXZXvS7x')}</div>
            <div>5. 双击连线即可设置业务规则；</div>
          </div>
        </div>
      </div>
    </Drawer>
  );
};

export default HelpModal;
