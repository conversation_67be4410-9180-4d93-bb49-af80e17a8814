import React, { createRef, useEffect, useState } from 'react';
import { Form, Input, Button, Select, Spin, Row, Col } from 'antd';
// import CampaignOverviewService from 'service/campaignOverviewService';
import { Link } from 'react-router-dom';
import _ from 'lodash';
import FormCom from '../../../../components/form/FormCom';
import config from './config';
import './create.scss';
import { t } from 'utils/translation';

const { Option } = Select;

const Create = () => {
  const [items, setItems] = useState(config);
  const form = createRef(null);

  const setChange = (name, fun) => {
    // 设置一个change事件
    const _formItem = _.cloneDeep(items);
    const setChange = _.find(_formItem, (item) => item.name === name).componentOptions;
    setChange.onChange = fun;
    setItems(_formItem);
  };

  useEffect(() => {
    _.map(items, (item) => (item.key = item.name));
    setChange('a', () => {
      // console.log('触发了change事件');
    });
  }, []);

  const test = async () => {
    await form.current.validateFields();
  };

  return (
    <>
      <Spin tip="Loading..." spinning={false}>
        <div className="createSmartUrl">
          <header>
            <div className="Breadcrumbs">
              <Link to="/aimarketer/home/<USER>/">{t('operationCenter-LZACou4jFw0k')}</Link>
              <span>/</span>
              <span> {t('operationCenter-WXWlEcBOdiB3')}</span>
            </div>
            <h2>{t('operationCenter-WXWlEcBOdiB3')}</h2>
          </header>
        </div>
        <div className="Board">
          <Form>
            <Row>
              <Col span={8}>
                <Form.Item label={t('operationCenter-MR87FyokSsX9')}>
                  <Input placeholder={t('operationCenter-rVQwJpElixS0')} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label={t('operationCenter-r52kYrJ0AY6V')}>
                  <Select placeholder={t('operationCenter-SBQg9r0P04gE')}>
                    <Option>{t('operationCenter-CxGICTHq2iqz')}</Option>
                    <Option>{t('operationCenter-dka7FJS0Uxqw')}</Option>
                    <Option>{t('operationCenter-wMgUilKMtAPi')}</Option>
                    <Option>{t('operationCenter-HuqFvzqKGBMK')}</Option>
                    <Option>{t('operationCenter-eRaEN08kqyHh')}</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label={t('operationCenter-kVCH7mTFSJ8G')}>
                  <Input />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
        <div className="Board">
          <FormCom
            initialValues={{
              b: true,
              c: true,
              d: true
            }}
            ref={form}
            items={items}
          />
        </div>
        <div className="footer">
          <Button>{t('operationCenter-jyAy2gDmWNdc')}</Button>
          <Button style={{ marginLeft: '10px' }} onClick={test}>
            {t('operationCenter-zQi3Fkz3gAZY')}
          </Button>
        </div>
      </Spin>
    </>
  );
};

export default Create;
