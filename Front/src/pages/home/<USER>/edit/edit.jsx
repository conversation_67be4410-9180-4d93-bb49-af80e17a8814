import { InfoCircleOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Checkbox, DatePicker, Form, Input, Popconfirm, Spin, Table, Tooltip, message } from 'antd';
import 'codemirror/lib/codemirror.css';
import 'codemirror/theme/material.css';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { Controlled as CodeMirror } from 'react-codemirror2';
import { Link, useParams } from 'react-router-dom';
import SmartUrlService from 'service/SmartUrlService';
import BusinessVariable from 'service/businessVariable';
import './edit.scss';
import { myfunction, words } from './myFunction';
import { t } from 'utils/translation';

require('codemirror/mode/javascript/javascript');

const smartUrlService = new SmartUrlService();
const { RangePicker } = DatePicker;

const pagination = {
  showTotal: (totals) => `${t('operationCenter-RdaIy2Rxi4zi')} ${totals} ${t('operationCenter-y4po0SGRx01R')}`
  // showQuickJumper: true,
  // showSizeChanger: true,
  // pageSizeOptions: ['10', '20', '50']
};
const options = {
  mode: 'customModel',
  lineWrapping: true,
  // addModeClass: true,
  spellcheck: true
};

const Create = (props) => {
  const { id } = useParams();
  const inputRef = useRef(null);
  const searchRef = useRef(null);
  const [form1] = Form.useForm();
  const [form2] = Form.useForm();
  const [param, setParam] = useState({
    page: 1,
    search: [{ operator: 'LIKE', propertyName: 'variableName', value: '' }],
    size: 9999,
    sorts: [{ propertyName: 'updateTime', direction: 'desc' }]
  });
  const [loading, setLoading] = useState(false);
  const dateFormat = 'YYYY-MM-DD' || undefined;
  const [times, setTimes] = useState({
    effectiveStartTime: undefined,
    effectiveEndTime: undefined
  });

  const [status, setStatus] = useState(null);
  const [insertDataSource, setInsertDataSource] = useState([]);
  const [isEncryptCustomParams, setIsEncryptCustomParams] = useState(false);
  const [isEncryptUserCode, setIsEncryptUserCode] = useState(false);
  const [targetUrlValue, setTargetUrlValue] = useState(null);
  const [def, setDef] = useState({});
  const [urlEmptyError, setUrlEmptyError] = useState(false);
  const [urlHttpError, setUrlHttpError] = useState(false);

  useEffect(() => {
    if (!_.isNil(id)) {
      const init = async () => {
        const data = await smartUrlService.getSmarkUrl(parseInt(id));
        const {
          taskName,
          memo,
          targetUrl,
          longLink,
          encryptDatatistId,
          encryptCustomParams,
          effectiveStartTime,
          effectiveEndTime,
          status
        } = data;

        setTimes({ effectiveStartTime, effectiveEndTime });

        // 从longLink中提取UTM参数值
        const utmParams = {
          utmSource: '',
          utmMedium: '',
          utmCampaign: '',
          utmContent: '',
          utmTerm: ''
        };

        const utmRegex = {
          utmSource: /utmSource=([^&]+)/,
          utmMedium: /utmMedium=([^&]+)/,
          utmCampaign: /utmCampaign=([^&]+)/,
          utmContent: /utmContent=([^&]+)/,
          utmTerm: /utmTerm=([^&]+)/
        };

        Object.keys(utmParams).forEach((key) => {
          const match = longLink.match(utmRegex[key]);
          if (match) {
            utmParams[key] = decodeURIComponent(match[1]);
          }
        });

        form2.setFieldsValue(utmParams);

        if (encryptDatatistId === 0) {
          setIsEncryptUserCode(false);
        } else if (encryptDatatistId === 1) {
          setIsEncryptUserCode(true);
        }

        if (encryptCustomParams === 0) {
          setIsEncryptCustomParams(false);
        } else if (encryptCustomParams === 1) {
          setIsEncryptCustomParams(true);
        }

        setTargetUrlValue(targetUrl);
        form1.setFieldsValue({
          taskName,
          effectiveDuration: [dayjs(effectiveStartTime, dateFormat), dayjs(effectiveEndTime, dateFormat)],
          memo
        });

        setStatus(status);
      };
      init();
    }
  }, []);

  useEffect(() => {
    setUrlEmptyError(targetUrlValue === '');
    setUrlHttpError(targetUrlValue && targetUrlValue !== '' && !/(http|https):\/\/([\w.]+\/?)\S*/.test(targetUrlValue));
  }, [targetUrlValue]);

  const columns = [
    {
      title: t('operationCenter-gQPT6uv9BVIa'),
      dataIndex: 'variableName',
      key: 'variableName'
    },
    {
      title: t('operationCenter-mJQAr3jkHZpj'),
      dataIndex: 'variableDescribe',
      key: 'variableDescribe'
    },
    {
      title: t('operationCenter-bnIHzNKXOoIS'),
      key: 'option',
      render: (text, record) => (
        <Button
          type="link"
          className="pl-0"
          onClick={() => insertTargetUrl(record, 'custom')}
          disabled={id || !targetUrlValue || targetUrlValue === ''}
        >
          {t('operationCenter-emMq1MatAmu0')}
        </Button>
      )
    }
  ];

  const userCodeColumns = [
    {
      title: t('operationCenter-SpLw6gKif5K0'),
      dataIndex: 'variableName',
      key: 'variableName'
    },
    {
      title: t('operationCenter-OLq3eCi9nWSn'),
      dataIndex: 'describe',
      key: 'describe'
    },
    {
      title: t('operationCenter-bnIHzNKXOoIS'),
      key: 'option',
      render: (text, record) => (
        <Button
          type="link"
          className="pl-0"
          onClick={() => insertTargetUrl(record, 'userCode')}
          disabled={id || !targetUrlValue || targetUrlValue === ''}
        >
          {t('operationCenter-emMq1MatAmu0')}
        </Button>
      )
    }
  ];

  useEffect(() => {
    const getInsertData = async () => {
      setLoading(true);
      try {
        const result = await BusinessVariable.query(param);
        pagination.total = result.totalElements;
        pagination.current = param.page;
        pagination.pageSize = param.size;

        let needAddKeywords = ' ';
        _.forEach(result.content, (item) => {
          needAddKeywords += `${item.variableName} `;
        });

        setDef({
          keywords: words(`${_.trim(needAddKeywords)}`)
        });

        setInsertDataSource(result.content);
        setLoading(false);
      } catch (err) {
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    getInsertData();
  }, [param]);

  const handleInsertString = (inserted, type) => {
    const doc = inputRef.current.editor.doc;
    doc.cm.focus();
    const cursorPosition = doc.getCursor();

    let newInsertSting = '';

    if (type === 'custom') {
      newInsertSting = `{${inserted.variableName}}`;
    } else if (type === 'userCode') {
      newInsertSting = `{datatistid}`;
    } else if (type === 'UTM') {
      const { utmSource, utmMedium, utmCampaign, utmContent, utmTerm } = inserted;

      newInsertSting = `utmSource=${utmSource}&utmMedium=${utmMedium}&utmCampaign=${utmCampaign}${utmContent ? `&utmContent=${utmContent}` : ''}${
        utmTerm ? `&utmTerm=${utmTerm}` : ''
      }`;
    }

    doc.replaceRange(`${newInsertSting}`, cursorPosition, cursorPosition);
  };

  const insertTargetUrl = (record, type) => {
    handleInsertString(record, type);
  };

  const disabledDate = (current) => {
    // 禁用今天之前的所有日期
    return current && current < dayjs().subtract(1, 'days');
  };

  const onPickerChange = (date, dateString) => {
    setTimes({
      effectiveStartTime: dateString[0],
      effectiveEndTime: dateString[1]
    });
  };

  const handleTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setParam({ ...param });
  };

  const onCustomSearch = _.debounce((e) => {
    setParam({ ...param, page: 1, search: [{ operator: 'LIKE', propertyName: 'variableName', value: e }] });
  }, 400);

  const save = () => {
    let urlEmptyError = false;
    let urlHttpError = false;

    if (!targetUrlValue || targetUrlValue === '') {
      setUrlEmptyError(true);
      urlEmptyError = true;
    }

    if (targetUrlValue && targetUrlValue !== '' && !/(http|https):\/\/([\w.]+\/?)\S*/.test(targetUrlValue)) {
      setUrlHttpError(true);
      urlHttpError = true;
    }

    // 检查UTM参数重复
    const utmParams = ['utmSource=', 'utmMedium=', 'utmCampaign=', 'utmContent=', 'utmTerm='];
    for (const param of utmParams) {
      const matches = targetUrlValue.match(new RegExp(param, 'g'));
      if (matches && matches.length > 1) {
        message.error(t('operationCenter-pf2W0lKEWnvk'));
        return false;
      }
    }

    form1
      .validateFields()
      .then(async (value) => {
        setLoading(true);
        if (urlEmptyError || urlHttpError) {
          setLoading(false);
          return;
        }
        const isDatatistIdEnabled = /{datatistid}/.test(targetUrlValue);
        const data = {
          ...value,
          encryptDatatistId: isEncryptUserCode ? 1 : 0,
          encryptCustomParams: isEncryptCustomParams ? 1 : 0,
          longLink: targetUrlValue,
          targetUrl: targetUrlValue,
          isDatatistId: isDatatistIdEnabled ? 'ENABLE' : 'DISABLE',
          ...times
        };

        urlEmptyError && urlHttpError && delete data.effectiveDuration;
        if (!_.isNil(id)) data.id = id;
        await smartUrlService.save(data);
        message.success(t('operationCenter-8WKhR7yFMKhG'));
        const timeer = setTimeout(() => {
          clearTimeout(timeer);
          props.history.push('/aimarketer/home/<USER>/');
        }, 1000);
      })
      .catch(async () => {
        setLoading(false);
        // await form2.validateFields();
      });
  };

  const onInsertUTM = async () => {
    try {
      const validatedFields = await form2.validateFields();
      insertTargetUrl(validatedFields, 'UTM');
    } catch (error) {
      console.error(error);
    }
  };
  return (
    <>
      <Spin tip="Loading..." spinning={false}>
        <div className="createSmartUrl">
          <header>
            <div className="Breadcrumbs">
              <Link to="/aimarketer/home/<USER>/" style={{ color: 'rgba(0, 0, 0, 0.45)' }}>
                {t('operationCenter-LZACou4jFw0k')}
              </Link>
              <span> / </span>
              <span>{id ? t('operationCenter-64BhFLPLQCgY') : t('operationCenter-ChUrBFEIhL4q')}{t('operationCenter-z0Kx8sLbwtNz')}</span>
            </div>
            <h2>{id ? t('operationCenter-64BhFLPLQCgY') : t('operationCenter-ChUrBFEIhL4q')}{t('operationCenter-z0Kx8sLbwtNz')}</h2>
          </header>
        </div>

        <div className="customList">
          <div className="left">
            <div className="title">{t('operationCenter-W1pdua4vskcj')}</div>
            <Form form={form1} layout="vertical" style={{ width: 560 }}>
              <Form.Item
                name="taskName"
                label={t('operationCenter-A81b4LbwkfDg')}
                colon={false}
                labelAlign="left"
                rules={[
                  { required: true, message: t('operationCenter-sZd5CEX5mhUm') },
                  { max: 100, message: t('operationCenter-6CI41jiyEcwC') },
                  {
                    pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
                    message: t('operationCenter-9oXmB366TgFn')
                  },
                  () => ({
                    validator: async (_, value) => {
                      // 唯一值判定
                      try {
                        let tableNameUnique;
                        if (id) {
                          tableNameUnique = await smartUrlService.isRepeat({
                            taskName: value,
                            id
                          });
                        } else {
                          tableNameUnique = await smartUrlService.isRepeat({
                            taskName: value
                          });
                        }
                        if (!tableNameUnique) {
                          return Promise.reject(new Error(t('operationCenter-UMoDl5YMSb7k')));
                        } else {
                          return Promise.resolve();
                        }
                      } catch (error) {
                        return Promise.reject(new Error(t('operationCenter-646daV68UcSa')));
                      }
                    }
                  })
                ]}
              >
                <Input placeholder={t('operationCenter-ZuA2L88yvzoe')} />
              </Form.Item>
              <Form.Item
                name="effectiveDuration"
                label={t('operationCenter-r52kYrJ0AY6V')}
                colon={false}
                labelAlign="left"
                rules={[{ required: true, message: t('operationCenter-sDL4it4IRQnh') }]}
              >
                <RangePicker
                  disabled={id && status !== 'ENABLE'}
                  style={{ width: '100%' }}
                  format="YYYY-MM-DD"
                  onChange={onPickerChange}
                  disabledDate={disabledDate}
                />
              </Form.Item>
              <Form.Item noStyle>
                <div
                  className={`${urlHttpError || urlEmptyError ? 'codeMirrorWrapper codeMirrorErrorWrapper' : 'codeMirrorWrapper'}`}
                >
                  <div className="pb-[8px] codeMirrorLabel">{t('operationCenter-sDL4it4IRQnh')}</div>
                  {def.keywords ? (
                    <CodeMirror
                      className={id ? 'codeMirrorInputDisabled' : ''}
                      value={targetUrlValue}
                      options={options}
                      ref={inputRef}
                      defineMode={{
                        name: 'customModel',
                        fn: (config) => myfunction(config, def)
                      }}
                      onBeforeChange={(editor, data, value) => {
                        !id && setTargetUrlValue(value);
                      }}
                      pasteLinesPerSelection
                    />
                  ) : (
                    <div className="placeDiv" />
                  )}
                </div>
              </Form.Item>
              <Form.Item>
                {urlEmptyError && !id && (
                  <div className="text-[var(--ant-error-color)] targetUrlRules">{t('operationCenter-ftiv7rUjGzlQ')}</div>
                )}
                {urlHttpError && !id && (
                  <div className="text-[var(--ant-error-color)] targetUrlRules">{t('operationCenter-xpAfS73ugTeO')}</div>
                )}
                <div className="text-[rgba(0,0,0,0.45)]">
                  {t('operationCenter-GK0peKS3O7Bn')}
                </div>
              </Form.Item>
              <Form.Item
                name="memo"
                label={t('operationCenter-kVCH7mTFSJ8G')}
                colon={false}
                labelAlign="left"
                rules={[
                  { max: 100, message: t('operationCenter-bT4wL9yv9NWL') },
                  {
                    pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
                    message: t('operationCenter-EoPxjaMJUOoR')
                  }
                ]}
              >
                <Input placeholder={t('operationCenter-Fmdu0uaC3rVJ')} />
              </Form.Item>
            </Form>
          </div>

          <div className="right">
            <>
              <div className="rightTableFilter">
                <div className="flex gap-[24px]">
                  <div className="customTableTitle">{t('operationCenter-2hpQZTedSkjS')}</div>
                  <div>
                    <Checkbox
                      checked={isEncryptCustomParams}
                      disabled={id || !targetUrlValue || targetUrlValue === ''}
                      onChange={(e) => setIsEncryptCustomParams(e.target.checked)}
                    >
                      <span className="mr-4">{t('operationCenter-1cEw8wdonBBs')}</span>
                      <Tooltip title={t('operationCenter-WzwF4qcyaZVC')}>
                        <InfoCircleOutlined className="text-[rgba(0,0,0,0.45)]" />
                      </Tooltip>
                    </Checkbox>
                  </div>
                </div>

                <div className="searchToolsWrap">
                  <Input
                    placeholder={t('operationCenter-b1fxf3B9jTg2')}
                    suffix={<SearchOutlined />}
                    ref={searchRef}
                    onChange={(e) => onCustomSearch(e.target.value)}
                  />
                </div>
              </div>
              <Table
                size="small"
                dataSource={insertDataSource}
                loading={loading}
                columns={columns}
                pagination={false}
                rowKey="id"
                onChange={handleTableChange}
              />
            </>

            <>
              <div className="rightTableFilter mt-[16px]">
                <div className="flex gap-[24px]">
                  <div className="customTableTitle">{t('operationCenter-FI3l67ad5wG0')}</div>
                  <div>
                    <Checkbox
                      checked={isEncryptUserCode}
                      disabled={id || !targetUrlValue || targetUrlValue === ''}
                      onChange={(e) => setIsEncryptUserCode(e.target.checked)}
                    >
                      <span className="mr-4">{t('operationCenter-bIitVdwcyJWY')}</span>
                      <Tooltip title={t('operationCenter-QbktLTwEFQ3t')}>
                        <InfoCircleOutlined className="text-[rgba(0,0,0,0.45)]" />
                      </Tooltip>
                    </Checkbox>
                  </div>
                </div>
              </div>
              <Table
                dataSource={[{ variableName: 'datatistid', describe: t('operationCenter-sPFe6RaG91Gr') }]}
                loading={loading}
                columns={userCodeColumns}
                pagination={false}
              />
            </>

            <>
              <div className="rightTableFilter mt-[16px]">
                <div className="flex justify-between items-center w-full">
                  <div className="customTableTitle">{t('operationCenter-ZrbQnFaM59Yd')}</div>
                  <div>
                    <Button
                      type="link"
                      className="pl-0"
                      onClick={onInsertUTM}
                      disabled={id || !targetUrlValue || targetUrlValue === ''}
                    >
                      {t('operationCenter-HLUXsRj8r0EE')}
                    </Button>
                  </div>
                </div>
              </div>

              <Form form={form2} layout="vertical">
                <Form.Item
                  name="utmSource"
                  label={
                    <Tooltip title={t('operationCenter-wJXUFz8KkLga')}>
                      {t('operationCenter-u28flUpYp35b')} <InfoCircleOutlined />
                    </Tooltip>
                  }
                  labelAlign="left"
                  colon={false}
                  rules={[
                    { required: true, message: t('operationCenter-ELyW37mjOJzy') },
                    { max: 128, message: t('operationCenter-YfTdyDVk7Qt6') },
                    {
                      pattern: /^[A-Za-z0-9._+\-\u4e00-\u9fa5]+$/g,
                      message: t('operationCenter-gOJCLUW9SIo7')
                    }
                  ]}
                >
                  <Input
                    placeholder={t('operationCenter-7QgkPFbU43VY')}
                    disabled={id || !targetUrlValue || targetUrlValue === ''}
                    className="rounded-[6px]"
                  />
                </Form.Item>

                <Form.Item
                  name="utmMedium"
                  label={
                    <Tooltip title={t('operationCenter-wJXUFz8KkLga')}>
                      {t('operationCenter-9xvG9a5DFLkB')} <InfoCircleOutlined />
                    </Tooltip>
                  }
                  labelAlign="left"
                  colon={false}
                  rules={[
                    { required: true, message: t('operationCenter-ERmCJRXeAaz8') },
                    { max: 128, message: t('operationCenter-YfTdyDVk7Qt6') },
                    {
                      pattern: /^[A-Za-z0-9._+\-\u4e00-\u9fa5]+$/g,
                      message: t('operationCenter-gOJCLUW9SIo7')
                    }
                  ]}
                >
                  <Input
                    placeholder={t('operationCenter-7QgkPFbU43VY')}
                    disabled={id || !targetUrlValue || targetUrlValue === ''}
                    className="rounded-[6px]"
                  />
                </Form.Item>

                <Form.Item
                  name="utmCampaign"
                  label={
                    <Tooltip title={t('operationCenter-rFmIBSGuMz72')}>
                      {t('operationCenter-FHkZlwmL4fFW')} <InfoCircleOutlined />
                    </Tooltip>
                  }
                  colon={false}
                  labelAlign="left"
                  rules={[
                    { required: true, message: t('operationCenter-SUgtlyXPAzh8') },
                    { max: 128, message: t('operationCenter-qsJ99ZIKAGPx') },
                    {
                      pattern: /^[A-Za-z0-9._+\-\u4e00-\u9fa5]+$/g,
                      message: t('operationCenter-gOJCLUW9SIo7')
                    }
                  ]}
                >
                  <Input
                    placeholder={t('operationCenter-7QgkPFbU43VY')}
                    disabled={id || !targetUrlValue || targetUrlValue === ''}
                    className="rounded-[6px]"
                  />
                </Form.Item>

                <Form.Item
                  name="utmContent"
                  label={
                    <Tooltip title={t('operationCenter-DMjUSYdE3ft7')}>
                      {t('operationCenter-1k99KyhYAkEN')} <InfoCircleOutlined />
                    </Tooltip>
                  }
                  colon={false}
                  labelAlign="left"
                  rules={[
                    { max: 128, message: t('operationCenter-qsJ99ZIKAGPx') },
                    {
                      pattern: /^[A-Za-z0-9._+\-\u4e00-\u9fa5]+$/g,
                      message: t('operationCenter-gOJCLUW9SIo7')
                    }
                  ]}
                >
                  <Input
                    placeholder={t('operationCenter-7QgkPFbU43VY')}
                    disabled={id || !targetUrlValue || targetUrlValue === ''}
                    className="rounded-[6px]"
                  />
                </Form.Item>

                <Form.Item
                  name="utmTerm"
                  label={
                    <Tooltip title={t('operationCenter-tvBHJHjFNgeo')}>
                      {t('operationCenter-rKx6JcG1TpX5')} <InfoCircleOutlined />
                    </Tooltip>
                  }
                  colon={false}
                  labelAlign="left"
                  rules={[
                    { max: 128, message: t('operationCenter-qsJ99ZIKAGPx') },
                    {
                      pattern: /^[A-Za-z0-9._+\-\u4e00-\u9fa5]+$/g,
                      message: t('operationCenter-gOJCLUW9SIo7')
                    }
                  ]}
                >
                  <Input
                    placeholder={t('operationCenter-7QgkPFbU43VY')}
                    disabled={id || !targetUrlValue || targetUrlValue === ''}
                    className="rounded-[6px]"
                  />
                </Form.Item>
              </Form>
            </>
          </div>
        </div>

        <div className="footer">
          <Popconfirm
            title={`${t('operationCenter-Fv6IycEtdwS4')}${id ? t('operationCenter-5GhWcxvmLwr5') : t('operationCenter-6J6TiQWmSDKc')}${t('operationCenter-JA7DB7shS5CX')}`}
            onConfirm={() => {
              props.history.go(-1);
            }}
          >
            <Button style={{ borderRadius: 6 }}>{t('operationCenter-H8rw6edUl1Ik')}{id ? t('operationCenter-5GhWcxvmLwr5') : t('operationCenter-6J6TiQWmSDKc')}</Button>
          </Popconfirm>
          <Button
            type="primary"
            htmlType="submit"
            style={{ marginLeft: '10px', borderRadius: 6 }}
            loading={loading}
            onClick={save}
          >
            {t('operationCenter-oVmqDHXt9qwp')}{id ? t('operationCenter-5GhWcxvmLwr5') : t('operationCenter-6J6TiQWmSDKc')}
          </Button>
        </div>
      </Spin>
    </>
  );
};

export default Create;
