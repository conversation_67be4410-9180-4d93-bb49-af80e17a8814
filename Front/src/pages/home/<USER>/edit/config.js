import React from 'react';
import { Tooltip } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';

import { t } from 'utils/translation';

const items = [
  {
    type: 'Input',
    label: (
      <Tooltip title={t('operationCenter-xRd4Fi9uyTWA')}>
        {t('operationCenter-zPEIixxdrqLz')} <InfoCircleOutlined />
      </Tooltip>
    ),
    name: 'a',
    colSpan: 8,
    rules: [
      { required: true, message: t('operationCenter-jbVbgv0f9Gkn') },
      { max: 64, message: t('operationCenter-sfKKHQ2lkCLe') },
      {
        pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
        message: t('operationCenter-KnG2AiPafXGE')
      }
    ],
    componentOptions: {
      allowClear: true,
      placeholder: t('operationCenter-rELz5AaH5znn')
    }
  },
  {
    type: 'Switch',
    label: (
      <Tooltip title={t('operationCenter-8wE0l0dHXkP7')}>
        {t('operationCenter-lSLlz2SBMJgx')} <InfoCircleOutlined />
      </Tooltip>
    ),
    name: 'b',
    colSpan: 8,
    rules: [{ required: true, message: t('operationCenter-eF7dQq7kE26s') }],
    componentOptions: {
      checkedChildren: t('operationCenter-aPjGWTQVqLj9'),
      unCheckedChildren: t('operationCenter-J22xu7L6Lfhy')
    }
  },
  {
    type: 'Switch',
    label: (
      <Tooltip title={t('operationCenter-isxQ4gtDXrPg')}>
        {t('operationCenter-QiujzA9ijfEC')} <InfoCircleOutlined />
      </Tooltip>
    ),
    colSpan: 8,
    name: 'c',
    rules: [{ required: true, message: t('operationCenter-DE97eGu4xFPD') }],
    componentOptions: {
      checkedChildren: t('operationCenter-GV2f0jl7GqTO'),
      unCheckedChildren: t('operationCenter-EikBSPg7ggQO')
    }
  },
  {
    type: 'Switch',
    label: t('operationCenter-aOXPFiTv1PiD'),
    colSpan: 8,
    name: 'd',
    // placeholder: '请输入显示顺序',
    rules: [{ required: true, message: t('operationCenter-xJ2ndWZc8Yth') }]
  },
  {
    type: 'Input',
    label: (
      <Tooltip title={t('operationCenter-Hk5BVUKDK1wv')}>
        {t('operationCenter-u56JFvdL487R')} <InfoCircleOutlined />
      </Tooltip>
    ),
    name: 'e',
    colSpan: 8,
    rules: [
      { required: true, message: t('operationCenter-jbVbgv0f9Gkn') },
      { max: 64, message: t('operationCenter-sfKKHQ2lkCLe') },
      {
        pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
        message: t('operationCenter-KnG2AiPafXGE')
      }
    ],
    componentOptions: {
      allowClear: true,
      placeholder: t('operationCenter-CPoHK1wUWMmJ')
    }
  },
  {
    type: 'Input',
    label: (
      <Tooltip title={t('operationCenter-C7Lu3ErGg9Tm')}>
        {t('operationCenter-rJGXSC8E2aCT')} <InfoCircleOutlined />
      </Tooltip>
    ),
    name: 'f',
    colSpan: 8,
    rules: [
      { required: true, message: t('operationCenter-jbVbgv0f9Gkn') },
      { max: 64, message: t('operationCenter-sfKKHQ2lkCLe') },
      {
        pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
        message: t('operationCenter-KnG2AiPafXGE')
      }
    ],
    componentOptions: {
      allowClear: true,
      placeholder: t('operationCenter-N8FKujr0yfSn')
    }
  },
  {
    type: 'Input',
    label: (
      <Tooltip title={t('operationCenter-zoeRU7z49xks')}>
        {t('operationCenter-l20D0aAKVdUQ')} <InfoCircleOutlined />
      </Tooltip>
    ),
    name: 'g',
    colSpan: 8,
    rules: [
      { required: true, message: t('operationCenter-jbVbgv0f9Gkn') },
      { max: 64, message: t('operationCenter-sfKKHQ2lkCLe') },
      {
        pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
        message: t('operationCenter-KnG2AiPafXGE')
      }
    ],
    componentOptions: {
      allowClear: true,
      placeholder: t('operationCenter-SUgtlyXPAzh8')
    }
  },
  {
    type: 'Input',
    label: (
      <Tooltip title={t('operationCenter-DMjUSYdE3ft7')}>
        {t('operationCenter-1k99KyhYAkEN')} <InfoCircleOutlined />
      </Tooltip>
    ),
    name: 'h',
    colSpan: 8,
    rules: [
      { max: 64, message: t('operationCenter-sfKKHQ2lkCLe') },
      {
        pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
        message: t('operationCenter-KnG2AiPafXGE')
      }
    ],
    componentOptions: {
      allowClear: true,
      placeholder: t('operationCenter-4J4GRvIHvf26')
    }
  },
  {
    type: 'Input',
    label: (
      <Tooltip title={t('operationCenter-p7XUV7CRBsyX')}>
        {t('operationCenter-O22xuczJNg8C')} <InfoCircleOutlined />
      </Tooltip>
    ),
    name: 'i',
    colSpan: 8,
    rules: [
      { max: 64, message: t('operationCenter-sfKKHQ2lkCLe') },
      {
        pattern: /^[A-Za-z0-9._\-\u4e00-\u9fa5]+$/g,
        message: t('operationCenter-KnG2AiPafXGE')
      }
    ],
    componentOptions: {
      allowClear: true,
      placeholder: t('operationCenter-1fIooVYi950Z')
    }
  }
  // {
  //   type: 'TextArea',
  //   label: '描述',
  //   colSpan: 8,
  //   name: 'memo',
  //   // placeholder: '请输入描述',
  //   rules: [
  //     { max: 150, message: '只能输入150个字' }
  //   ]
  // },
  // {
  //   type: 'Select',
  //   label: '城市选择',
  //   name: 'version',
  //   rules: [
  //     { required: true, message: '城市必须填写' }
  //   ],
  //   componentOptions: {
  //     placeholder: '请选择',
  //     options: [],
  //     showSearch: true,
  //     filterOption: (input, option) => {
  //       return option.props.title.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  //     }
  //   }
  // },
  // {
  //   type: 'Radio',
  //   label: '是否测试',
  //   name: 'testStatus',
  //   rules: [
  //     { required: true, message: '是否测试' }
  //   ],
  //   componentOptions: {
  //     options: [
  //       { text: '是', value: true },
  //       { text: '否', value: false }
  //     ]
  //   }
  // },
  // {
  //   type: 'Switch',
  //   label: '可见状态',
  //   name: 'isShow',
  //   rules: [
  //     { required: true, message: '是否可见' }
  //   ],
  //   componentOptions: {
  //     checkedChildren: '显示',
  //     unCheckedChildren: '隐藏'
  //   }
  // }
];
export default items;
