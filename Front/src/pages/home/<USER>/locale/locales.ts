export default {
  cn: {
    // index.jsx 翻译 - Front/src/pages/home/<USER>/index.jsx
    'setting-mGM3HUnFsEMu': '审批事项',
    'setting-Iu2kxuqk5hqN': '审批详情',
    'setting-sGSuVXwjOrGy': '审批名称',
    'setting-I6bhcMPQtoFs': '驳回',
    'setting-WWncMkSUS8WM': '通过',
    'setting-2vex7RqjP2Wa': '审批编号',
    'setting-XYazlDOO9UTU': '审批事项名称',
    'setting-gvmFA2nFRMoW': '类型',
    'setting-GI1MY67AUs4v': '审批历史',
    'setting-MeoIL4UUitty': '审批流程',
    'setting-Er3OCPtKIFDu': '确认通过',
    'setting-RykF0uuJYTeQ': '确认驳回',
    'setting-oB2vrTY1RrrJ': '审批意见',
    'setting-Qg20rpYR26Ke': '请输入审批意见',
    'setting-tRYU45kuu5bW': '最大长度限制为100位字符',
    'setting-3iuGeWEK5yuH': '审批成功',
    'setting-qir8AeKCfDqF': '用户分群',
    'setting-IdoIY8IRREw9': '流程画布',
    'setting-hGBdEdt7MQuZ': '营销作品',
    'setting-WmSovDrv2VRw': '标签系统',
    'setting-u8Adgr17G0xc': '消息模板',
    'setting-yUjtFaPUEARe': '流程详情',
    'setting-3jCLxMkB0db6': '分群详情',
    'setting-RQzD4WLJNlQg': '状态',

    // config.js 翻译 - Front/src/pages/home/<USER>/config.js
    'setting-cjLhV8mX8XEu': '审批中',
    'setting-xfUALfgz8iaf': '审批通过',
    'setting-WTu8SpaYe7NL': '驳回',
    'setting-alPFKV5eeZdZ': '已撤销',
    'setting-6syfqhJzfnCe': '已取消',
    'setting-Tz7LXM7oiauE': '开始',
    'setting-oxOg1ceV1Idp': '提交人',
    'setting-ZXiAAOW0sN1x': '审批人',
    'setting-H46puG4mXzfo': '结束',
    'setting-5j8n4hMP0zqb': '审批异常',

    // approvalHistory/index.jsx 翻译 - Front/src/pages/home/<USER>/approvalHistory/index.jsx
    'setting-vLvj1oyf2CfS': '审批历史',
    'setting-Ls5atnDJq4QP': '提交人',
    'setting-yQHu47Kv1Rab': '审批人',
    'setting-6aosIAIA2EWO': '通过',
    'setting-eHUJ6pO5URgq': '驳回',
    'setting-SwGw51euOF5f': '申请时间',
    'setting-JH0a0WmVuoX5': '审批时间',
    'setting-29HTiunDsGyy': '审批意见',

    // approvalProcess/index.jsx 翻译 - Front/src/pages/home/<USER>/approvalProcess/index.jsx
    'setting-rZvtIJKAyDfa': '开始',
    'setting-bMiohBG5R94F': '结束',
    'setting-JT2kMDYvQjS0': '审批人',
    'setting-5pq16hJ4nnMJ': '提交人',
    'setting-3GrVjDRmzfC1': '会签',
    'setting-fCcOWJePeeyh': '或签',
    'setting-xR8HeqBv0PPw': '组合',
    'setting-1efXgB608hJe': '将审批人节点拖入组合框内，形成组合条件，组合框内不需要连线；',
    'setting-PP4ZUVU0oxEj': '初始化画布',
    'setting-5WUxcufEBInY': '部门：',
    'setting-vbrpwxw5wc9R': '角色：',
    'setting-Jj0zePRymoMs': '审批人：',
    'setting-LMBzrBOxTIw8': '重置定位',
    'setting-83cTZ80nnxFP': '快速上手',

    // approvalProcess/nodeDrawer/index.jsx 翻译 - Front/src/pages/home/<USER>/approvalProcess/nodeDrawer/index.jsx
    'setting-afL0mXdRbMnT': '审批人',
    'setting-W0EGXsxW3fXM': '支持同时按部门、角色、指定审批人配置。符合任一条件的用户均可审批。',
    'setting-JPfDhWb4SxnL': '或签',
    'setting-MhdtH5vwMqJm': '部门',
    'setting-7ra9PHlm01Nq': '角色',
    'setting-wTTHNcfeAxc6': '指定审批人',

    // approvalProcess/helpModal/index.jsx 翻译 - Front/src/pages/home/<USER>/approvalProcess/helpModal/index.jsx
    'setting-cESnuBhgVJ8t': '使用帮助',
    'setting-nm2mKw4XCPsr': '一、流程配置组件',
    'setting-zWy26YpdkD58': '开始：审批流程开始（不能编辑）；',
    'setting-5TzHsLYOB5Ye': '结束：审批流程结束（不能编辑）；',
    'setting-KN1XsI38hpMt': '提交人：审批任务的提交人（不能编辑），此节点读取具体的审批人及其部门信息等；',
    'setting-O0ISHVTnBSmI': '审批人：审批任务的审批人，支持按部门、按角色、按指定审批人审批，支持多选，可设置或签；',
    'setting-trmdifehoFpz': '会签：将审批流变为会签形式，从会签节点输出的分支为且的关系，即分支需要全部走完；',
    'setting-vHBGMyBUqqFI': '组合：将【审批人节点】拖入组合框内，形成组合条件，组合框内不需要连线；',
    'setting-Wd24TGrAvImZ': '连线：代表审批的流转方向，【双击连线】设置业务规则可控制审批的流转（开始、结束、提交人的输入输出连线不能设置）；',
    'setting-Ov71Cg7vjanb': '二、配置一个正确的审批流程',
    'setting-WyCq8SwdXmkK': '审批对象创建后，第一次做流程配置时，会初始化一个简单的审批流（如上图），在简单审批流的基础上进行调整，或删除重新编排。一个正确的审批流程必须符合：',
    'setting-sGGjnE7A7KTZ': '1. 节点不能少：必须包含开始、提交人、审批人、结束节点；',
    'setting-v79c38y2Jcuc': '2. 节点不能多：开始和提交人节点只能是一个，审批人和结束节点可以是多个；',
    'setting-Sktr4yaRXM8t': '3. 顺序不能错：开始-提交人-审批人-结束；',
    'setting-a5okyekZeiZJ': '4. 连线要正确：除开始和结束节点只有一条连线，其余节点至少有"进入"和"输出"两条线；',
    'setting-yUKBfHfgSCBg': '三、配置或签流程',
    'setting-bQquZtBxMUgE': '1. 点击审批人节点，在弹出的侧边栏设置审批人；',
    'setting-i5p5I2V2MNbI': '2. 支持按部门、按角色、按指定审批人三种类型设置，每种类型支持多选；',
    'setting-9n31Be1Ex82K': '3. 每种类型内部，各类型之间都是"或"的关系，即为"或签"；',
    'setting-jjTbF3tpWLid': '四、配置会签流程',
    'setting-jB7iPj1PhqD0': '方法一：通过会签组件',
    'setting-4bkcgvmnrSJ2': '1. 将会签节点拖入画布中，会签节点后至少跟着两个审批人节点；',
    'setting-EEmcmV2jqXFN': '2. 会签节点以组的形式存在，即一前一后代表会签的开始与结束；',
    'setting-wvtLskZEy2IX': '3. 会签的所有分支走完，才会走到下一个审批节点；',
    'setting-946me3oY0xBo': '方法二：通过组合组件',
    'setting-Qiq8k2iW6DY7': '1. 将组合组件拖入画布中，组合节点只有输入、输出两条连线；',
    'setting-2PETn4FyInv6': '2. 将审批人节点拖入组合框中，并设置审批人；',
    'setting-oQUJV5MS6AHF': '3. 组合框中的审批人节点为"且"的关系，即为会签；',
    'setting-9kGNV7Gt2syB': '五、设置流转的业务规则',
    'setting-zBKFXZqWseuY': '业务规则有两种：通用业务规则以及配置的业务规则；',
    'setting-bjE0m8qQ6GPv': '1. 通用业务规则主要针对提交人，即按提交人的不同走不同的分支；',
    'setting-eaj0d55CzdrH': '2. 通用规则不绑定审批对象，所有审批对象通用；',
    'setting-EJy3VDWHgiFk': '3. 通用业务规则有三个参数：提交人-按部门、提交人-按角色、提交人-指定提交人；',
    'setting-bN6y03zUZXv2': '4. 配置的业务规则绑定审批对象，每个审批对象子类都可以设置自己的业务规则，当对审批对象进行流程配置时，仅展示自己的业务规则；',
    'setting-QL8m1Hygq4zm': '5. 双击连线即可设置业务规则；',

    // Additional helpModal translations
    'setting-VKhwiPx4tFKP': '使用帮助',
    'setting-rqZt6BFMSQ4M': '一、流程配置组件',
    'setting-1PkwDD6qaKur': '开始：审批流程开始（不能编辑）；',
    'setting-XpqgQbjkm7Zl': '结束：审批流程结束（不能编辑）；',
    'setting-rndqOM0MlAyc': '提交人：审批任务的提交人（不能编辑），此节点读取具体的审批人及其部门信息等；',
    'setting-OMu2FcCtjdw0': '审批人：审批任务的审批人，支持按部门、按角色、按指定审批人审批，支持多选，可设置或签；',
    'setting-qZTCwSraY8gA': '会签：将审批流变为会签形式，从会签节点输出的分支为且的关系，即分支需要全部走完；',
    'setting-HEAsaw5vYfIA': '组合：将【审批人节点】拖入组合框内，形成组合条件，组合框内不需要连线；',
    'setting-eMTZywrH1JuA': '连线：代表审批的流转方向，【双击连线】设置业务规则可控制审批的流转（开始、结束、提交人的输入输出连线不能设置）；',
    'setting-9RUloodHzQiy': '二、配置一个正确的审批流程',
    'setting-AXPxyNxOZwxq': '审批对象创建后，第一次做流程配置时，会初始化一个简单的审批流（如上图），在简单审批流的基础上进行调整，或删除重新编排。一个正确的审批流程必须符合：',
    'setting-atpk8lKZ2OYU': '1. 节点不能少：必须包含开始、提交人、审批人、结束节点；',
    'setting-zUZt4Ty3076Q': '2. 节点不能多：开始和提交人节点只能是一个，审批人和结束节点可以是多个；',
    'setting-K71hz2zpWQHH': '3. 顺序不能错：开始-提交人-审批人-结束；',
    'setting-hIoqUhQLEsAZ': '4. 连线要正确：除开始和结束节点只有一条连线，其余节点至少有"进入"和"输出"两条线；',
    'setting-pPj1VnUyB1Et': '三、配置或签流程',
    'setting-42nnq9AtjQm6': '1. 点击审批人节点，在弹出的侧边栏设置审批人；',
    'setting-jSwsuEZJBuIj': '2. 支持按部门、按角色、按指定审批人三种类型设置，每种类型支持多选；',
    'setting-QrbvfIlERMeR': '3. 每种类型内部，各类型之间都是"或"的关系，即为"或签"；',
    'setting-Za7JBfSvS8M0': '四、配置会签流程',
    'setting-5bRcVjMcqA7o': '方法一：通过会签组件',
    'setting-bgGWB49uCWxm': '1. 将会签节点拖入画布中，会签节点后至少跟着两个审批人节点；',
    'setting-jw7rUpXSXlFm': '2. 会签节点以组的形式存在，即一前一后代表会签的开始与结束；',
    'setting-yW7VuRHHhc9p': '3. 会签的所有分支走完，才会走到下一个审批节点；',
    'setting-gjrzBoET8D5g': '方法二：通过组合组件',
    'setting-fGTvLAcjd4HM': '1. 将组合组件拖入画布中，组合节点只有输入、输出两条连线；',
    'setting-0ymzxNFdD3GT': '2. 将审批人节点拖入组合框中，并设置审批人；',
    'setting-cDuOqPopmMHd': '3. 组合框中的审批人节点为"且"的关系，即为会签；',
    'setting-OMjMttYSKP1Q': '五、设置流转的业务规则',
    'setting-KxH61C7MGF2G': '业务规则有两种：通用业务规则以及配置的业务规则；',
    'setting-VChQCaL1J0o1': '1. 通用业务规则主要针对提交人，即按提交人的不同走不同的分支；',
    'setting-Pwi5FEZ0FDqX': '2. 通用规则不绑定审批对象，所有审批对象通用；',
    'setting-wOMxOXt6RlyC': '3. 通用业务规则有三个参数：提交人-按部门、提交人-按角色、提交人-指定提交人；',
    'setting-fuDEWXZXvS7x': '4. 配置的业务规则绑定审批对象，每个审批对象子类都可以设置自己的业务规则，当对审批对象进行流程配置时，仅展示自己的业务规则；',
    'setting-IkVD5QI8YLM5': '5. 双击连线即可设置业务规则；'
  },
  en: {
    // index.jsx translations - Front/src/pages/home/<USER>/index.jsx
    'setting-mGM3HUnFsEMu': 'Approval Items',
    'setting-Iu2kxuqk5hqN': 'Approval Detail',
    'setting-sGSuVXwjOrGy': 'Approval Name',
    'setting-I6bhcMPQtoFs': 'Reject',
    'setting-WWncMkSUS8WM': 'Approve',
    'setting-2vex7RqjP2Wa': 'Approval Number',
    'setting-XYazlDOO9UTU': 'Approval Item Name',
    'setting-gvmFA2nFRMoW': 'Type',
    'setting-GI1MY67AUs4v': 'Approval History',
    'setting-MeoIL4UUitty': 'Approval Process',
    'setting-Er3OCPtKIFDu': 'Confirm Approval',
    'setting-RykF0uuJYTeQ': 'Confirm Rejection',
    'setting-oB2vrTY1RrrJ': 'Approval Opinion',
    'setting-Qg20rpYR26Ke': 'Please enter approval opinion',
    'setting-tRYU45kuu5bW': 'Maximum length is 100 characters',
    'setting-3iuGeWEK5yuH': 'Approval Successful',
    'setting-qir8AeKCfDqF': 'User Segment',
    'setting-IdoIY8IRREw9': 'Process Canvas',
    'setting-hGBdEdt7MQuZ': 'Marketing Works',
    'setting-WmSovDrv2VRw': 'Label System',
    'setting-u8Adgr17G0xc': 'Message Template',
    'setting-yUjtFaPUEARe': 'Process Detail',
    'setting-3jCLxMkB0db6': 'Segment Detail',
    'setting-RQzD4WLJNlQg': 'Status',

    // config.js translations - Front/src/pages/home/<USER>/config.js
    'setting-cjLhV8mX8XEu': 'Under Approval',
    'setting-xfUALfgz8iaf': 'Approved',
    'setting-WTu8SpaYe7NL': 'Rejected',
    'setting-alPFKV5eeZdZ': 'Withdrawn',
    'setting-6syfqhJzfnCe': 'Cancelled',
    'setting-Tz7LXM7oiauE': 'Start',
    'setting-oxOg1ceV1Idp': 'Submitter',
    'setting-ZXiAAOW0sN1x': 'Approver',
    'setting-H46puG4mXzfo': 'End',
    'setting-5j8n4hMP0zqb': 'Approval Error',

    // approvalHistory/index.jsx translations - Front/src/pages/home/<USER>/approvalHistory/index.jsx
    'setting-vLvj1oyf2CfS': 'Approval History',
    'setting-Ls5atnDJq4QP': 'Submitter',
    'setting-yQHu47Kv1Rab': 'Approver',
    'setting-6aosIAIA2EWO': 'Approved',
    'setting-eHUJ6pO5URgq': 'Rejected',
    'setting-SwGw51euOF5f': 'Application Time',
    'setting-JH0a0WmVuoX5': 'Approval Time',
    'setting-29HTiunDsGyy': 'Approval Opinion',

    // approvalProcess/index.jsx translations - Front/src/pages/home/<USER>/approvalProcess/index.jsx
    'setting-rZvtIJKAyDfa': 'Start',
    'setting-bMiohBG5R94F': 'End',
    'setting-JT2kMDYvQjS0': 'Approver',
    'setting-5pq16hJ4nnMJ': 'Submitter',
    'setting-3GrVjDRmzfC1': 'Parallel Gateway',
    'setting-fCcOWJePeeyh': 'Or Gateway',
    'setting-xR8HeqBv0PPw': 'Combination',
    'setting-1efXgB608hJe': 'Drag approver nodes into the combination box to form combination conditions, no connection needed within the combination box;',
    'setting-PP4ZUVU0oxEj': 'Initialize Canvas',
    'setting-5WUxcufEBInY': 'Department:',
    'setting-vbrpwxw5wc9R': 'Role:',
    'setting-Jj0zePRymoMs': 'Approver:',
    'setting-LMBzrBOxTIw8': 'Reset Position',
    'setting-83cTZ80nnxFP': 'Quick Start',

    // approvalProcess/nodeDrawer/index.jsx translations - Front/src/pages/home/<USER>/approvalProcess/nodeDrawer/index.jsx
    'setting-afL0mXdRbMnT': 'Approver',
    'setting-W0EGXsxW3fXM': 'Supports configuration by department, role, and designated approver simultaneously. Users meeting any condition can approve.',
    'setting-JPfDhWb4SxnL': 'Or Gateway',
    'setting-MhdtH5vwMqJm': 'Department',
    'setting-7ra9PHlm01Nq': 'Role',
    'setting-wTTHNcfeAxc6': 'Designated Approver',

    // approvalProcess/helpModal/index.jsx translations - Front/src/pages/home/<USER>/approvalProcess/helpModal/index.jsx
    'setting-cESnuBhgVJ8t': 'Help Guide',
    'setting-nm2mKw4XCPsr': '1. Process Configuration Components',
    'setting-zWy26YpdkD58': 'Start: Approval process start (cannot be edited);',
    'setting-5TzHsLYOB5Ye': 'End: Approval process end (cannot be edited);',
    'setting-KN1XsI38hpMt': 'Submitter: Submitter of approval task (cannot be edited), this node reads specific approver and department information;',
    'setting-O0ISHVTnBSmI': 'Approver: Approver of approval task, supports approval by department, role, designated approver, supports multiple selection, can set or gateway;',
    'setting-trmdifehoFpz': 'Parallel Gateway: Changes approval flow to parallel form, branches output from parallel gateway have AND relationship, all branches must be completed;',
    'setting-vHBGMyBUqqFI': 'Combination: Drag [Approver Nodes] into combination box to form combination conditions, no connection needed within combination box;',
    'setting-Wd24TGrAvImZ': 'Connection: Represents approval flow direction, [Double-click connection] to set business rules to control approval flow (start, end, submitter input/output connections cannot be set);',
    'setting-Ov71Cg7vjanb': '2. Configure a Correct Approval Process',
    'setting-WyCq8SwdXmkK': 'After creating approval object, when configuring process for the first time, a simple approval flow will be initialized (as shown above). Adjust based on the simple approval flow or delete and rearrange. A correct approval process must comply with:',
    'setting-sGGjnE7A7KTZ': '1. Nodes cannot be missing: Must include start, submitter, approver, end nodes;',
    'setting-v79c38y2Jcuc': '2. Nodes cannot be excessive: Start and submitter nodes can only be one each, approver and end nodes can be multiple;',
    'setting-Sktr4yaRXM8t': '3. Order cannot be wrong: Start-Submitter-Approver-End;',
    'setting-a5okyekZeiZJ': '4. Connections must be correct: Except start and end nodes have only one connection, other nodes must have at least "input" and "output" connections;',
    'setting-yUKBfHfgSCBg': '3. Configure Or Gateway Process',
    'setting-bQquZtBxMUgE': '1. Click approver node, set approver in the popup sidebar;',
    'setting-i5p5I2V2MNbI': '2. Supports three types of settings: by department, by role, by designated approver, each type supports multiple selection;',
    'setting-9n31Be1Ex82K': '3. Within each type, all types have "OR" relationship, which is "Or Gateway";',
    'setting-jjTbF3tpWLid': '4. Configure Parallel Gateway Process',
    'setting-jB7iPj1PhqD0': 'Method 1: Through Parallel Gateway Component',
    'setting-4bkcgvmnrSJ2': '1. Drag parallel gateway node into canvas, parallel gateway node must be followed by at least two approver nodes;',
    'setting-EEmcmV2jqXFN': '2. Parallel gateway nodes exist in group form, front and back represent start and end of parallel gateway;',
    'setting-wvtLskZEy2IX': '3. All branches of parallel gateway must be completed before proceeding to next approval node;',
    'setting-946me3oY0xBo': 'Method 2: Through Combination Component',
    'setting-Qiq8k2iW6DY7': '1. Drag combination component into canvas, combination node has only input and output connections;',
    'setting-2PETn4FyInv6': '2. Drag approver nodes into combination box and set approvers;',
    'setting-oQUJV5MS6AHF': '3. Approver nodes in combination box have "AND" relationship, which is parallel gateway;',
    'setting-9kGNV7Gt2syB': '5. Set Business Rules for Flow',
    'setting-zBKFXZqWseuY': 'There are two types of business rules: general business rules and configured business rules;',
    'setting-bjE0m8qQ6GPv': '1. General business rules mainly target submitters, different submitters take different branches;',
    'setting-eaj0d55CzdrH': '2. General rules are not bound to approval objects, universal for all approval objects;',
    'setting-EJy3VDWHgiFk': '3. General business rules have three parameters: submitter-by department, submitter-by role, submitter-designated submitter;',
    'setting-bN6y03zUZXv2': '4. Configured business rules are bound to approval objects, each approval object subclass can set its own business rules, only its own business rules are displayed when configuring process for approval objects;',
    'setting-QL8m1Hygq4zm': '5. Double-click connection to set business rules;',

    // Additional helpModal translations
    'setting-VKhwiPx4tFKP': 'Usage Help',
    'setting-rqZt6BFMSQ4M': '1. Process Configuration Components',
    'setting-1PkwDD6qaKur': 'Start: Approval process begins (cannot be edited);',
    'setting-XpqgQbjkm7Zl': 'End: Approval process ends (cannot be edited);',
    'setting-rndqOM0MlAyc': 'Submitter: The submitter of the approval task (cannot be edited), this node reads specific approver and department information;',
    'setting-OMu2FcCtjdw0': 'Approver: The approver of the approval task, supports approval by department, role, or designated approver, supports multiple selection, can set OR approval;',
    'setting-qZTCwSraY8gA': 'Countersign: Changes the approval flow to countersign form, branches output from countersign nodes have AND relationship, meaning all branches must be completed;',
    'setting-HEAsaw5vYfIA': 'Combination: Drag [Approver Nodes] into the combination box to form combination conditions, no connection lines needed within the combination box;',
    'setting-eMTZywrH1JuA': 'Connection: Represents the flow direction of approval, [double-click connection] to set business rules to control approval flow (start, end, submitter input/output connections cannot be set);',
    'setting-9RUloodHzQiy': '2. Configure a Correct Approval Process',
    'setting-AXPxyNxOZwxq': 'After creating an approval object, when configuring the process for the first time, a simple approval flow will be initialized (as shown above), adjust based on the simple approval flow, or delete and rearrange. A correct approval process must comply with:',
    'setting-atpk8lKZ2OYU': '1. Nodes cannot be missing: must include start, submitter, approver, and end nodes;',
    'setting-zUZt4Ty3076Q': '2. Nodes cannot be excessive: start and submitter nodes can only be one each, approver and end nodes can be multiple;',
    'setting-K71hz2zpWQHH': '3. Order cannot be wrong: start-submitter-approver-end;',
    'setting-hIoqUhQLEsAZ': '4. Connections must be correct: except start and end nodes which have only one connection, other nodes must have at least "input" and "output" connections;',
    'setting-pPj1VnUyB1Et': '3. Configure OR Approval Process',
    'setting-42nnq9AtjQm6': '1. Click the approver node, set the approver in the pop-up sidebar;',
    'setting-jSwsuEZJBuIj': '2. Supports three types of settings: by department, by role, by designated approver, each type supports multiple selection;',
    'setting-QrbvfIlERMeR': '3. Within each type, the relationship between types is "OR", which is "OR approval";',
    'setting-Za7JBfSvS8M0': '4. Configure Countersign Process',
    'setting-5bRcVjMcqA7o': 'Method 1: Through Countersign Component',
    'setting-bgGWB49uCWxm': '1. Drag the countersign node into the canvas, the countersign node should be followed by at least two approver nodes;',
    'setting-jw7rUpXSXlFm': '2. Countersign nodes exist in group form, one before and one after representing the start and end of countersign;',
    'setting-yW7VuRHHhc9p': '3. All branches of countersign must be completed before proceeding to the next approval node;',
    'setting-gjrzBoET8D5g': 'Method 2: Through Combination Component',
    'setting-fGTvLAcjd4HM': '1. Drag the combination component into the canvas, combination nodes have only input and output connections;',
    'setting-0ymzxNFdD3GT': '2. Drag approver nodes into the combination box and set approvers;',
    'setting-cDuOqPopmMHd': '3. Approver nodes in the combination box have "AND" relationship, which is countersign;',
    'setting-OMjMttYSKP1Q': '5. Set Business Rules for Flow',
    'setting-KxH61C7MGF2G': 'There are two types of business rules: general business rules and configured business rules;',
    'setting-VChQCaL1J0o1': '1. General business rules mainly target submitters, i.e., different branches based on different submitters;',
    'setting-Pwi5FEZ0FDqX': '2. General rules are not bound to approval objects, applicable to all approval objects;',
    'setting-wOMxOXt6RlyC': '3. General business rules have three parameters: submitter-by department, submitter-by role, submitter-designated submitter;',
    'setting-fuDEWXZXvS7x': '4. Configured business rules are bound to approval objects, each approval object subclass can set its own business rules, when configuring processes for approval objects, only its own business rules are displayed;',
    'setting-IkVD5QI8YLM5': '5. Double-click the connection to set business rules;'
  }
};
