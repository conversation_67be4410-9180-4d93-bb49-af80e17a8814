import { Form, Input, message, Modal, Select, Space, Table, Tooltip } from 'antd';
import TableSearch from 'components/bussinesscoms/tableSearch/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { t } from '@/utils/translation';
import MyToDoListService from 'service/myToDoListService';
import UserService from 'service/UserService';
import { elements, initHistoryParam, initParam } from '../config';

import '../index.scss';

const userService = new UserService();

const { TextArea } = Input;

const pagination = {
  showTotal: (totals) => t('setting-tuZSKb88yzks', { totals }),
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const hisToryPagination = {
  showTotal: (totals) => t('setting-tuZSKb88yzks', { totals }),
  showQuickJumper: true,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50']
};

const statusList = {
  RUNNING: t('setting-YYJxXNtyy2hw'),
  PASS: t('setting-uUypU63rNBil'),
  REJECT: t('setting-6FhEyEQ9TOfo'),
  BACKOUT: t('setting-o5W3oX5UsDx2'),
  CANCEL: t('setting-keCa96EDRxaS')
};

const detailUrlList = {
  CAMPAIGN: '/aimarketer/home/<USER>',
  SEGMENT: '/aimarketer/home/<USER>/userGroup'
};

export default function MyToDoListRunning({ props, tabKey, dictTypeList, userList }) {
  const {
    location: { state },
    dispatch,
    messageInfo
  } = props;
  const [form] = Form.useForm();

  const [dataSource, setDataSource] = useState([]);
  const [historyTableSource, setHistoryTableSource] = useState([]);
  const [userId, setUserId] = useState(undefined);
  const [loading, setLoading] = useState(false);
  const [param, setParam] = useState(_.cloneDeep(state?.paramRunning || initParam));
  const [historyParam, setHistoryParam] = useState(initHistoryParam);
  //   const [tabKey, setTabKey] = useState(1);
  const [approveStatus, setApproveStatus] = useState([]);
  const [columnsKey, setColumnsKey] = useState('');

  const [open, setOpen] = useState(false);
  const [logStatus, setLogStatus] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);

  const renderOptions = (record) => (
    <Space size={16}>
      <a onClick={() => onColumnActionClick('finish', record)} type="link">
        {t('setting-POW1CSI95rFv')}
      </a>
      <a onClick={() => onColumnActionClick('reject', record)} type="link">
        {t('setting-6FhEyEQ9TOfo')}
      </a>
      <a onClick={() => onColumnActionClick('log', record)} type="link">
        {t('setting-IPdxsfd2v3xh')}
      </a>
    </Space>
  );

  const columnsHistory = [
    {
      title: t('setting-gFkdVPngERQF'),
      key: 'promoterName',
      width: 150,
      dataIndex: 'promoterName',
      render: (text, record) => (
        <div>{(record.createTime && record.status === 'RUNNING') || record.status === 'BACKOUT' ? text : '-'}</div>
      )
    },
    {
      title: t('setting-TxuhBhIrNl8A'),
      width: 200,
      dataIndex: 'createTime',
      sorter: true,
      key: 'createTime',
      render: (text, record) =>
        (record.createTime && record.status === 'RUNNING') || record.status === 'BACKOUT'
          ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss')
          : '-'
    },
    {
      title: t('setting-cQXthQfXcNMb'),
      width: 100,
      key: 'status',
      render: (text) => <span>{statusList[text.status]}</span>
    },
    {
      title: t('setting-JTw3dPocWQh3'),
      key: 'approverName',
      width: 100,
      render: (record) => <span>{record.approverName ? record.approverName : '-'}</span>
    },
    {
      title: t('setting-mUmM9lXou2zx'),
      key: 'opinion',
      width: 240,
      render: (record) => (
        <Tooltip title={record.opinion ? record.opinion : ''}>
          <span className="optionWrap">{record.opinion ? record.opinion : '-'}</span>
        </Tooltip>
      )
    },
    {
      title: t('setting-ynXAxqqtMxT7'),
      width: 200,
      dataIndex: 'approvalTime',
      sorter: true,
      key: 'approvalTime',
      render: (text, record) => (record.approvalTime ? dayjs(record.approvalTime).format('YYYY-MM-DD HH:mm:ss') : '-')
    }
  ];

  const columnsRunning = [
    {
      title: t('setting-mikfpWLP1Axh'),
      key: 'approvalNo',
      dataIndex: 'approvalNo',
      sorter: true,
      width: 200
    },
    {
      title: t('setting-BYaWcwVLx9VK'),
      key: 'contentName',
      width: 200,
      ellipsis: true,
      dataIndex: 'contentName',
      render: (text, record) => (
        <Tooltip title={text}>
          <a onClick={() => onColumnActionClick('detail', record)}>{text}</a>
        </Tooltip>
      )
    },
    {
      title: t('setting-BQpV6odEe9du'),
      key: 'contentType',
      width: 100,
      dataIndex: 'contentType',
      render: (text) => <span>{dictTypeList.find((item) => item.value === text).label}</span>
    },
    {
      title: t('setting-gFkdVPngERQF'),
      key: 'promoterName',
      width: 150,
      dataIndex: 'promoterName'
    },
    {
      title: t('setting-TxuhBhIrNl8A'),
      width: 200,
      sorter: true,
      dataIndex: 'createTime',
      key: 'createTime',
      render: (text, record) => (record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : '-')
    },
    {
      title: t('setting-4EP24CzENMwC'),
      width: 120,
      fixed: 'right',
      render: (text, record) => <Space>{renderOptions(record)}</Space>
    }
  ];

  useEffect(() => {
    const init = async () => {
      const { id } = await userService.getCurrentUser();

      const res = await MyToDoListService.getProcessAuthority({
        projectId: localStorage.getItem('projectId'),
        loginId: id
      });

      setApproveStatus(res);
      setUserId(id);
    };
    init();
  }, []);

  const handleTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setParam({ ...param });
  };

  const handleHistoryTableChange = (lastpagination, filtersArg, sorter) => {
    historyParam.page = lastpagination.current;
    historyParam.size = lastpagination.pageSize;
    if (sorter.field) {
      historyParam.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setHistoryParam({ ...historyParam });
  };

  useEffect(() => {
    const getTableData = async () => {
      setLoading(true);
      if (userId) {
        const processType = [];
        approveStatus.forEach((item) => {
          if (item.flag) {
            processType.push(item.type);
          }
        });

        if (!processType.length) {
          setLoading(false);
          return;
        }

        const finalParam = _.cloneDeep(param);
        finalParam.search = [
          ...finalParam.search,
          {
            operator: 'EQ',
            propertyName: 'projectId',
            value: localStorage.getItem('projectId')
          },
          { operator: 'NE', propertyName: 'promoterId', value: userId },
          { operator: 'EQ', propertyName: 'status', value: 'RUNNING' },
          {
            operator: 'IN',
            propertyName: 'contentType',
            value: processType.join(',')
          },
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          },
          {
            operator: 'NE',
            propertyName: 'approvalType',
            value: 'ACTIVITI'
          }
        ];

        const res = await MyToDoListService.approvalListBy([
          {
            operator: 'EQ',
            propertyName: 'projectId',
            value: localStorage.getItem('projectId')
          },
          { operator: 'NE', propertyName: 'promoterId', value: userId },
          { operator: 'EQ', propertyName: 'status', value: 'RUNNING' },
          {
            operator: 'IN',
            propertyName: 'contentType',
            value: processType.join(',')
          },
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          },
          {
            operator: 'NE',
            propertyName: 'approvalType',
            value: 'ACTIVITI'
          }
        ]);

        dispatch({ type: 'messageInfo', messageInfo: { toToListV2: messageInfo.toToListV2, toDoList: res.length } });

        const result = await MyToDoListService.query2(finalParam);
        pagination.total = result.totalElements;
        pagination.current = param.page;
        pagination.pageSize = param.size;
        props.history.replace({
          state: { ...state, paramRunning: param, tabKey }
        });
        setDataSource(result.content);
      }
      setLoading(false);
    };
    getTableData();
  }, [param, tabKey, userId, approveStatus]);

  useEffect(() => {
    const getHistoryTableData = async () => {
      setHistoryLoading(true);
      const finalParam = _.cloneDeep(historyParam);
      const result = await MyToDoListService.processHistory(finalParam);
      hisToryPagination.total = result.totalElements;
      hisToryPagination.current = historyParam.page;
      hisToryPagination.pageSize = historyParam.size;
      setHistoryTableSource(result.content.filter((item) => item.status !== 'PENDING'));
      setLoading(false);
      setHistoryLoading(false);
    };
    getHistoryTableData();
  }, [historyParam]);

  const showModal = () => {
    setOpen(true);
  };

  const handleCancel = () => {
    setOpen(false);
  };

  const hideLogModal = () => {
    setLogStatus(false);
  };

  const onSubmit = async () => {
    setConfirmLoading(true);
    const { opinion, status, approvalNo, contentType } = form.getFieldValue();

    try {
      await form.validateFields();
    } catch (error) {
      setConfirmLoading(false);
      return;
    }

    try {
      await MyToDoListService.saveProcessLabelInstance({
        approvalNo,
        contentType,
        approverId: userId,
        status: status === 'FINISH' ? 'PASS' : 'REJECT',
        opinion,
        projectId: localStorage.getItem('projectId')
      });

      message.success(status === 'FINISH' ? t('setting-ZOrXTva5Q2xT') : t('setting-PimTERBUbI6u'));
      setConfirmLoading(false);
      handleCancel();
      setParam({ ...param });
    } catch (error) {
      console.error(error);
    } finally {
      setConfirmLoading(false);
    }
  };

  const onColumnActionClick = async (key, record) => {
    setColumnsKey(key);
    const {
      id,
      approvalNo,
      createUserId,
      createTime,
      contentType,
      contentName,
      contentId,
      contentUrl,
      mark,
      status,
      promoterId
    } = record;
    if (key === 'detail') {
      if (contentType === 'MARKET_WORKS') {
        const result = await MyToDoListService.getAuthorization({
          loginId: userId,
          projectId: localStorage.getItem('projectId')
        });

        const newUrl =
          contentUrl.indexOf('?') >= 0
            ? `${contentUrl}&Authorization=${result}`
            : `${contentUrl}?Authorization=${result}`;
        window.open(newUrl, '_blank');
      } else if (mark === 'INSIDE') {
        props.history.push(
          `${detailUrlList[contentType]}/detail?id=${contentId}&definition=${true}&status=${true}&promoterId=${promoterId}&approvalType=BMS`
        );
      } else {
        const newContentUrl = contentUrl.includes('?') ? `${contentUrl}&isTag=true` : `${contentUrl}?isTag=true`;
        const pathName = `/aimarketer/home/<USER>
        props.history.push(pathName);
      }
    } else if (key === 'finish') {
      form.setFieldsValue({
        name: contentName,
        id,
        createTime,
        createUserId,
        approvalNo,
        definitionId: record.definitionId,
        status: 'FINISH',
        contentUrl,
        opinion: null,
        contentType
      });
      showModal();
    } else if (key === 'reject') {
      form.setFieldsValue({
        name: contentName,
        createTime,
        createUserId,
        approvalNo,
        definitionId: record.definitionId,
        id,
        status: 'REJECT',
        contentUrl,
        opinion: null,
        contentType
      });
      showModal();
    } else if (key === 'log') {
      // showLogModal(id);
      props.history.push(`/aimarketer/home/<USER>
        contentType,
        approvalNo,
        contentName,
        approvalStatus: status,
        createUserId,
        contentId,
        mark,
        contentUrl,
        approvalType: 'BMS'
      });
    }
  };
  return (
    <div className="todoList">
      <div className="search">
        <TableSearch
          elements={elements(2, userList, dictTypeList)}
          onChange={(data) => setParam({ ...param, ...data })}
          span={8}
          initialValues={state?.paramRunning?.search}
        />
      </div>
      <div className="content">
        <div className="tableWrap">
          <Table
            dataSource={dataSource}
            columns={columnsRunning}
            loading={loading}
            onChange={handleTableChange}
            rowKey="id"
            pagination={pagination}
            scroll={{ x: 1600 }}
          />
        </div>

        <Modal
          title={t('setting-IPdxsfd2v3xh')}
          className="processHistoryModalWrap"
          open={logStatus}
          destroyOnClose
          // okText="确认"
          // onOk={hideLogModal}
          confirmLoading={historyLoading}
          onCancel={hideLogModal}
          footer={null}
        >
          <div>
            <Table
              dataSource={historyTableSource}
              pagination={hisToryPagination}
              columns={columnsHistory}
              loading={historyLoading}
              onChange={handleHistoryTableChange}
              scroll={{ x: 400 }}
              rowKey="id"
            />
          </div>
        </Modal>

        <Modal
          title={t('setting-tUC0TGNPWmCW')}
          className="processModalWrap"
          open={open}
          destroyOnClose
          okText={`确认${columnsKey === 'finish' ? '通过' : '驳回'}`}
          onOk={() => onSubmit()}
          confirmLoading={confirmLoading}
          onCancel={handleCancel}
        >
          <div className="processForm">
            <Form
              name="basic"
              layout="vertical"
              form={form}
              colon={false}
              confirmLoading={confirmLoading}
              initialValues={{
                name: null,
                id: null,
                opinion: null,
                status: null
              }}
            >
              <Form.Item label={t('setting-BYaWcwVLx9VK')} name="name">
                <Select disabled />
              </Form.Item>

              <Form.Item
                label={t('setting-mUmM9lXou2zx')}
                name="opinion"
                rules={[{ max: 100, message: t('setting-1Qd3te97Qccb') }]}
              >
                <TextArea
                  placeholder={t('setting-ZIwj2COfyHeI')}
                  autoSize={{
                    minRows: 2,
                    maxRows: 6
                  }}
                />
              </Form.Item>
            </Form>
          </div>
        </Modal>
      </div>
    </div>
  );
}
