import { t } from 'utils/translation';

import CheckAuth from '@/utils/checkAuth';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import {
  Breadcrumb,
  Button,
  Descriptions,
  Drawer,
  Dropdown,
  Modal,
  Spin,
  Statistic,
  Tabs,
  Typography,
  message
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useReducer, useState } from 'react';
import { useParams } from 'react-router-dom';
import CampaignsService from 'service/CampaignsService';
import UserService from 'service/UserService';
import AddCampains from '../addCampains';
import ProcessCanvas from './addComposeTabs/processCanvas/processCanvas';
import CompaignsActivityAnalysis from './tabs/AnalyzeTab/activityAnalysis/index';
import Compose from './tabs/ComposeTab/compose';
import Situation from './tabs/SituationTab/situation';

import { copyToClipboard } from '@/utils/universal';
import './details.scss';

const { Text } = Typography;
const { Countdown } = Statistic;

const campaignsService = new CampaignsService();
const userService = new UserService();

const reducer = (state, action) => ({ ...state, ...action });
const ShareCampainsDetails = (props) => {
  const { id } = useParams();
  const [data, setData] = useState(null);
  const [tabKey, setTabKey] = useState('1');
  const [addComposeVisible, setAddComposeVisible] = useState(false);
  const [visible, setVisible] = useState(false);
  const [isShowEdit, setIsShowEdit] = useState({ status: false, idEdit: null });
  const [editValue, setEditValue] = useState({});
  const [Refresh, setRefresh] = useState(false);
  const [userId, setUserId] = useState(null);
  const [loading, setLoading] = useState(false);
  const [rolesList, setRolesList] = useState([]);
  const [noRolesOpen, setNoRolesOpen] = useState(false);
  const [rolesLoading, setRolesLoading] = useState(true);

  const [state, dispatch] = useReducer(reducer, {
    addComposeData: [],
    saveParams: [],
    saveLoading: false,
    situationRefresh: false,
    shareDeptId: null
  });

  useEffect(() => {
    const isActivity = localStorage.getItem('activityCache');
    const isActivityAnalysis = localStorage.getItem('isActivityAnalysis');
    if (isActivity || isActivityAnalysis) {
      setTabKey('2');
    }
    localStorage.removeItem('activityCache');
    localStorage.removeItem('isActivityAnalysis');
  }, []);

  useEffect(() => {
    let time = null;
    const init = async () => {
      const authList = await campaignsService.getShareUserAuths({
        shareContentId: Number(id),
        type: 'STRATEGY'
      });

      const userInfo = await userService.getCurrentUser();
      setUserId(userInfo.id);

      const data = await campaignsService.getCampaignsV2({
        id: Number(id),
        deptId: window.getDeptId(),
        type: 'share'
      });

      if (authList.find((item) => item.authId === 'aim_campaigns_view') || data.shareId === userInfo.id) {
        setRolesLoading(false);
        setRolesList(authList.map((item) => item.authId));
      } else {
        setNoRolesOpen(true);
      }

      dispatch({
        shareDeptId: data.shareUserDeptId
      });
      // saveRecordInfo(id, userInfo.id);
      setEditValue(data);
      setIsShowEdit({ status: true, idEdit: id });
      setData(data);
    };
    init();
    time = setInterval(init, 60000);
    return () => clearInterval(time);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [Refresh, userId]);

  //   const calc = (day1, day2) => {
  //     let StartTime = Date.parse(_.replace(day1, /-/g, '/'));
  //     let EndTime = Date.parse(_.replace(day2, /-/g, '/'));
  //     const oneDay = 1000 * 60 * 60 * 24;
  //     const d1 = new Date(StartTime).getTime();
  //     const d2 = new Date(EndTime).getTime() + 86400000;
  //     let day = { remainTime: parseInt((d2 - d1) / oneDay), nowTime: new Date().getTime(), endTime: d2 };

  //     if (day.nowTime > day.endTime) {
  //       return '0';
  //     }
  //     return `${parseInt((d2 - d1) / oneDay)}`;
  //   };

  const saveRecordInfo = async (id, userId) => {
    setLoading(true);
    await campaignsService.saveUserOperationRecord({
      targetId: id,
      id: data?.recentUserOperationRecord?.id,
      targetType: 'CAMPAIGNS',
      type: 'RECENT',
      createUserId: userId,
      updateUserId: userId,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
    setLoading(false);
  };

  const onDrawerClose = () => {
    setAddComposeVisible(false);
  };

  const onSave = async () => {
    const { addComposeData } = state;
    if (!addComposeData.length) {
      message.error(t('dataCenter-ddBN55hdgKVt'));
      return;
    }
    dispatch({ saveLoading: true });
    const params = addComposeData.map((item) => {
      return {
        campaignsId: Number(id),
        scenarioId: item.scenario?.id,
        campaignId: item.id
      };
    });

    try {
      await campaignsService.saveCampains(params);
      dispatch({
        saveParams: params
      });
      setAddComposeVisible(false);
      dispatch({ saveLoading: false });
      saveRecordInfo(data.id, userId);
      message.success(t('dataCenter-XGvv6yjVCgTE'));
    } catch (e) {
      setAddComposeVisible(false);
      dispatch({ saveLoading: false });
      message.error(e);
    }
  };

  const onCopyUrlChange = () => {
    const url = `${window.location.origin}/aimarketer/home/<USER>/shareList/detail/${id}`;
    copyToClipboard(url);
  };
  const getGroupButtons = (phase) => {
    let items;
    if (phase === 'TERMINATED') {
      // menu = <Menu>
      //   <Menu.Item onClick={() => onColumnActionClick(collectStatus ? 'cancelCollect' : 'collect', null, data)}>
      //     <span>{collectStatus ? '取消收藏' : '收藏'}</span>
      //   </Menu.Item>
      //   <Menu.Item onClick={() => onColumnActionClick('delete', null, data)}>
      //     <span>删除</span>
      //   </Menu.Item>
      // </Menu>;

      items = [
        rolesList.find((item) => item === 'aim_campaigns_delete') &&
          CheckAuth.checkAuth('aim_campaigns_delete') && {
            label: <span onClick={() => onColumnActionClick('delete', null, data)}>{t('dataCenter-xmbk4yliJtB9')}</span>,
            key: 'delete'
          },
        {
          label: <span onClick={onCopyUrlChange}>{t('dataCenter-5Y34kyr1Owlp')}</span>,
          key: 'copy'
        }
      ];
    } else {
      items = [
        rolesList.find((item) => item === 'aim_campaigns_edit') &&
          CheckAuth.checkAuth('aim_campaigns_edit') && {
            label: (
              <span
                onClick={() => onColumnActionClick('edit', null, data)}
                style={{ display: 'inline-block', width: '100%' }}
              >
                {t('dataCenter-Mxh6ORwjfxy8')}
              </span>
            ),
            key: 'edit'
          },
        rolesList.find((item) => item === 'aim_campaigns_stop') &&
          CheckAuth.checkAuth('aim_campaigns_stop') && {
            label: (
              <span
                onClick={() => onColumnActionClick('stop', null, data)}
                style={{ display: 'inline-block', width: '100%' }}
              >
                {t('dataCenter-abWKlKA4FILq')}
              </span>
            ),
            key: 'stop'
          },
        rolesList.find((item) => item === 'aim_campaigns_delete') &&
          CheckAuth.checkAuth('aim_campaigns_delete') && {
            label: (
              <span
                onClick={() => onColumnActionClick('delete', null, data)}
                style={{ display: 'inline-block', width: '100%' }}
              >
                {t('dataCenter-xmbk4yliJtB9')}
              </span>
            ),
            key: 'delete'
          },
        {
          label: <span onClick={onCopyUrlChange}>{t('dataCenter-5Y34kyr1Owlp')}</span>,
          key: 'copy'
        }
      ];
    }

    return (
      <>
        <CheckAuth code="aim_campaigns_edit">
          {rolesList.find((item) => item === 'aim_campaigns_edit') && (
            <Button
              type="primary"
              className="bor_ra-6"
              style={{ marginRight: '8px' }}
              onClick={() => setAddComposeVisible(true)}
            >
              {t('dataCenter-Jo9SXvtW0ozm')}
            </Button>
          )}
        </CheckAuth>

        {/* <Button style={{ marginRight: '8px' }} className="bor_ra-6">复制</Button> */}
        <Dropdown menu={{ items }} className="bor_ra-6">
          <Button>{t('dataCenter-h37ccAmRnJwG')}</Button>
        </Dropdown>
      </>
    );
  };

  const onColumnActionClick = (key, feature, record) => {
    setEditValue(record);
    const { id } = record;
    if (key === 'edit') {
      setIsShowEdit({ status: true, idEdit: id });
      // changeVisible(true);
      setVisible(true);
    } else if (key === 'delete') {
      Modal.confirm({
        title: t('dataCenter-xmbk4yliJtB9'),
        className: 'campainsDelWrap',
        content: <p className="confirmDelete">{t('dataCenter-FtDbOIPFB2EG')}{record.name}{t('dataCenter-vFoNQoB8mBBR')}</p>,
        okText: t('dataCenter-JfW4IvqvHB9a'),
        okType: 'danger',
        cancelText: t('dataCenter-xujEOCdXqerA'),
        async onOk() {
          await campaignsService.delCampaigns(data?.id);
          message.success(t('dataCenter-P4l2EuJg2KfA'));
          props.history.push('/aimarketer/home/<USER>');
          // setSearch([...searchRef.current]);
        },
        onCancel() {}
      });
    } else if (key === 'stop') {
      Modal.confirm({
        title: t('dataCenter-RKFpao6EtOou'),
        className: 'campainsDelWrap',
        content: <p className="confirmDelete">{t('dataCenter-Yg5zEf09TGAJ')}{record.name}{t('dataCenter-vFoNQoB8mBBR')}</p>,
        okText: t('dataCenter-DEcnj5BxqJ4d'),
        okType: 'danger',
        cancelText: t('dataCenter-zKkxyLwAsR94'),
        async onOk() {
          await campaignsService.stopCampaigns(data?.id);
          saveRecordInfo(id, userId);
          message.success(t('dataCenter-90PwsQx1hSUa'));
          setRefresh(!Refresh);
          // setSearch([...searchRef.current]);
        },
        onCancel() {}
      });
    }
  };

  const reflash = () => {
    setRefresh(!Refresh);
  };

  const onTabChange = (key) => {
    saveRecordInfo(id, userId);
    setTabKey(key);
  };

  const handleBack = () => {
    props.history.push('/aimarketer/home/<USER>');
  };

  const TabsItems = [
    {
      label: t('dataCenter-YTtvGkbNU0dZ'),
      key: '1',
      children: (
        <Situation
          campaignId={id}
          state={state}
          dispatch={dispatch}
          userId={userId}
          editValue={editValue}
          situationRefresh={state.situationRefresh}
          rolesList={rolesList}
          data={data}
        />
      ),
      disabled: loading
    },
    {
      label: t('dataCenter-0axEPDrcGyFw'),
      key: '2',
      children: (
        <CompaignsActivityAnalysis
          campaignId={id}
          rolesList={rolesList}
          state={state}
          dispatch={dispatch}
          history={props.history}
          campaignsData={data}
          userId={userId}
          situationRefresh={state.situationRefresh}
        />
      ),
      disabled: loading
    },
    {
      label: t('dataCenter-JnmaVuyaROdA'),
      key: '3',
      children: (
        <Compose
          rolesList={rolesList}
          campaignId={id}
          state={state}
          dispatch={dispatch}
          history={props.history}
          userId={userId}
          situationRefresh={state.situationRefresh}
        />
      ),
      disabled: loading
    }
  ];

  const composeTabsItems = [
    {
      label: t('dataCenter-L5mtfBn4Pa5O'),
      key: '1',
      children: (
        <ProcessCanvas
          dispatch={dispatch}
          state={state}
          onClose={onDrawerClose}
          onSave={onSave}
          campaignId={id}
          data={data}
        />
      )
    }
  ];

  return (
    <>
      <Spin tip="Loading..." spinning={false}>
        <div className="campainsDatails" style={rolesLoading ? { display: 'none' } : {}}>
          <div className="Details">
            <div className="Breadcrumbs">
              <Breadcrumb>
                <Breadcrumb.Item>
                  <a onClick={handleBack}>{t('dataCenter-7BE1IfMkoTRi')}</a>
                </Breadcrumb.Item>
                <Breadcrumb.Item>
                  <a onClick={() => props.history.push('/aimarketer/home/<USER>/shareList')}>{t('dataCenter-c78lifFPGMLI')}</a>
                </Breadcrumb.Item>
                <Breadcrumb.Item>
                  <span>{t('dataCenter-6up6MpJih0Rv')}</span>
                </Breadcrumb.Item>
              </Breadcrumb>
            </div>
            <div className="title">
              <h2>
                {data?.name}
                {data?.status === 'RUNNING' ? (
                  <span className="countDown">
                    {t('dataCenter-DEBGuy77ILry')}
                    <Countdown value={Date.now() + (data?.endTime - Date.now())} format={t('dataCenter-du4dcbYDKjvn')} />
                  </span>
                ) : null}
              </h2>
              {/* <Button onClick={() => props.history.push(`/aimarketer/home/<USER>/edit/${getIdFromUrl()}`)}>编辑</Button> */}
              <div className="btn-group">{getGroupButtons(data?.status, data)}</div>
            </div>
            <Descriptions column={2}>
              <Descriptions.Item label="ID">{data?.orderNo}</Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-cN8TrA3MQqVJ')}>{data?.createUserName}</Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-VOKP4acEnLPI')}>{data?.owner?.name}</Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-1TCxnKzxrhtJ')}>
                {dayjs(data?.createTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-M81OG6BvRbUi')}>{dayjs(data?.startTime).format('YYYY年MM月DD日')}</Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-kfEwnWA3RUwf')}>{data?.updateUserName}</Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-xKjzTfv9o44p')}>{dayjs(data?.endTime).format('YYYY年MM月DD日')}</Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-b9Tvxf2Vr7qT')}>
                {dayjs(data?.updateTime).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-mQJBoDmwOkhR')}>
                <Text style={{ width: 150 }} ellipsis={{ tooltip: data?.memo }}>
                  {data?.memo}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label={t('dataCenter-gtBhKmGq7Nrd')}>{data?.shareName}</Descriptions.Item>
            </Descriptions>
          </div>
          <section>
            <Tabs activeKey={tabKey} items={TabsItems} onTabClick={onTabChange} />
          </section>
          {visible && (
            <AddCampains
              visible={visible}
              history={props.history}
              action={() => setVisible(false)}
              editValue={editValue}
              isShowEdit={isShowEdit}
              userId={userId}
              refresh={reflash}
              onClose={() => {
                setVisible(false);
                setIsShowEdit({ status: false, idEdit: null });
                setEditValue({});
              }}
            />
          )}

          <Drawer
            title={t('dataCenter-qpwGRPenDLNX')}
            width={960}
            open={addComposeVisible}
            destroyOnClose
            onClose={onDrawerClose}
            className="addComposeDrawer"
          >
            <Tabs defaultActiveKey="1" tabPosition="left" items={composeTabsItems} />
          </Drawer>

          <Modal
            open={noRolesOpen}
            className="noAutoCampaignsModal"
            centered
            footer={
              <Button type="primary" onClick={() => props.history.push('/aimarketer/home/<USER>/shareList')}>
                {t('dataCenter-CZm3F9nryW3u')}
              </Button>
            }
          >
            <div>
              <div className="mb-[8px] text-[16px] font-[600]">
                <ExclamationCircleOutlined className="text-[#FFC53D] text-[22px]" />
                <span className="ml-[16px]">{t('dataCenter-A44LXAZJJdOy')}</span>
              </div>

              <div className="ml-[38px]">
                <div className="text-[rgba(0,0,0,.65)]">{t('dataCenter-nc7RgCSQ3MOP')}</div>
              </div>
            </div>
          </Modal>
        </div>
      </Spin>
    </>
  );
};

export default ShareCampainsDetails;
