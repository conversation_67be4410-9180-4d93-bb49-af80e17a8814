/*
 * @Author: 卜广硕 <EMAIL>
 * @Date: 2022-12-19 12:12:18
 * @LastEditors: 卜广硕 <EMAIL>
 * @LastEditTime: 2023-03-01 14:45:20
 * @FilePath: \Front\src\pages\home\campaigns\details\tabs\SituationTab\processCanvasPanel\processCanvasPanel.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import CampaignsService from '@/service/CampaignsService';
import { Table } from 'antd';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { t } from 'utils/translation';

const campaignV2Service = new CampaignsService();

const ProcessPanel = (props) => {
  const { campaignsList } = props;
  const [dataSource, setDataSource] = useState([]);
  const [passedTotal, setPassedTotal] = useState(0);
  const [joinTotal, setJoinTotal] = useState(0);
  const [proessStatus, setProcessStatus] = useState(null);

  const history = useHistory();

  useEffect(() => {
    const getProcess = async () => {
      const processAuth = await campaignV2Service.getProcessByType({
        type: 'CAMPAIGN',
        projectId: localStorage.getItem('projectId'),
        status: 'ENABLE'
      });
      setProcessStatus(processAuth.status);
    };
    getProcess();
  }, []);

  useEffect(() => {
    const init = async () => {
      setDataSource(campaignsList);
      const _campaignsList = _.cloneDeep(campaignsList);
      let passed_count_total = 0;
      let join_count_total = 0;

      _campaignsList.forEach((item) => {
        passed_count_total += item.campaignV2?.passedCount || 0;
        join_count_total += item.campaignV2?.joinCount || 0;
      });

      setPassedTotal(passed_count_total);
      setJoinTotal(join_count_total);
    };
    init();
  }, [campaignsList]);

  const columns = [
    {
      title: t('dataCenter-wOrWUKw38zJq'),
      dataIndex: 'id',
      width: '10%',
      sorter: (a, b) => a.id - b.id,
      render: (text, record) => <div>{record.campaignV2?.id}</div>
    },
    {
      title: t('dataCenter-FoLyafNiJDGj'),
      dataIndex: 'name',
      width: '30%',
      render: (text, record) => (
        <a onClick={() => history.push(`/aimarketer/home/<USER>/detail?id=${record.campaignV2?.id}`)}>
          {record.campaignV2?.name}
        </a>
      )
    },
    {
      title: t('dataCenter-WJrTxUa0Zy9u'),
      dataIndex: 'type',
      ellipsis: true,
      width: '20%',
      render: (text, record) => <div>{record.campaignV2?.scenario.name}</div>
    },
    {
      title: t('dataCenter-Un5dxgxCHGea'),
      dataIndex: 'passedCount',
      width: '20%',
      ellipsis: true,
      sorter: (a, b) => a.id - b.id,
      render: (text, record) => (
        <span>
          {proessStatus && proessStatus === 'ENABLE'
            ? renderJoinAndPassedCount(record.campaignV2, record.campaignV2?.passedCount)
            : renderNoProcessJoinAndPassedCount(record.campaignV2, record.campaignV2?.passedCount)}
        </span>
      )
    },
    {
      title: t('dataCenter-T7hekLSnvNlk'),
      dataIndex: 'joinCount',
      width: '20%',
      ellipsis: true,
      sorter: (a, b) => a.id - b.id,
      render: (text, record) => (
        <span>
          {proessStatus && proessStatus === 'ENABLE'
            ? renderJoinAndPassedCount(record.campaignV2, record.campaignV2?.joinCount)
            : renderNoProcessJoinAndPassedCount(record.campaignV2, record.campaignV2?.joinCount)}
        </span>
      )
    }
  ];

  const renderJoinAndPassedCount = (record, type) => {
    if (record.phase === 'DRAFT' || record.phase === 'TESTING' || record.phase === 'TEST_SUC') {
      return t('dataCenter-17jUrhzFFbq4');
    } else if (typeof type === 'undefined') {
      return t('dataCenter-17jUrhzFFbq4');
    } else if (
      record.approvalStatus &&
      (record.approvalStatus === 'PENDING' ||
        record.approvalStatus === 'RUNNING' ||
        record.approvalStatus === 'REJECT') &&
      record.phase === 'STOPPED'
    ) {
      return t('dataCenter-17jUrhzFFbq4');
    } else {
      return type;
    }
  };

  const renderNoProcessJoinAndPassedCount = (record, type) => {
    if (record.phase === 'DRAFT' || record.phase === 'TESTING' || record.phase === 'TEST_SUC') {
      return t('dataCenter-17jUrhzFFbq4');
    } else if (typeof type === 'undefined') {
      return t('dataCenter-17jUrhzFFbq4');
    } else {
      return type;
    }
  };

  return (
    <div className="processWrap">
      <Table dataSource={dataSource} columns={columns} pagination={false} rowKey="id" />

      <div className="totalWrap">
        <span className="totalTitle">{t('dataCenter-wYPaEVj78HE5')}</span>
        <span className="totalContent">
          <span className="totalItem">
            {
              <div>
                <span>{passedTotal.toLocaleString()}</span>
              </div>
            }
          </span>
          <span className="totalItem">
            {
              <div>
                <span>{joinTotal.toLocaleString()}</span>
              </div>
            }
          </span>
        </span>
      </div>
    </div>
  );
};

export default ProcessPanel;
