import { message } from 'antd';
import React, { useRef, useState } from 'react';
import { t } from 'utils/translation';
import SelectTime from './index';

export default () => {
  const selectTimeRef = useRef();
  const [dateRange2, setDateRange2] = useState([
    { type: 'RELATIVE', timeTerm: 'DAY', isPast: true, times: 30 },
    { type: 'NOW', timeTerm: 'DAY', isPast: true }
  ]);

  const setSelectTime = (value, flag) => {
    setDateRange2(value);
    if (flag) {
      return message.error(t('dataCenter-GqUApyltIXIC'));
    }
  };
  return (
    <div>
      <SelectTime ref={selectTimeRef} data={dateRange2} showTime className="selectTime" onChange={setSelectTime} />
    </div>
  );
};
