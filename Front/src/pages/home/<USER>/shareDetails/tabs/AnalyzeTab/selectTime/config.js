import dayjs from 'dayjs';
import { t } from 'utils/translation';

const timeTerm = [
  // {
  //   value: 'MINUTE',
  //   label: '分钟',
  //   unit: 'minutes'
  // },
  // {
  //   value: 'HOUR',
  //   label: '小时',
  //   unit: 'hours'
  // },
  {
    value: 'DAY',
    label: t('dataCenter-rpMlVXoYKitd'),
    unit: 'days'
  },
  {
    value: 'WEEK',
    label: t('dataCenter-nMVPAdtTu8fw'),
    unit: 'weeks'
  },
  {
    value: 'MONTH',
    label: t('dataCenter-WIcwsQQdMIgk'),
    unit: 'months'
  }
];

const scorllUnit = 32;

const isPassObj = {
  true: t('dataCenter-Urky5ioEQE1Y'),
  false: t('dataCenter-Aid1QkQafnVV')
};

const getString = (obj, showTime) => {
  let str = '';
  if (typeof obj === 'object') {
    if (obj.type === 'ABSOLUTE') {
      const format = showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
      str = `${dayjs(obj.timestamp).format(format)}`;
    } else if (obj.type === 'RELATIVE') {
      str = `${obj.times}${
        timeTerm.find((n) => n.value === obj.timeTerm)?.label
      }${isPassObj[JSON.stringify(obj.isPast)]}`;
    } else if (obj.type === 'NOW') {
      str = t('dataCenter-IvEssZKEEgH2');
    }
  }

  return str;
};

const getTime = (obj) => {
  let time = dayjs();
  if (obj.type === 'ABSOLUTE') {
    time = dayjs(obj.timestamp);
  } else if (obj.type === 'RELATIVE') {
    const info = timeTerm.find((n) => n.value === obj.timeTerm);
    if (obj.isPast) {
      time = dayjs().subtract(obj.times, info.unit);
    } else {
      time = dayjs().add(obj.times, info.unit);
    }
  }
  return time.valueOf();
};

export { getString, getTime, isPassObj, scorllUnit, timeTerm };
