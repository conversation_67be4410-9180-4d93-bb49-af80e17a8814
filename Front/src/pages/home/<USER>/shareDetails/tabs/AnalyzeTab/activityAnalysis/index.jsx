import {
  CheckCircleFilled,
  CloseOutlined,
  FullscreenOutlined,
  MoreOutlined,
  PlusCircleOutlined
} from '@ant-design/icons';
import { Button, Dropdown, Modal, Spin, Table, Tabs, Tooltip, message } from 'antd';
import React, { useEffect, useReducer, useRef, useState } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import { useHistory } from 'react-router-dom';
import CampaignV2Service from 'service/CampaignV2Service';
import { t } from 'utils/translation';

import CheckAuth from '@/utils/checkAuth';
import dayjs from 'dayjs';
import _ from 'lodash';
import CampaignsService from 'service/CampaignsService';
import { useDebounce } from 'utils/customhooks';
import { MyIcon } from 'utils/myIcon';
import { SelectTime } from 'wolf-static-cpnt';
import ComChart from '../../../../../analysisCenter/database/comChart/index';
import { getTime } from '../selectTime/config';
import DefaultCharts from './defaultCharts';
import RenderAddCharts from './renderAddCharts';
import RenderAddHasBoardCharts from './renderAddHasBoardCharts';

import './index.scss';

const campaignsService = new CampaignsService();

const reducer = (state, action) => ({ ...state, ...action });
const campaignV2Service = new CampaignV2Service();
const ResponsiveReactGridLayout = WidthProvider(Responsive);
const defaultProps = {
  cols: { lg: 12, md: 12, sm: 12, xs: 12, xxs: 12 },
  rowHeight: 100,
  margin: [24, 24],
  breakpoints: { lg: 0, md: 0, sm: 0, xs: 0, xxs: 0 },
  widgets: { h: 4, w: 4, x: 0, y: 0 }
};

let isDelete = false;
let isAdd = false;

const CompaignsActivityAnalysis = (props) => {
  const { rolesList } = props;
  const [batchId, setBatchId] = useState(t('dataCenter-tg5Td8NdO6eN'));
  const [times, setTimes] = useState({
    beginTime: dayjs(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
    calcEndTime: dayjs(Date.now()).format('YYYY-MM-DD HH:mm:ss')
  });
  const history = useHistory();

  const [state, dispatch] = useReducer(reducer, {
    dateRange2: [],
    folderModalVisible: false,
    folderValue: {},
    chartModalVisible: false,
    saveloading: false,
    selectValue: {}, // 活动分析图表
    addselectValue: {}, // 添加图表项目
    addHasSelectValue: {}, // 添加已有图表
    updateTime: '',
    dashboardModalVisible: false,
    dashboardValue: {},
    computingTime: {}, // 上次计算时间
    chartList: [],
    boardId: '',
    optionalTime: {}, // 可选时间段
    allCategoryList: [],
    menuList: [],
    defaultDataList: [],
    refresh: true,
    scenario: props.scenario,
    expandedKeys: [],
    selectedKeys: props.location?.state?.selectedKeys || [],
    refreshSelected: Math.random(), // 用于刷新
    chartBoardList: [],
    loading: false,
    getLoading: false,
    campaignsData: props.campaignsData,
    campaignsList: [],
    newComputingTime: {},
    chartsReflash: false,
    switchState: false,
    config: {}
    // infos: {
    //   dateRange2: [{ type: 'ABSOLUTE', timeTerm: 'DAY', isPast: true, times: 7, timestamp: props.campaignsData.campaigns?.startTime }, { type: 'ABSOLUTE', timeTerm: 'DAY', isPast: true, timestamp: campaignsData.id }]
    // }
  });

  const {
    chartList,
    computingTime,
    scenario,
    refresh,
    loading,
    selectValue,
    addHasSelectValue,
    dateRange2,
    chartsReflash,
    campaignsData
    // infos
  } = state;

  const [forceCalc, setForceCale] = useState(0);
  const { layouts = {}, widgets = [] } = selectValue;
  const campaignId = window.location.pathname.substring(window.location.pathname.lastIndexOf('/') + 1);
  const [saveResult, setSaveResult] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [tabKey, setTabKey] = useState(localStorage.getItem('env') !== 'NS' ? 'ActivityBoard' : 'hasBoard');
  const [domLoading, setDomLoading] = useState(false);
  const [itemWidth, setItemWidth] = useState(0);
  const [boardLoading, setBoardLoading] = useState(false);
  // const [composeData, setComposeData] = useState([]);

  const timer = useRef(null);

  const isGetLayouts = useRef(false);

  useEffect(() => {
    init();
    return () => {
      timer.current && clearTimeout(timer.current);
      // localStorage.removeItem('timeFilter');
    };
  }, []);

  const init = async () => {
    dispatch({ loading: true });
    // const data = await campaignsService.getCampaigns(campaignId);
    const data = await campaignsService.getCampaignsV2({
      id: Number(campaignId),
      deptId: window.getDeptId(),
      type: 'share'
    });
    // const getCampainsList = await campaignsService.findCampaigns([
    //   { operator: 'EQ', propertyName: 'campaignsId', value: campaignId }
    // ]);
    // setComposeData(getCampainsList)
    const selectValue = await campaignV2Service.campaignV2ChartBoardListBy([
      {
        operator: 'EQ',
        propertyName: 'campaignId',
        value: campaignId
      },
      {
        operator: 'EQ',
        propertyName: 'type',
        value: 'CAMPAIGNS'
      }
    ]);

    const { config } = await campaignsService.getBusiness({
      businessType: 'SegmentBusinessConfig'
    });

    dispatch({ config });
    // selectValue[0] = selectValue[selectValue.length - 1];

    if (!_.isEmpty(selectValue)) {
      try {
        const chartList = await campaignV2Service.getBoardCharts({
          id: Number(selectValue[0]?.id),
          deptId: Number(data.shareUserDeptId)
        });
        isGetLayouts.current = true;
        if (!_.isEmpty(selectValue[0])) {
          // if (!_.isEmpty(props.dataList)) {
          //   getTime(resultData.content);
          // } else { // 如果没有批次就取当前时间戳的开始时间合结束时间
          //   setTimes({
          //     beginTime: new Date().getTime(),
          //     calcEndTime: new Date().getTime()
          //   });
          // }

          const timeFilter = JSON.parse(localStorage.getItem('timeFilter'));
          const state = localStorage.getItem('dateState');

          // 如果是全屏或者编辑页面跳转回来的 就把上次筛选时间写上去
          if (timeFilter && state) {
            const filterConfig = JSON.parse(localStorage.getItem('filterConfig'));
            if (filterConfig) {
              setBatchId(filterConfig.batchId);
              localStorage.removeItem('filterConfig');
            }

            const timeFilterArr = timeFilter.split(',');

            setTimes({
              beginTime: JSON.parse(timeFilterArr[0]),
              calcEndTime: JSON.parse(timeFilterArr[1])
            });
            await updateDatalist(JSON.parse(timeFilterArr[0]), JSON.parse(timeFilterArr[1]), dateRange2);
            dispatch({
              computingTime: {
                startTime: JSON.parse(timeFilterArr[0]),
                endTime: JSON.parse(timeFilterArr[1])
              }
            });
            localStorage.removeItem('dateState');
            localStorage.removeItem('timeFilter');
          }

          dispatch({
            selectValue: selectValue[0],
            getLoading: false,
            chartList: chartList.body,
            boardId: selectValue[0]?.id,
            loading: false,
            updateTime: selectValue[0]?.updateTime,
            campaignsData: data
          });
          localStorage.removeItem('dateRange');
        }
      } catch (err) {
        dispatch({
          getLoading: false,
          loading: false
        });
      }
    } else {
      dispatch({
        selectValue: {
          layouts: {},
          widgets: []
        },
        getLoading: false,
        chartList: chartList?.body,
        selectedKeys: [],
        loading: false,
        refresh: !refresh
      });
    }
    // debugger;
  };

  const saveRecordInfo = async (id, userId) => {
    await campaignsService.saveUserOperationRecord({
      targetId: id,
      id: campaignsData?.recentUserOperationRecord?.id,
      targetType: 'CAMPAIGNS',
      type: 'RECENT',
      createUserId: userId,
      updateUserId: userId,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
  };

  useEffect(() => {
    dispatch({
      dateRange2: props.campaignsData
        ? props.campaignsData.status === 'ENDED'
          ? [
              {
                type: 'ABSOLUTE',
                timeTerm: 'DAY',
                isPast: true,
                times: 7,
                timestamp: props.campaignsData?.startTime
              },
              {
                type: 'ABSOLUTE',
                timeTerm: 'DAY',
                isPast: true,
                timestamp: props.campaignsData?.endTime
              }
            ]
          : [
              {
                type: 'ABSOLUTE',
                timeTerm: 'DAY',
                isPast: true,
                times: 7,
                timestamp: props.campaignsData?.startTime
              },
              { type: 'NOW', timeTerm: 'DAY', isPast: true }
            ]
        : JSON.parse(localStorage.getItem('dateRange'))
    });
  }, [props.campaignsData]);

  useEffect(() => {
    let time = null;
    const getHeart = async () => {
      if (JSON.stringify(selectValue) !== '{}') {
        await campaignV2Service.heartbeat({ id: selectValue?.id });
      }
    };

    getHeart();
    time = setInterval(getHeart, 5000);

    return () => clearInterval(time);
  }, [selectValue]);
  // const isGetLayouts = useRef(false);

  const modalShow = () => {
    setModalVisible(true);
  };

  const modalClose = () => {
    setModalVisible(false);
  };

  const delData = async (id) => {
    isDelete = true;
    const findIndex = _.findIndex(widgets, (item) => item.i === id);
    if (findIndex !== -1) {
      widgets.splice(findIndex, 1);
      dispatch({
        selectValue: {
          ...selectValue,
          deleteId: id,
          version: saveResult.version
        }
      });
    }
  };

  const onEdit = (e, id) => {
    saveRecordInfo(campaignId, props.userId);
    const chartEnum = {
      NEW_FUNNEL: 'funnelAnalysis',
      RETENTION_ANALYSIS: 'retentionAnalysis',
      EVENT_ANALYSIS: 'eventAnalysis'
    };
    const type = chartList.find((item) => item.id === Number(id)).chartType;
    e.domEvent.preventDefault();
    e.domEvent.stopPropagation();
    localStorage.setItem('isActivityAnalysis', 'true');
    localStorage.setItem('activityCache', 'true');
    localStorage.setItem('dateState', true);
    localStorage.setItem('dateRange', JSON.stringify(dateRange2));
    localStorage.setItem(
      'filterConfig',
      JSON.stringify({
        batchId
      })
    );

    if (forceCalc !== 0 || localStorage.getItem('timeFilter')) {
      if (type === 'NEW_FUNNEL' || type === 'RETENTION_ANALYSIS' || type === 'EVENT_ANALYSIS') {
        // history.push(`/aimarketer/home/<USER>/analysisCenter/database/funnelAnalysis/${id}?calcState=${!!localStorage.getItem('timefilter')}`);
        history.push(
          `/aimarketer/home/<USER>/analysisCenter/database/${
            chartEnum[type]
          }/${id}?&timeFilter=${getTime(dateRange2[0])},${getTime(
            dateRange2[1]
          )}&campaignId=${campaignId}&boardType=campaigns&version=${
            saveResult.version
          }&saveId=${saveResult.version && saveResult.id}`
        );
      } else {
        history.push(
          `/aimarketer/home/<USER>/database/edit/${id}?timeFilter=${getTime(
            dateRange2[0]
          )},${getTime(dateRange2[1])}`,
          { campaignId, boardChart: 'campaigns' }
        );
      }
    } else {
      if (type === 'NEW_FUNNEL' || type === 'RETENTION_ANALYSIS' || type === 'EVENT_ANALYSIS') {
        history.push(
          `/aimarketer/home/<USER>/analysisCenter/database/${
            chartEnum[type]
          }/${id}?campaignId=${campaignId}&boardType=campaigns&version=${
            saveResult.version
          }&saveId=${saveResult.version && saveResult.id}`
        );
      } else {
        history.push(`/aimarketer/home/<USER>/database/edit/${id}`, {
          campaignId,
          boardChart: 'campaigns'
        });
      }
    }
  };

  /* 触发防抖函数 */
  const onLayoutChange = useDebounce((layout) => {
    selectValue.version = saveResult.version;
    saveRecordInfo(campaignId, props.userId);
    if (!_.isEmpty(layout)) {
      dispatch({
        selectValue: {
          ...selectValue,
          version: saveResult.version,
          layouts: {
            xxs: layout
          },
          widgets: layout
        }
      });
    }
    isGetLayouts.current = false;
  }, 1500);

  // useEffect(() => {
  //   if(localStorage.getItem('campaignsChart')) {

  //   }
  // },[localStorage.getItem('campaignsChart')]);

  useEffect(() => {
    const save = async () => {
      setBoardLoading(true);
      // const campaignsChart = localStorage.getItem('campaignsChart')
      //   ? JSON.parse(localStorage.getItem('campaignsChart'))
      //   : null;
      if (JSON.stringify(selectValue) !== '{}' && selectValue.id && addHasSelectValue.type !== 'a') {
        // 调用保存接口
        if (isAdd) {
          // if (_.isEmpty(selectValue.layouts) && selectValue.widgets.length) {
          if (selectValue.widgets.length) {
            const res = await campaignV2Service.saveCampaignV2ChartBoard({
              ...selectValue,
              campaignId,
              type: 'CAMPAIGNS'
            });
            isAdd = false;
            setSaveResult(res);
          }
        } else {
          const res = await campaignV2Service.saveCampaignV2ChartBoard({
            ...selectValue,
            campaignId,
            type: 'CAMPAIGNS'
          });
          isAdd = false;
          setSaveResult(res);
        }

        if (isDelete) {
          await campaignsService.delCampaignChart(selectValue.deleteId);
          isDelete = false;
          // const res = await campaignV2Service.saveCampaignV2ChartBoard({
          //   ...selectValue,
          //   campaignId
          // });
          // setSaveResult(res);
        }
      }
      setBoardLoading(false);
      // saveRecordInfo(campaignId, props.userId);
    };
    save();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(selectValue)]);

  // useEffect(() => {
  //   const save = async () => {
  //     if (selectValue.id && addHasSelectValue.type !== 'a') {
  //       // 调用保存接口
  //       const res = await campaignV2Service.saveCampaignV2ChartBoard({
  //         ...selectValue,
  //         campaignId,
  //         type: 'CAMPAIGNS'
  //       });
  //       setSaveResult(res);
  //     }
  //   };
  //   save();
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [JSON.stringify(selectValue)]);

  const save = async () => {
    if (_.isEmpty(state.addselectValue)) {
      return message.error(t('dataCenter-xJDKhfv5ptqe'));
    }
    isAdd = true;
    const getCampainsList = await campaignsService.findCampaigns([
      { operator: 'EQ', propertyName: 'campaignsId', value: campaignId }
    ]);

    const newxxs = _.map(state.addselectValue.widgets, (n) => {
      return {
        ...n,
        ...{ static: false, isResizable: true, isDraggable: true }
      };
    });
    state.addselectValue.layouts.xxs = newxxs;
    const widgets = state.addselectValue.widgets;
    const filterWidgets = [];

    _.forEach(state.chartList, (item) => {
      _.forEach(widgets, (widget) => {
        if (parseInt(widget.i) === item.id) {
          if (
            _.find(saveResult.widgets, (o) => {
              return o.i === widget.i;
            })
          ) {
            filterWidgets.push({ ...item, isUnique: true });
          } else if (item.scenario && !_.find(getCampainsList, (o) => o.campaignV2.scenario?.id === item.scenario.id)) {
            filterWidgets.push({ ...item, isUnique: false, isType: true });
          } else {
            filterWidgets.push({
              ...item,
              isUnique: false,
              isType: false,
              saveState: true
            });
          }
        }
      });
    });

    // _.forEach(state.chartList, item => {
    //   _.forEach(widgets, widget => {
    //     if (parseInt(widget.i) === item.id) {
    //       if (_.find(saveResult.widgets, (o) => { return o.i === widget.i; })) {
    //         filterWidgets.push({ ...item, isUnique: true });
    //       } else if (item.scenario && item?.scenario.id !== scenario?.id) {
    //         filterWidgets.push({ ...item, isUnique: false, isType: true });
    //       } else {
    //         filterWidgets.push({ ...item, isUnique: false, isType: false, saveState: true });
    //       }
    //     }
    //   });
    // });

    const errorCharts = [];
    _.forEach(filterWidgets, (item) => {
      if (item.scenario && !_.find(getCampainsList, (o) => o.campaignV2.scenario?.id === item.scenario.id)) {
        errorCharts.push({ name: item.name, scenarioName: t('dataCenter-ThgdpePgYE3G') });
      } else if (item.isUnique) {
        errorCharts.push({ name: item.name, scenarioName: t('dataCenter-HWFBpkewiLia') });
      } else {
        errorCharts.push({ name: item.name, scenarioName: '' });
      }
    });

    if (
      _.isEmpty(errorCharts) ||
      _.every(errorCharts, (o) => {
        return o.scenarioName === '';
      })
    ) {
      del();
      dispatch({ saveloading: true });
      try {
        const allSelectValue = selectValue.widgets.length
          ? {
              ...selectValue,
              id: saveResult.id,
              widgets: [...selectValue.widgets, ...state.addselectValue.widgets],
              layouts: {
                ...selectValue.layouts,
                xxs: [...selectValue.layouts.xxs, ...state.addselectValue.layouts.xxs]
              }
            }
          : state.addselectValue;

        const res = await campaignV2Service.saveCampaignV2ChartBoard({
          ...allSelectValue,
          campaignId,
          type: 'CAMPAIGNS',
          version: saveResult.version,
          id: selectValue.version && saveResult.id
        });

        setSaveResult(res);
        init();
        dispatch({ saveloading: false });
        saveRecordInfo(campaignId, props.userId);
        message.success(t('dataCenter-XGvv6yjVCgTE'));
        setTimeout(() => modalClose(), 1000);
      } catch (err) {
        dispatch({ saveloading: false });
      }
    } else {
      const saveList = filterWidgets.filter((item) => item.saveState);

      if (!_.isEmpty(saveList)) {
        const result = [];
        saveList.forEach((item) => {
          state.addselectValue.widgets.forEach((widItem, widIndex) => {
            if (widItem.i === item.id.toString()) {
              result.push(state.addselectValue.widgets[widIndex]);
            }
          });
        });

        const allSelectValue = [...selectValue.widgets, ...result];
        const res = await campaignV2Service.saveCampaignV2ChartBoard({
          widgets: allSelectValue,
          campaignId,
          type: 'CAMPAIGNS',
          version: saveResult.version,
          id: selectValue.version && saveResult.id
        });
        setSaveResult(res);
        init();
        dispatch({ saveloading: false });
        modalClose();
      }

      warning(errorCharts);
    }
  };

  const hasSave = async () => {
    // if (_.isEmpty(state.addselectValue)) {
    //   return message.error('未选择看板');
    // }
    // let newxxs = _.map(selectValue.widgets, (n) => {
    //   return { ...n, ...{ static: false, isResizable: true, isDraggable: true } };
    // });
    // selectValue.layouts.xxs = newxxs;
    isAdd = true;
    const widgets = addHasSelectValue.widgets;
    const filterWidgets = [];
    _.forEach(state.chartList, (item) => {
      _.forEach(widgets, (widget) => {
        if (parseInt(widget.i) === item.id) {
          filterWidgets.push(item);
        }
      });
    });
    const errorCharts = [];
    _.forEach(filterWidgets, (item) => {
      if (item.scenario && item.scenario.id !== scenario?.id) {
        errorCharts.push({ name: item.name, scenarioName: t('dataCenter-ThgdpePgYE3G') });
      }
    });

    del();
    dispatch({ saveloading: true });
    try {
      await campaignV2Service.saveCampaignV2ChartBoard({
        ...addHasSelectValue,
        campaignId,
        type: 'CAMPAIGNS',
        version: saveResult.version,
        id: saveResult.version && saveResult.id
      });
      init();
      dispatch({ saveloading: false });
      saveRecordInfo(campaignId, props.userId);
      message.success(t('dataCenter-XGvv6yjVCgTE'));
      setTimeout(() => modalClose(), 1000);
    } catch (err) {
      dispatch({ saveloading: false });
    }
  };

  const onClickDetail = (id) => {
    saveRecordInfo(campaignId, props.userId);
    localStorage.setItem('isActivityAnalysis', 'true');
    localStorage.setItem('activityCache', 'true');
    localStorage.setItem('dateRange', JSON.stringify(dateRange2));
    localStorage.setItem(
      'filterConfig',
      JSON.stringify({
        batchId
      })
    );
    if (forceCalc !== 0 || localStorage.getItem('timeFilter')) {
      history.push(
        `/aimarketer/home/<USER>/database/detail/${id}?fullScreen=${true}&timeFilter=${getTime(
          dateRange2[0]
        )},${getTime(dateRange2[1])}`
      );
    } else {
      history.push(`/aimarketer/home/<USER>/database/detail/${id}?fullScreen=${true}`);
    }
  };

  const ref = async (obj) => {
    if (getTime(dateRange2[0]) > getTime(dateRange2[1])) {
      return message.error(t('dataCenter-7XOnwNYXs8ZG'));
    }
    await updateDatalist(times.beginTime, times.calcEndTime, dateRange2);
    setForceCale(obj);
    dispatch({
      loading: false,
      selectValue,
      computingTime: {
        startTime: getTime(dateRange2[0]),
        endTime: getTime(dateRange2[1])
      },
      chartsReflash: !chartsReflash
    });
  };

  const updateDatalist = (beginTime, calcEndTime, dateRange2) => {
    const _chartList = _.cloneDeep(chartList);
    dateRange2.forEach((item) => {
      if (item.type !== 'ABSOLUTE') {
        delete item.timestamp;
      }
    });

    const newChartList = _.map(_chartList, (item) => {
      return {
        ...item,
        dateRange2
      };
    });
    dispatch({ chartList: newChartList, widgets });
  };

  const change = (value) => {
    saveRecordInfo(campaignId, props.userId);
    dispatch({
      dateRange2: value
    });
  };

  const del = () => {
    dispatch({
      selectValue: {
        layouts: {},
        widgets: []
      },
      computingTime: {}
    });
    setBatchId(t('dataCenter-tg5Td8NdO6eN'));
    localStorage.removeItem('timeFilter');
    setForceCale(0);
    init();
  };

  const reflashCalc = (forceCalc) => {
    saveRecordInfo(campaignId, props.userId);
    ref(forceCalc + 1);
  };

  const warning = (errorCharts) => {
    const successCount = errorCharts.filter((item) => item.scenarioName === '').length;
    const columns = [
      {
        key: 'state',
        dataIndex: 'state',
        title: t('dataCenter-czyurPFXDUdf'),
        width: 120,
        ellipsis: {
          showTitle: false
        },
        render: (val, record) => (
          <CheckCircleFilled
            style={{
              color: record.scenarioName === '' ? '#52C41A' : 'rgba(0, 0, 0, 0.15)',
              fontSize: '18px'
            }}
          />
        )
      },
      {
        key: 'name',
        dataIndex: 'name',
        title: t('dataCenter-ZGUcWlo2alSg'),
        ellipsis: {
          showTitle: false
        },
        render: (text) => <Tooltip title={text}>{text}</Tooltip>
      },
      {
        key: 'scenarioName',
        dataIndex: 'scenarioName',
        title: t('dataCenter-XevzIsB1bPXK'),
        width: 200,
        ellipsis: {
          showTitle: false
        },
        render: (text) => <Tooltip title={text}>{text}</Tooltip>
      }
    ];
    // dataSource
    Modal.warning({
      width: 662,
      title: `${successCount}${t('dataCenter-BvKBrgC369wy')}${errorCharts.length - successCount}${t('dataCenter-2ZlPMCJRZNIt')}`,
      content: (
        <div>
          <Table
            // bordered
            dataSource={errorCharts}
            columns={columns}
            pagination={false}
            scroll={{ y: 500 }}
          />
        </div>
      )
    });
  };

  const addNewChartClick = (type, route) => {
    // history.push(route, {
    //   campaignId,
    //   boardType: 'campaigns',
    //   hasWidgets: selectValue.widgets,
    //   version: saveResult.version,
    //   id: saveResult.version && saveResult.id,
    //   visibleState: type === 'BASE' && true
    // });

    history.push(
      `${route}?campaignId=${campaignId}&boardType=campaigns&version=${
        saveResult.version
      }&saveId=${saveResult.version && saveResult.id}&visibleState=${type === 'BASE' && true}`,
      {
        campaignId,
        boardType: 'campaigns',
        hasWidgets: selectValue.widgets,
        version: saveResult.version,
        id: saveResult.version && saveResult.id,
        visibleState: type === 'BASE' && true
      }
    );
  };

  const TabsItems = [
    localStorage.getItem('env') !== 'NS' && {
      label: t('dataCenter-WpNtTBWaf9FG'),
      key: 'ActivityBoard',
      children: (
        <div className="ActivityBoard">
          <RenderAddCharts state={state} dispatch={dispatch} onClose={modalClose} save={save} />
        </div>
      )
    },
    {
      label: t('dataCenter-CHBV0mcBZlsM'),
      key: 'hasBoard',
      children: (
        <div className="ActivityAnalysisStyleHasBoard">
          <RenderAddHasBoardCharts state={state} dispatch={dispatch} onClose={modalClose} save={hasSave} />
        </div>
      )
    },
    CheckAuth.checkAuth('aim_chart_anaysis_edit') && {
      label: t('dataCenter-zlDI3jurwoLS'),
      key: 'addBoard',
      children: (
        <div className="addBoardWrap">
          <div
            className="addItem"
            onClick={() =>
              // history.push('/aimarketer/home/<USER>/database', {
              //   visibleState: true,
              //   campaignId,
              //   boardType: 'campaigns'
              // })
              addNewChartClick('BASE', '/aimarketer/home/<USER>/database')
            }
          >
            <div className="titleWrap">
              <MyIcon type="icon-icon-box" className="chartIcon" />
              <span className="title">{t('dataCenter-tYhIVdjdbAnO')}</span>
            </div>
            <div className="desc">{t('dataCenter-IAqbJC7hOzST')}</div>
          </div>
          <div
            className="addItem"
            onClick={() =>
              // history.push(
              //   '/aimarketer/home/<USER>/analysisCenter/database/funnelAnalysis',
              //   { campaignId, boardType: 'campaigns' }
              // )
              addNewChartClick('RELATION', '/aimarketer/home/<USER>/analysisCenter/database/funnelAnalysis')
            }
          >
            <div className="titleWrap">
              <MyIcon type="icon-a-icon-funnelplot" className="chartIcon" />
              <span className="title">{t('dataCenter-sfXDlrgEfBgA')}</span>
            </div>
            <div className="desc">{t('dataCenter-uZNm2eHxpw0r')}</div>
          </div>
          <div
            className="addItem"
            onClick={() =>
              // history.push(
              //   '/aimarketer/home/<USER>/analysisCenter/database/retentionAnalysis',
              //   { campaignId, boardType: 'campaigns' }
              // )
              addNewChartClick('RELATION', '/aimarketer/home/<USER>/analysisCenter/database/retentionAnalysis')
            }
          >
            <div className="titleWrap">
              <MyIcon type="icon-icon-retaion" className="chartIcon" />
              <span className="title">{t('dataCenter-wG0vr37v3aaj')}</span>
            </div>
            <div className="desc">{t('dataCenter-l7TsmSa9Dfu7')}</div>
          </div>
          {/* <div className="addItem" onClick={() => message.info('功能开发中，敬请期待')}> */}
          <div
            className="addItem"
            onClick={() =>
              addNewChartClick('EVENT', '/aimarketer/home/<USER>/analysisCenter/database/eventAnalysis')
            }
          >
            <div className="titleWrap">
              <MyIcon type="icon-icon-shijianfenxi" className="chartIcon" />
              <span className="title">{t('dataCenter-Fj2oa3nAEHiA')}</span>
            </div>
            <div className="desc">{t('dataCenter-VhTLLhOtoBPn')}</div>
          </div>
        </div>
      )
    }
  ];

  const onResizeStart = () => {
    setDomLoading(true);
  };

  const onResizeEnd = (layouts, preEvent, nextEvent) => {
    setItemWidth(nextEvent.w);
    setTimeout(() => setDomLoading(false), 2000);
  };

  return (
    <div className="compaignsActivityAnalysis">
      <div className="top">
        <div className="batch" style={{ display: 'flex' }}>
          <span className="batchTitle">{t('dataCenter-ko7eDqtWYqBX')}</span>
          <SelectTime data={dateRange2} isAnalysis showTime style={{ width: 628 }} onChange={change} />
        </div>
        {rolesList.find((item) => item === 'aim_campaigns_edit') && (
          <div className="setup">
            {/* <span className="time" style={{ marginRight: '15px' }}>最近计算时间：{dayjs(updateTime).format('YYYY-MM-DD HH:mm:ss')}</span> */}
            <span className="time" style={{ marginRight: '15px' }}>
              {!_.isEmpty(computingTime) && (
                <span>{t('dataCenter-zhtLReiAE4oq')}{dayjs(Date.now()).format('YYYY-MM-DD HH:mm:ss')}</span>
              )}
            </span>
            {localStorage.getItem('env') !== 'NS' ? (
              <span className="add" onClick={() => reflashCalc(forceCalc)} style={{ marginLeft: 0 }}>
                {t('dataCenter-mderrF7MHUa6')}
              </span>
            ) : null}

            {/* <span className="add" onClick={showDrawer}>添加看板</span> */}
            <CheckAuth code="aim_campaigns_edit">
              <span className="add">
                <div className="boardDropdown" onClick={modalShow}>
                  <PlusCircleOutlined style={{ paddingRight: '8px' }} />
                  {t('dataCenter-O3KMEfVWTzvQ')}
                </div>
              </span>
            </CheckAuth>
          </div>
        )}
      </div>

      <div className="contentValue">
        <Spin spinning={loading}>
          {!_.isEmpty(computingTime) && (
            <span className="showTime">
              {`${dayjs(computingTime.startTime).format('YYYY-MM-DD HH:mm:ss')} ~ ${dayjs(computingTime.endTime).format(
                'YYYY-MM-DD HH:mm:ss'
              )}`}
              <span style={{ cursor: 'pointer', marginLeft: '8px' }} onClick={del}>
                <CloseOutlined style={{ width: '7px', height: '7px', fontSize: '12px' }} />
              </span>
            </span>
          )}

          <DefaultCharts
            campaignsData={campaignsData}
            state={state}
            dispatch={dispatch}
            userId={props.userId}
            rolesList={rolesList}
          />
          <Spin spinning={boardLoading}>
            <ResponsiveReactGridLayout
              {...defaultProps}
              className={rolesList.find((item) => item !== 'aim_campaigns_edit') ? 'disableResize' : ''}
              layouts={layouts}
              isResizable
              style={{ width: 'calc(100% - 10px)' }}
              onLayoutChange={onLayoutChange}
              onResizeStart={onResizeStart}
              onResizeStop={onResizeEnd}
              isDraggable={!!rolesList.find((item) => item === 'aim_campaigns_edit')}
            >
              {widgets.map((l) => {
                if (_.isEmpty(selectValue)) return null;
                const info = chartList.find((n) => n.id === parseInt(l.i));
                let time = '';
                if (!_.isEmpty(computingTime)) {
                  time += `${dayjs(computingTime.startTime).format('YYYY-MM-DD HH:mm:ss')} / ${dayjs(
                    computingTime.endTime
                  ).format('YYYY-MM-DD HH:mm:ss')}`;
                } else {
                  // debugger;
                  if (info?.dateTimeStamp) {
                    if (info.dateTimeStamp.beginDate) {
                      time += `${dayjs(info.dateTimeStamp.beginDate).format('YYYY-MM-DD HH:mm:ss')} / `;
                    }
                    if (info.dateTimeStamp.endDate) {
                      time += dayjs(info.dateTimeStamp.endDate).format('YYYY-MM-DD HH:mm:ss');
                    }
                  }
                }

                if (info) {
                  return (
                    <div className="hasInfo" key={l.i} data-grid={l}>
                      <div className="toolbar">
                        <div className="toolbarLeft">
                          <div className="title">{info?.name}</div>
                          <div className="time">{time}</div>
                        </div>
                        {rolesList.find((item) => item === 'aim_campaigns_edit') && (
                          <div className="right">
                            <FullscreenOutlined
                              onClick={() => {
                                onClickDetail(l.i);
                              }}
                              style={{ fontSize: 16, marginRight: 10 }}
                            />
                            <Dropdown
                              placement="bottomRight"
                              getPopupContainer={(triggerNode) => triggerNode.parentNode}
                              menu={{
                                items: [
                                  {
                                    label: t('dataCenter-Mxh6ORwjfxy8'),
                                    onClick: (e) => {
                                      onEdit(e, l.i);
                                    }
                                  },
                                  {
                                    label: t('dataCenter-xmbk4yliJtB9'),
                                    onClick: () => {
                                      delData(l.i);
                                    }
                                  }
                                ]
                              }}
                            >
                              <Button type="default" icon={<MoreOutlined />} size="small" shape="circle" />
                            </Dropdown>
                          </div>
                        )}
                      </div>
                      <div
                        className={l.w >= 8 ? 'content lgWrap' : 'content smWrap'}
                        style={{
                          height: 'calc(100% - 40px)',
                          padding: '4px 16px 16px 16px'
                        }}
                      >
                        <ComChart info={info} forceCalc={forceCalc} domLoading={domLoading} itemWidth={itemWidth} />
                      </div>
                    </div>
                  );
                }
                return null;
              })}
            </ResponsiveReactGridLayout>
          </Spin>
        </Spin>
      </div>

      <Modal
        title={t('dataCenter-O3KMEfVWTzvQ')}
        width={tabKey === 'addBoard' ? 640 : 1280}
        onCancel={modalClose}
        className="addCampaignChartModal"
        // onOk={save}
        open={modalVisible}
        footer={null}
        // bodyStyle={{
        //   paddingBottom: 80,
        //   paddingLeft: 0
        // }}
        destroyOnClose
      >
        <Tabs activeKey={tabKey} destroyInactiveTabPane onChange={(e) => setTabKey(e)} items={TabsItems} />
      </Modal>
    </div>
  );
};

export default CompaignsActivityAnalysis;
