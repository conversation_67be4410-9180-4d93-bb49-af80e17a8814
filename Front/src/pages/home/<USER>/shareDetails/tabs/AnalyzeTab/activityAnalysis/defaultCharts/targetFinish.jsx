import { HolderOutlined, SwapOutlined } from '@ant-design/icons';
import { Chart } from '@antv/g2';
import { useToggle } from '@umijs/hooks';
import { Button, Empty, Popover, Spin, Table } from 'antd';
import dayjs from 'dayjs';
import update from 'immutability-helper';
import _ from 'lodash';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import CampaignsService from 'service/CampaignsService';
import { t } from 'utils/translation';

import '../index.scss';

const campaignsService = new CampaignsService();

const timeTerm = [
  {
    value: 'DAY',
    label: t('dataCenter-rpMlVXoYKitd'),
    unit: 'days'
  },
  {
    value: 'WEEK',
    label: t('dataCenter-nMVPAdtTu8fw'),
    unit: 'weeks'
  },
  {
    value: 'MONTH',
    label: t('dataCenter-WIcwsQQdMIgk'),
    unit: 'months'
  }
];

const type = 'DraggableBodyRow';

const DraggableBodyRow = ({ index, moveRow, className, style, ...restProps }) => {
  const ref = useRef(null);
  const [{ isOver, dropClassName }, drop] = useDrop({
    accept: type,
    collect: (monitor) => {
      const { index: dragIndex } = monitor.getItem() || {};

      if (dragIndex === index) {
        return {};
      }

      return {
        isOver: monitor.isOver(),
        dropClassName: dragIndex < index ? ' drop-over-downward' : ' drop-over-upward'
      };
    },
    drop: (item) => {
      moveRow(item.index, index);
    }
  });

  const [{ isDragging }, drag] = useDrag({
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    }),
    // item 中包含 index 属性，则在 drop 组件 hover 和 drop 是可以根据第一个参数获取到 index 值
    item: { type, index }
    // end: (item) => {
    //   if (!item) {
    //     return;
    //   }
    //   end(item.id, item.index);
    // }
  });
  drop(drag(ref));

  // const style = {
  //   margin: '16px 6px',
  //   // Card 为占位元素是，透明度 0.4，拖拽状态时透明度 0.2，正常情况透明度为 1
  //   opacity: id === -1 ? 0.4 : isDragging ? 0.2 : 1
  // };

  return (
    <tr
      ref={ref}
      className={`${className}${isOver ? dropClassName : ''}`}
      style={{
        cursor: 'move',
        opacity: index === -1 ? 0.4 : isDragging ? 0.2 : 1,
        ...style
      }}
      {...restProps}
    />
  );
};

const TargetFinish = (props) => {
  const { campaignsData, normData, states, dispatch, campaignList, userId, rolesList } = props;
  const { state, toggle } = useToggle(false);
  const [dataSource, setDataSource] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectList, setSelectList] = useState([]);
  const [chartData, setChartData] = useState([]);
  const [defaultTime, setDefaultTime] = useState([]);
  const [saveSelectList, setSaveSelectList] = useState([]);
  const [saveState, setSaveState] = useState(undefined);
  const [metricConfigList, setMetricConfigList] = useState([]);
  const [loading, setLoading] = useState(false);

  const mountNodeRef = useRef(null);
  const chartRef = useRef(null);

  const { dateRange2, chartsReflash, switchState } = states;

  useEffect(() => {
    const getChartData = async () => {
      if (campaignList.length) {
        setLoading(true);
        const campaingsMetricConfigList = await campaignsService.getCampaignsMetricsConfig([
          {
            operator: 'EQ',
            propertyName: 'campaignsId',
            value: campaignsData?.id
          }
        ]);
        if (!campaingsMetricConfigList[0]?.orderNo) {
          campaingsMetricConfigList.forEach((item, index) => {
            item.orderNo = index;
          });
        }

        const chartDatas = await campaignsService.calcPresetCharts({
          id: campaignsData.id,
          flowIds: campaignList.map((item) => item?.campaignV2?.id).filter((v) => v),
          type: 'COMPLETION_RATE',
          dateRange: dateRange2?.map((item) => {
            return {
              ...item,
              truncateAsDay: true
            };
          })
        });
        setDefaultTime(dateRange2);
        const newChartsData = chartDatas.map((item) => {
          return {
            title: item.name,
            ranges: [item.target * 0.6, item.target * 0.8, item.target * 1.2],
            actual: item.value,
            target: item.target,
            ratio: item.ratio,
            orderNo: _.find(campaingsMetricConfigList, (o) => o.name === item.name).orderNo
          };
        });

        let newData = [];
        if (saveSelectList.length) {
          newChartsData.forEach((item) => {
            saveSelectList.forEach((saveItem) => {
              if (saveItem.name === item.title) {
                newData.push(item);
              }
            });
          });
        } else {
          newData = newChartsData;
        }

        setChartData(_.sortBy(newData, (o) => o.orderNo));
        setMetricConfigList(campaingsMetricConfigList);
        setLoading(false);
      }
    };
    getChartData();
  }, [chartsReflash, saveSelectList, campaignList]);

  useEffect(() => {
    if (!mountNodeRef.current) {
      return;
    }

    if (_.isEmpty(chartData) || chartRef.current) {
      if (chartRef.current) {
        chartRef.current.destroy();
      }
    }

    chartRef.current = new Chart({
      container: mountNodeRef.current,
      autoFit: true,
      height: 700
    });
    chartRef.current.legend(false); // 不展示图例

    chartRef.current.tooltip({
      showTitle: false,
      showMarkers: false,
      itemTpl: '<li class="g2-tooltip-list-item">{name}: {value}{ratio}{target}</li>'
    });

    let y = 0;
    const yGap = 0.1;
    for (let i = 0, l = chartData.length; i < l; i++) {
      const ranges = chartData[i].ranges;
      const view = chartRef.current.createView({
        region: {
          start: {
            x: 0,
            y
          },
          end: {
            x: 1,
            y: y + yGap
          }
        },
        padding: [30, 24, 10]
      });

      view.data([chartData[i]]);
      view.scale({
        actual: {
          min: 0,
          max: ranges[2]
        },
        target: {
          min: 0,
          max: ranges[2]
        }
      });
      view.coordinate().transpose();
      view.axis('target', false);
      view.axis('title', {
        label: {
          offsetY: -26,
          offsetX: 5,
          style: { textAlign: 'start' }
        }
      });
      view.axis('actual', {
        position: 'left',
        label: {
          offsetY: 3
        }
      });
      view
        .point()
        .position('title*target')
        .color('#5B8FF9')
        .shape('line')
        .size(12)
        .style({
          lineWidth: 2
        })
        .tooltip('title*target', (item, percent) => {
          return {
            name: t('dataCenter-TDiZJ1No2S1g'),
            value: percent
          };
        });
      view
        .interval()
        .position('title*actual')
        .color('#353535')
        .size(15)
        .tooltip('title*actual*ratio*target', (item, percent, ratio, target) => {
          return {
            name: item,
            value: percent,
            ratio: `<br><br>${t('dataCenter-od2Q14I1OH8b')}: ${ratio}%`,
            target: `<br><br>${t('dataCenter-sCjFm25XKjOy')}: ${target}`
          };
        });
      // 差
      view.annotation().region({
        start: ['start', 0],
        end: ['end', ranges[0]],
        style: {
          fill: '#999999',
          fillOpacity: 0.85
        }
      });
      // 良
      view.annotation().region({
        start: ['start', ranges[0]],
        end: ['end', ranges[1]],
        style: {
          fill: '#BFBFBF',
          fillOpacity: 0.85
        }
      });
      // 优
      view.annotation().region({
        start: ['start', ranges[1]],
        end: ['end', ranges[2]],
        style: {
          fill: '#E7E7E7',
          fillOpacity: 0.85
        }
      });
      y += yGap + 0.025;
    }

    chartRef.current.legend({
      offsetY: -310,
      custom: true,
      items: [
        {
          value: t('dataCenter-qP7L7zIIrzzq'),
          name: t('dataCenter-qP7L7zIIrzzq'),
          marker: { symbol: 'square', style: { fill: '#999999', r: 5 } }
        },
        {
          value: t('dataCenter-6blX5IHbOnNe'),
          name: t('dataCenter-6blX5IHbOnNe'),
          marker: { symbol: 'square', style: { fill: '#BFBFBF', r: 5 } }
        },
        {
          value: t('dataCenter-mHxoQzmo9vS0'),
          name: t('dataCenter-mHxoQzmo9vS0'),
          marker: { symbol: 'square', style: { fill: '#E7E7E7', r: 5 } }
        },
        {
          value: t('dataCenter-jOxeEvJdRLfx'),
          name: t('dataCenter-jOxeEvJdRLfx'),
          marker: { symbol: 'square', style: { fill: '#353535', r: 5 } }
        },
        {
          value: t('dataCenter-TDiZJ1No2S1g'),
          name: t('dataCenter-TDiZJ1No2S1g'),
          marker: { symbol: 'line', style: { stroke: '#353535', r: 5 } }
        }
      ]
    });

    chartRef.current.removeInteraction('legend-filter'); // 自定义图例，移除默认的分类图例筛选交互
    chartRef.current.render();
  }, [chartData]);

  const saveRecordInfo = async (id, userId) => {
    await campaignsService.saveUserOperationRecord({
      targetId: id,
      id: campaignsData?.recentUserOperationRecord?.id,
      targetType: 'CAMPAIGNS',
      type: 'RECENT',
      createUserId: userId,
      updateUserId: userId,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });
  };

  // 更新数据
  // useEffect(() => {
  //   if (!_.isEmpty(chartRef.current) && !_.isEmpty(chartData)) {
  //     chartRef.current.changeData(chartData);
  //   }
  // }, [chartData]);

  // useEffect(() => {
  //   if (!_.isEmpty(chartRef.current) && !_.isEmpty(chartData)) {
  //     chartRef.current.changeData(chartData);
  //   }
  // }, [chartData]);

  const columns = [
    {
      title: t('dataCenter-L3WjKGhRtzat'),
      dataIndex: 'name',
      className: 'drag-visible'
    },
    {
      title: t('dataCenter-fS70aLJlRbOH'),
      dataIndex: 'dataType',
      width: 100
    },
    {
      title: t('dataCenter-aOHnftY2uGjD'),
      dataIndex: 'memo',
      width: 150
    },
    {
      title: t('dataCenter-PlEhe77nChDa'),
      dataIndex: 'target',
      width: 150,
      render: (text, record) => (
        <div>
          <span style={{ marginRight: '66px' }}>{record?.targetCompleteRate ? t('dataCenter-AdE2opLZxoxq') : t('dataCenter-Lb2aWEuC5JNK')}</span>
          <HolderOutlined />
        </div>
      )
    }
  ];

  useEffect(() => {
    const getNormTableData = async () => {
      const newNormData = [];

      const _chartData = _.cloneDeep(chartData);
      let _normData = _.cloneDeep(normData);
      _normData.forEach((item) => {
        _chartData.forEach((chartItem) => {
          if (chartItem.title === item.name) {
            item.orderNo = chartItem.orderNo;
          }
        });
      });

      _normData = _.sortBy(_normData, (o) => o.orderNo);
      _normData.forEach((item) => {
        chartData.forEach((chartItem) => {
          if (item.name === chartItem.title) {
            newNormData.push(item);
          }
        });
      });

      setDataSource(newNormData);
      setSelectedRowKeys(newNormData.slice(0, 4).map((item) => item.key));
      setSelectList(newNormData.slice(0, 4));
    };

    getNormTableData();
  }, [normData, chartData]);

  const components = {
    body: {
      row: DraggableBodyRow
    }
  };

  const getTime = (obj) => {
    let time = dayjs();
    if (obj.type === 'ABSOLUTE') {
      time = dayjs(obj.timestamp);
    } else if (obj.type === 'RELATIVE') {
      const info = timeTerm.find((n) => n.value === obj.timeTerm);
      if (obj.isPast) {
        time = dayjs().subtract(obj.times, info.unit);
      } else {
        time = dayjs().add(obj.times, info.unit);
      }
    }
    return time.valueOf();
  };

  // 改变弹出框可见状态
  const changeVisible = (visible) => {
    dispatch({
      switchState: visible
    });
    toggle(visible);
  };

  const handleVisibleChange = (visible) => {
    dispatch({
      switchState: visible
    });
    toggle(visible);
  };
  const handleColumnsSubmit = async () => {
    const _dataSource = _.cloneDeep(dataSource);

    const newDataSource = _dataSource.map((item, index) => {
      return {
        orderNo: index,
        campaignsId: campaignsData.id,
        id: _.find(metricConfigList, (o) => o.keyUp === item.key).id,
        keyUp: item.key,
        name: item.name,
        target: _.find(chartData, (o) => o.title === item.name).target,
        type: _.find(metricConfigList, (o) => o.keyUp === item.key).type
      };
    });

    await campaignsService.saveAllMetric(newDataSource);
    saveRecordInfo(campaignsData.id, userId);
    setSaveState(!saveState);
    setSaveSelectList(selectList);
    changeVisible(false);
  };

  const moveRow = useCallback(
    (dragIndex, hoverIndex) => {
      const dragRow = dataSource[dragIndex];

      setDataSource(
        update(dataSource, {
          $splice: [
            [dragIndex, 1],
            [hoverIndex, 0, dragRow]
          ]
        })
      );

      const dragDataSource = update(dataSource, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, dragRow]
        ]
      });

      if (_.find(selectedRowKeys, (o) => o === dragRow.key)) {
        const resultKey = [];
        const resultItem = [];
        dragDataSource.forEach((item) => {
          selectList.forEach((selectItem) => {
            if (item.key === selectItem.key) {
              resultKey.push(item.key);
              resultItem.push(item);
            }
          });
        });
        onSelectChange(resultKey, resultItem, true);
      }
    },
    [dataSource, selectedRowKeys]
  );

  const onSelectChange = (selectedRowKeys, selectedRows, moveStatus) => {
    if (!moveStatus) {
      const _dataSource = _.cloneDeep(dataSource);
      const resultKey = [];
      const resultItem = [];
      _dataSource.forEach((item) => {
        selectedRows.forEach((selectItem) => {
          if (item.key === selectItem.key) {
            resultKey.push(item.key);
            resultItem.push(item);
          }
        });
      });
      setSelectedRowKeys(resultKey);
      setSelectList(resultItem);
    } else {
      setSelectedRowKeys(selectedRowKeys);
      setSelectList(selectedRows);
    }
  };

  const renderNormTable = () => {
    return (
      <DndProvider backend={HTML5Backend} key={Math.random()}>
        <div className="normHeader">
          <div className="title">
            <span className="text">{t('dataCenter-az0B29GuyxUG')}</span>
            <span className="desc">{t('dataCenter-wp7eoU1DOsBr')}</span>
          </div>
          {/* <div className="reset" onClick={() => reset()}>重置</div> */}
        </div>
        <Table
          pagination={false}
          columns={columns}
          dataSource={dataSource}
          components={components}
          // rowSelection={rowSelection}
          onRow={(_, index) => {
            const attr = {
              index,
              moveRow
            };
            return attr;
          }}
        />
        <div className="confirmBar">
          <Button onClick={() => changeVisible(false)} style={{ marginRight: '8px' }}>
            {t('dataCenter-xujEOCdXqerA')}
          </Button>
          <Button onClick={handleColumnsSubmit} type="primary">
            {t('dataCenter-k6rTBmiEDr0H')}
          </Button>
        </div>
      </DndProvider>
    );
  };

  return (
    <div className="targetFinish">
      <Spin spinning={loading}>
        <div>
          <div className="titleWrap">
            <div className="titleDate">
              <div className="title">{t('dataCenter-tkxkbkZ1tXrH')}</div>
              <div className="date">
                {defaultTime?.length
                  ? `${dayjs(getTime(defaultTime[0])).format('YYYY-MM-DD HH:mm:ss')} ~ ${dayjs(
                      getTime(defaultTime[1])
                    ).format('YYYY-MM-DD HH:mm:ss')}`
                  : null}
              </div>
            </div>
            {rolesList.find((item) => item === 'aim_campaigns_edit') && normData.length ? (
              <div className="switch">
                <Popover
                  overlayClassName="norm-setting-popup"
                  placement="bottomRight"
                  content={renderNormTable}
                  trigger="click"
                  destroyTooltipOnHide
                  open={state}
                  onOpenChange={handleVisibleChange}
                >
                  <Button type="text" onClick={() => changeVisible(true)} disabled={switchState}>
                    <SwapOutlined />
                    <span className="normText">{t('dataCenter-983JHfhOc8PD')}</span>
                  </Button>
                </Popover>
              </div>
            ) : null}
          </div>
          {chartData.length ? (
            <div className="container" ref={mountNodeRef} />
          ) : (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} style={{ position: 'relative', top: '80px' }} />
          )}
        </div>
      </Spin>
    </div>
  );
};

export default TargetFinish;
