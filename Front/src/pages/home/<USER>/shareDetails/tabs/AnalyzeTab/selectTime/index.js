import { Input, Popover } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { t } from 'utils/translation';
import { getString, getTime } from './config';
import Content from './content/index';
import { DataContext } from './context';

import './index.scss';

/**
 * @description: onChange接收两个参数timeValue 和 flag
 */
export default forwardRef(({ data, onChange, type, showTime, style }, ref) => {
  const [startVisible, setStartVisible] = useState(false);
  const [endVisible, setEndVisible] = useState(false);
  const [value, setValue] = useState([]);
  const [desc, setDesc] = useState([]);
  const [errorFlag, setErrorFlag] = useState(false);

  useImperativeHandle(ref, () => ({
    check
  }));
  const check = () => {
    if (errorFlag) {
      return false;
    } else {
      return true;
    }
  };

  useEffect(() => {
    setValue(data ? [...data] : []);
    if (data && data[0] && data[1]) {
      const data0 = getTime(data[0]);
      const data1 = getTime(data[1]);
      setErrorFlag(data0 - data1 > 0);
    } else {
      setErrorFlag(false);
    }
  }, [data]);

  useEffect(() => {
    const str1 = getString(value[0], showTime);
    const str2 = getString(value[1], showTime);
    setDesc([str1, str2]);
  }, [value, showTime]);

  const onSave = (data1, from) => {
    const info = { ...data1 };
    if (from === 'start') {
      if (info.type === 'ABSOLUTE') {
        if (showTime) {
          info.timestamp = info.timestamp.valueOf();
        } else {
          info.timestamp = info.timestamp.startOf('day').valueOf();
        }
      }
      value[0] = info;
      setStartVisible(false);
      setEndVisible(true);
    } else if (from === 'end') {
      if (info.type === 'ABSOLUTE') {
        if (showTime) {
          info.timestamp = info.timestamp.valueOf();
        } else {
          info.timestamp = info.timestamp.endOf('day').valueOf();
        }
      }
      value[1] = info;
      setEndVisible(false);
    }
    setValue([...value]);
    if (value[0] && value[1]) {
      const time1 = getTime(value[0]);
      const time2 = getTime(value[1]);
      const flag = time1 - time2 > 0;
      onChange(value, flag);
    }
  };

  return (
    <div style={{ maxWidth: 360, ...style }} className="site-input-group-wrapper1">
      <DataContext.Provider value={{ type, showTime }}>
        <Input.Group compact>
          <Popover
            trigger="click"
            arrowPointAtCenter
            open={startVisible}
            onOpenChange={setStartVisible}
            placement="bottom"
            content={<Content type={type} data={value[0]} save={(date) => onSave(date, 'start')} />}
          >
            <Input
              style={{
                width: '45%',
                textAlign: 'center',
                borderColor: errorFlag && 'red'
              }}
              value={desc[0]}
              placeholder={t('dataCenter-QLuyZl2f8E27')}
            />
          </Popover>
          <Input
            className="site-input-split"
            style={{
              width: '10%',
              borderLeft: 0,
              borderRight: 0,
              pointerEvents: 'none',
              backgroundColor: '#fff',
              textAlign: 'center',
              borderColor: errorFlag && 'red',
              padding: 4
            }}
            placeholder="~"
            disabled
          />
          <Popover
            trigger="click"
            open={endVisible}
            onOpenChange={setEndVisible}
            placement="bottom"
            content={<Content data={value[1]} save={(date) => onSave(date, 'end')} />}
          >
            <Input
              className="site-input-right"
              value={desc[1]}
              style={{
                width: '45%',
                textAlign: 'center',
                borderColor: errorFlag && 'red'
              }}
              placeholder={t('dataCenter-pdxK0VK8RQAT')}
            />
          </Popover>
        </Input.Group>
      </DataContext.Provider>
    </div>
  );
});
