import { DownOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Spin, Tree } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import CampaignV2Service from 'service/CampaignV2Service';
import { t } from 'utils/translation';

import dayjs from 'dayjs';
import _ from 'lodash';
import { Responsive, WidthProvider } from 'react-grid-layout';
import VisibilitySensor from 'react-visibility-sensor';
import ComChart2 from '../../../../../analysisCenter/database/comChart/index';
import './index.scss';

const campaignV2Service = new CampaignV2Service();
const { DirectoryTree } = Tree;
const ResponsiveReactGridLayout = WidthProvider(Responsive);
const defaultProps = {
  cols: { lg: 12, md: 12, sm: 12, xs: 12, xxs: 12 },
  rowHeight: 100,
  margin: [24, 24],
  breakpoints: { lg: 0, md: 0, sm: 0, xs: 0, xxs: 0 },
  widgets: { h: 4, w: 4, x: 0, y: 0 }
};
const getWidth = (type, parent) => {
  let width = 117;
  if (type === 'FIRST_LEVEL') {
    if (parent) {
      width = 117;
    } else {
      width = 141;
    }
  } else if (type === 'SECOND_LEVEL') {
    if (parent) {
      width = 100;
    } else {
      width = 117;
    }
  }
  return width;
};

const RenderAddCharts = (props) => {
  const [loadedComChartIndexArr, setLoadedComChartIndexArr] = useState([]);
  const [dashboardChartList, setDashboardChartList] = useState([]);
  const { state, dispatch, onClose, save } = props;

  const isGetLayouts = useRef(false);

  const { selectedKeys, refreshSelected, saveloading, menuList, refresh, expandedKeys, addselectValue, getLoading } =
    state;
  const { layouts = {}, widgets = [] } = addselectValue;

  const onVisibilityChange = (isVisible, n, index) => {
    if (isVisible && !_.isEqual(_.uniq([...loadedComChartIndexArr, index]), loadedComChartIndexArr)) {
      setLoadedComChartIndexArr((prevState) => {
        return _.uniq([...prevState, index]);
      });
    }
  };

  useEffect(() => {
    const init = async () => {
      // debugger;
      try {
        dispatch({ loading: true });
        const allCategoryList = await campaignV2Service.getAllchartBoardCategory([
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          }
        ]);
        const res = await campaignV2Service.getChartConfigAllList([
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          }
        ]);
        const chartBoardList = await campaignV2Service.getAllchartBoard([
          {
            operator: 'EQ',
            propertyName: 'deptId',
            value: window.getDeptId()
          }
        ]);
        const menuList = getLevelData(chartBoardList, allCategoryList, 0);
        // console.log(menuList, 'menuList');
        const defaultSelectedKeys = chartBoardList.find((n) => n.default === true) || chartBoardList[0];
        const _selectedKeys =
          selectedKeys.length === 0 && defaultSelectedKeys ? [`${defaultSelectedKeys.id}`] : selectedKeys;
        setDashboardChartList(res);
        dispatch({
          allCategoryList,
          menuList,
          chartBoardList,
          selectedKeys: _selectedKeys,
          expandedKeys: allCategoryList.map((n) => `folder.${n.id}`),
          loading: false
        });
      } catch (error) {
        dispatch({ loading: false });
      }
    };
    init();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    (async () => {
      dispatch({ getLoading: true });
      if (selectedKeys.length > 0) {
        const addselectValue = await campaignV2Service.getChartBoardById(selectedKeys[0]);
        isGetLayouts.current = true;
        if (addselectValue) {
          // props.history.replace({ state: { selectedKeys } });
          // 修改成不可拖拽 不可放大
          const newxxs = _.map(addselectValue.widgets, (n) => {
            return {
              ...n,
              ...{ static: true, isResizable: false, isDraggable: false }
            };
          });
          addselectValue.layouts.xxs = newxxs;
          dispatch({ addselectValue, getLoading: false });
        } else {
          dispatch({
            addselectValue: {
              layouts: {},
              widgets: []
            },
            selectedKeys: [],
            getLoading: false,
            refresh: !refresh
          });
        }
      } else {
        dispatch({ getLoading: false, addselectValue: {} });
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedKeys, refreshSelected]);

  const getLevelData = (chartBoardList, data, parentId) => {
    const itemArr = [];
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      if (node.parentId === parentId) {
        const newNode = {};
        newNode.key = `folder.${node.id}`;
        newNode.selectable = false;
        newNode.width = getWidth(node.type);
        newNode.title = node.name;
        newNode.isLeaf = false;
        const res = getLevelData(chartBoardList, data, node.id);
        if (res.length > 0) {
          newNode.children = res;
        }
        const list = chartBoardList.filter((w) => parseInt(node.id) === w.category?.id);
        if (list.length > 0) {
          if (newNode.children) {
            list.forEach((n) => {
              newNode.children.push({
                key: `${n.id}`,
                width: getWidth(node.type, true),
                title: n.boardName,
                isLeaf: true
              });
            });
          } else {
            newNode.children = list.map((n) => ({
              key: `${n.id}`,
              width: getWidth(node.type, true),
              title: n.boardName,
              isLeaf: true
            }));
          }
        }
        itemArr.push(newNode);
      }
    }
    return itemArr;
  };

  return (
    <>
      <div className="addChartLeft">
        <DirectoryTree
          showIcon
          width={250}
          onSelect={(selectedKeys) => dispatch({ selectedKeys })}
          expandedKeys={expandedKeys}
          onExpand={(expandedKeys) => dispatch({ expandedKeys })}
          selectedKeys={selectedKeys}
          // titleRender={(item) => treeTitle(item)}
          switcherIcon={<DownOutlined />}
          treeData={menuList}
        >
          {/* {renderTreeNodes(menuList)} */}
        </DirectoryTree>
        <footer>
          <div>
            <Button onClick={onClose}>{t('dataCenter-xujEOCdXqerA')}</Button>
            <Button type="primary" onClick={save} loading={saveloading || getLoading}>
              {t('dataCenter-XXjotuP9Tuhb')}
            </Button>
          </div>
        </footer>
      </div>
      <div className="right">
        <div>
          <Spin spinning={getLoading}>
            <ResponsiveReactGridLayout
              {...defaultProps}
              layouts={layouts}
              // isResizable={false}
              // resizeHandles="right"
              // isDroppable={false}
              // isDraggable={false} // 是否可拖拽
              // preventCollision
              style={{ width: 'calc(100% - 10px)' }}
            >
              {widgets.map((l, index) => {
                if (_.isEmpty(dashboardChartList)) return null;
                const info = dashboardChartList.find((n) => n.id === parseInt(l.i));
                // console.log(info, 'info11');
                if (!info) {
                  return (
                    <div className="noInfo" key={l.i} data-grid={l}>
                      <div className="noInfoContent">{t('dataCenter-mQzJiT0ICjSK')}</div>
                    </div>
                  );
                }
                let time = '';
                if (info.dateTimeStamp) {
                  if (info.dateTimeStamp.beginDate) {
                    time += `${dayjs(info.dateTimeStamp.beginDate).format('YYYY-MM-DD HH:mm:ss')} / `;
                  }
                  if (info.dateTimeStamp.endDate) {
                    time += dayjs(info.dateTimeStamp.endDate).format('YYYY-MM-DD HH:mm:ss');
                  }
                }

                return (
                  <div className="hasInfo" key={l.i} data-grid={l}>
                    <div className="toolbar">
                      <div className="left">
                        <div className="title">{info.name}</div>
                        <div className="time">{time}</div>
                      </div>
                    </div>
                    <div style={{ height: 'calc(100% - 60px)' }}>
                      <VisibilitySensor
                        partialVisibility
                        key={l.id}
                        onChange={(isVisible) => onVisibilityChange(isVisible, l, index)}
                      >
                        {_.includes(loadedComChartIndexArr, index) ? (
                          <ComChart2 info={info} />
                        ) : (
                          <div style={{ width: '100%', height: '100%' }} />
                        )}
                      </VisibilitySensor>
                    </div>
                  </div>
                );
              })}
            </ResponsiveReactGridLayout>
          </Spin>
        </div>
      </div>
    </>
  );
};

export default RenderAddCharts;
