import { Button, Input, Select, Table, message } from 'antd';
import _ from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import { t } from 'utils/translation';

import CampaignsService from 'service/CampaignsService';
import { setNativeValue } from 'utils/universal';

import { SearchOutlined } from '@ant-design/icons';

import '../addTabs.scss';

const { Option } = Select;

const campaignsService = new CampaignsService();

const pagination = {
  showTotal: (totals) => `${t('dataCenter-oVHOMzdWo1X7')} ${totals} ${t('dataCenter-GviPAx92Kah6')}`,
  showSizeChanger: false
};

const ProcessCanvas = (props) => {
  const { state, dispatch, onClose, onSave, campaignId, data = {} } = props;
  const [selectedList, setSelectedList] = useState([]);
  const [dataSource, setDataSource] = useState([]);
  const [searchSelectVal, setSearchSelectVal] = useState('name');
  const [loading, setLoading] = useState(false);

  const [param, setParam] = useState({
    page: 1,
    search: [
      {
        operator: 'EQ',
        propertyName: 'projectId',
        value: localStorage.getItem('projectId')
      },
      {
        operator: 'NE',
        propertyName: 'phase',
        value: 'DRAFT'
      },
      {
        operator: 'EQ',
        propertyName: 'deptId',
        value: data?.deptId || window.getDeptId()
      }
    ],
    size: 20,
    sorts: [{ propertyName: 'updateTime', direction: 'desc' }]
  });

  const searchRef = useRef();

  const { saveLoading } = state;

  const columns = [
    {
      title: t('dataCenter-FoLyafNiJDGj'),
      dataIndex: 'name',
      render: (text, record) => <div>{`[${record.id}] ${text}`}</div>,
      ellipsis: true
    }
  ];

  useEffect(() => {
    const init = async () => {
      setLoading(true);
      const campainsList = await campaignsService.query3({
        id: Number(campaignId),
        page: param
      });
      const getCampainsList = await campaignsService.findCampaigns([
        {
          operator: 'EQ',
          propertyName: 'campaignsId',
          value: campaignId
        }
      ]);
      const _campainsList = _.cloneDeep(campainsList);
      _campainsList.content.forEach((item) => {
        item.key = item.id;
        if (_.find(getCampainsList, (o) => o.campaignV2?.id === item.id) || item.phase === 'DRAFT') {
          item.disabled = true;
        }
      });

      pagination.total = campainsList.totalElements;
      pagination.current = param.page;
      pagination.pageSize = param.size;
      setDataSource(_campainsList.content);
      setLoading(false);
    };

    init();
  }, [param]);

  const onTextChange = _.debounce((val) => {
    const _param = _.cloneDeep(param);
    if (searchSelectVal === 'name') {
      _param.search = [
        {
          operator: 'NE',
          propertyName: 'phase',
          value: 'DRAFT'
        },
        {
          operator: 'EQ',
          propertyName: 'projectId',
          value: localStorage.getItem('projectId')
        },
        { operator: 'LIKE', propertyName: 'name', value: val },
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: data?.deptId || window.getDeptId()
        }
      ];
    } else {
      if (val !== '' && !Number(val)) {
        message.error(t('dataCenter-iIqj5FZenGcM'));
        return;
      }
      _param.search = [
        {
          operator: 'NE',
          propertyName: 'phase',
          value: 'DRAFT'
        },
        {
          operator: 'EQ',
          propertyName: 'projectId',
          value: localStorage.getItem('projectId')
        },
        { operator: 'EQ', propertyName: 'id', value: val },
        {
          operator: 'EQ',
          propertyName: 'deptId',
          value: data?.deptId || window.getDeptId()
        }
      ];
    }

    setParam(_param);
  }, 500);

  const onTableChange = (lastpagination, filtersArg, sorter) => {
    param.page = lastpagination.current;
    param.size = lastpagination.pageSize;
    if (sorter.field) {
      param.sorts = [
        {
          propertyName: sorter.field,
          direction: sorter.order === 'ascend' ? 'asc' : 'desc'
        }
      ];
    }
    setParam({ ...param });
  };

  const rowSelection = {
    preserveSelectedRowKeys: true,
    onChange: (selectedRowKeys, selectedRows) => {
      dispatch({
        addComposeData: selectedRows
      });
      setSelectedList(selectedRows);
    },
    getCheckboxProps: (record) => ({
      disabled: record.disabled
    })
  };

  const onSelect = (value) => {
    setNativeValue([searchRef.current.input], '');
    const _param = _.cloneDeep(param);
    _param.search = [
      {
        operator: 'NE',
        propertyName: 'phase',
        value: 'DRAFT'
      },
      {
        operator: 'EQ',
        propertyName: 'projectId',
        value: localStorage.getItem('projectId')
      },
      {
        operator: 'EQ',
        propertyName: value,
        value: searchRef.current.input.value
      },
      {
        operator: 'EQ',
        propertyName: 'deptId',
        value: data?.deptId || window.getDeptId()
      }
    ];
    setParam(_param);
    setSearchSelectVal(value);
  };

  return (
    <div className="processCanvasWrap">
      <div className="searchWrap">
        {/* <Input placeholder="搜索" onChange={(e) => onTextChange(e.target.value)} style={{ width: '100%' }} suffix={<SearchOutlined />} /> */}
        <Input.Group compact>
          <Select style={{ width: '11%' }} value={searchSelectVal} onSelect={onSelect}>
            <Option value="name">{t('dataCenter-6FoXyfGEDqoH')}</Option>
            <Option value="id"> ID</Option>
          </Select>

          <Input
            onChange={(e) => onTextChange(e.target.value)}
            style={{ width: '89%' }}
            suffix={<SearchOutlined />}
            ref={searchRef}
          />
        </Input.Group>
      </div>

      <div className="tableWrap">
        <Table
          columns={columns}
          onChange={onTableChange}
          dataSource={dataSource}
          pagination={pagination}
          loading={loading}
          rowSelection={{
            ...rowSelection
          }}
          scroll={{ y: 'calc(100vh - 292px)' }}
        />
      </div>

      <footer>
        <Button className="bor_ra-6" style={{ marginRight: '8px' }} onClick={onClose}>
          {t('dataCenter-xujEOCdXqerA')}
        </Button>
        <Button
          className="bor_ra-6"
          type="primary"
          onClick={onSave}
          loading={saveLoading || loading}
          disabled={!selectedList.length}
        >
          {t('dataCenter-XXjotuP9Tuhb')}
        </Button>
      </footer>
    </div>
  );
};

export default ProcessCanvas;
