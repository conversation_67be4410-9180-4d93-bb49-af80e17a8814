export default {
  cn: {
  "operationCenter-bL27BOkL62Pz": "本季度以来",
  "operationCenter-MZtjlKevIUmI": "策略概览",
  "operationCenter-Bjf9QrZ4TI8k": "统计时间",
  "operationCenter-iViVHUpq8KVF": "流程画布数量",
  "operationCenter-EW2KVQnwz4a3": "单次执行",
  "operationCenter-l7sdM6R7oMjH": "周期执行",
  "operationCenter-8WBLhLx2s7qr": "流程画布运行",
  "operationCenter-kWJCcoiNKuNR": "草稿",
  "operationCenter-BSEgSlE4JclI": "测试中",
  "operationCenter-uJYyC1RjV8AP": "测试完成",
  "operationCenter-xflTznlGwPqJ": "启用中",
  "operationCenter-LeHpQJOR1teT": "已结束",
  "operationCenter-C6uMyeTlUn5B": "流程画布审批",
  "operationCenter-x4OUEYxXSr4h": "待提交审批",
  "operationCenter-HpvmBKTGIqnS": "审批中",
  "operationCenter-bAqePfRQ2uJn": "审批通过",
  "operationCenter-XPj4WDRtsMsq": "审批驳回",
  "operationCenter-RjSEp8bp4q7r": "已撤销",
  "operationCenter-pYZEWT1xe6il": "已取消",
  "operationCenter-BZWNuCpHg0EK": "流程画布批次",
  "operationCenter-NYJAeAoLoFlP": "批次总数",
  "operationCenter-cRxd2We9GgqB": "正式批次总数",
  "operationCenter-F1ZUhvxiRMuy": "测试批次总数",
  "operationCenter-ywVNFQ0rzr1I": "当前任务排队数",
  "operationCenter-msJtsqpepEOb": "平均任务启动延迟",
  "operationCenter-lIp16hhdVJbu": "平均任务启动耗时",
  "operationCenter-4PRcATQMDIre": "流程画布批次状态",
  "operationCenter-250UdDDqtcwa": "运行结束",
  "operationCenter-cQxMb489jeUm": "终止",
  "operationCenter-FLrEnNva69zy": "运行失败",
  "operationCenter-dupagM3vHeB5": "撤回中",
  "operationCenter-nsbbw4EyeaAK": "撤回成功",
  "operationCenter-DZpfaNw8DjFr": "运行中",
  "operationCenter-qPFW0hkh2cLb": "运行中正式批次",
  "operationCenter-Qp5MvFD97bpj": "运行中测试批次",
  "operationCenter-Vg8QCnw8pdEe": "待运行",
  "operationCenter-w0XN3YBxNATU": "待运行正式批次",
  "operationCenter-4fEh6Hs0fbht": "待运行测试批次",
  "operationCenter-RU4KA41SyZ5f": "待终止",
    "operationCenter-YTomt3CWAqLM": "待终止正式批次",
  "operationCenter-pbZJyv75QRwp": "待终止测试批次",
  "operationCenter-IbZToIZ1XCvA": "用户分群数量",
  "operationCenter-nqozRzTBM1OR": "分群总数",
  "operationCenter-oU4CKmaWqxK0": "单次计算分群",
  "operationCenter-LdLyE93luxE7": "周期计算分群",
  "operationCenter-7CeakWS6dIDM": "用户分群状态",
  "operationCenter-hRiVWjSVdGb8": "草稿",
  "operationCenter-DGjKpBfMB8xh": "启用中",
  "operationCenter-8RIUgybNkdyk": "已停用",
  "operationCenter-uJMHWNGDxxhS": "启用并被流程画布引用",
  "operationCenter-gUpAWpynjBDu": "流程画布引用率",
  "operationCenter-9GGUXdhRoQJE": "用户分群审批",
  "operationCenter-T91teANpCljK": "用户分群计算",
  "operationCenter-ZZJ4WpChvuSV": "计算任务总数",
  "operationCenter-zHuGgWLcpkoJ": "今日待计算",
  "operationCenter-xqtWfU6fY4Ni": "平均计算启动延迟",
  "operationCenter-BCnKUATv9Efv": "平均计算耗时",
  "operationCenter-lKXuxSUkUuMg": "用户分群状态",
  "operationCenter-8DZsDkmLpsNX": "计算结束",
  "operationCenter-Eja9lpKVztqz": "计算成功",
  "operationCenter-iUrKGvIQiWct": "计算失败",
  "operationCenter-uvBSEdW7rYj6": "计算中",
  "operationCenter-2KiMyj1dan88": "流程画布覆盖人次",
  "operationCenter-90HrRltPphab": "累计覆盖总人次",
  "operationCenter-KLP6keb1rIpm": "日平均覆盖人次",
  "operationCenter-IH2TJi2DII6p": "今年以来",
  "operationCenter-Yuam4KALm6le": "本月以来",
  "operationCenter-vMi8fDCPDuaf": "本季度以来",
  "operationCenter-Ks9LbkfYKIOH": "本年第一季度",
  "operationCenter-X1a0mdFWge3J": "本年第二季度",
  "operationCenter-ts7GYU7e1x8T": "本年第三季度",
  "operationCenter-fuzemAoBAeL8": "本年第四季度",
  "operationCenter-uDPNRNMHrbcr": "'历史流程画布批次的总数量，包括测试批次和正式批次'",
  "operationCenter-Gy5H8oA6soHw": "'当前时间点流程画布批次任务的排队数量'",
  "operationCenter-pwfeB1zQogDh": "'近一周流程画布批次任务的平均启动延迟时间'",
  "operationCenter-dVLVxIoMTM36": "'近一周流程画布批次任务的平均启动耗时时间'",
  "operationCenter-NsRD5KWQ9tdf": "'当前状态为启用中的用户分群被流程画布引用的数量, 曾经引用或正在引用都包含在内'",
  "operationCenter-ZopIEx69G0DZ": "'启用中并被流程画布引用的分群数量/启用中的分群数量'",
  "operationCenter-myFkiZLswRHW": "'历史分群计算任务的总数量'",
  "operationCenter-TKcgz5kzTctN": "'今日待计算的分群任务数量'",
  "operationCenter-4BgbjYDsYivs": "'近一周用户分群平均计算启动延迟时间'",
  "operationCenter-aRrQYObRY1Xj": "'近一周用户分群平均计算耗时时间",
  "operationCenter-52FBI6aWsZpI": "日期",
  "operationCenter-rJVQi6fZ43Q0": "覆盖总人次",
  "operationCenter-r5AfgNuWMLOH": "时间范围",
  "operationCenter-PMx4CGhf477n": "",
  "operationCenter-yPLQUHBXqv7f": "",
  },
  en: {
  "operationCenter-bL27BOkL62Pz": "Since this quarter",
  "operationCenter-MZtjlKevIUmI": "Strategy Overview",
  "operationCenter-Bjf9QrZ4TI8k": "Statistical Time",
  "operationCenter-iViVHUpq8KVF": "Canvas Count",
  "operationCenter-EW2KVQnwz4a3": "Single Execution",
  "operationCenter-l7sdM6R7oMjH": "Cycle Execution",
  "operationCenter-8WBLhLx2s7qr": "Canvas Running",
  "operationCenter-kWJCcoiNKuNR": "Draft",
  "operationCenter-BSEgSlE4JclI": "Testing",
  "operationCenter-uJYyC1RjV8AP": "Test Completed",
  "operationCenter-xflTznlGwPqJ": "Enabled",
  "operationCenter-LeHpQJOR1teT": "Ended",
  "operationCenter-C6uMyeTlUn5B": "Canvas Approval",
  "operationCenter-x4OUEYxXSr4h": "Pending Approval",
  "operationCenter-HpvmBKTGIqnS": "Approval in Progress",
  "operationCenter-bAqePfRQ2uJn": "Approval Passed",
  "operationCenter-XPj4WDRtsMsq": "Approval Rejected",
  "operationCenter-RjSEp8bp4q7r": "Revoked",
  "operationCenter-pYZEWT1xe6il": "Cancelled",
  "operationCenter-BZWNuCpHg0EK": "Canvas Batch",
  "operationCenter-NYJAeAoLoFlP": "Total Batches",
  "operationCenter-cRxd2We9GgqB": "Official Batch Total",
  "operationCenter-F1ZUhvxiRMuy": "Test Batch Total",
  "operationCenter-ywVNFQ0rzr1I": "Current Task Queue",
  "operationCenter-msJtsqpepEOb": "Average Task Start Delay",
  "operationCenter-lIp16hhdVJbu": "Average Task Duration",
  "operationCenter-4PRcATQMDIre": "Canvas Batch Status",
  "operationCenter-250UdDDqtcwa": "Ended",
  "operationCenter-cQxMb489jeUm": "Terminated",
  "operationCenter-FLrEnNva69zy": "Failed",
  "operationCenter-dupagM3vHeB5": "Withdrawing",
  "operationCenter-nsbbw4EyeaAK": "Withdrawal Success",
  "operationCenter-DZpfaNw8DjFr": "Running",
  "operationCenter-qPFW0hkh2cLb": "Running Official Batch",
  "operationCenter-Qp5MvFD97bpj": "Running Test Batch",
  "operationCenter-Vg8QCnw8pdEe": "Pending",
  "operationCenter-w0XN3YBxNATU": "Pending Official Batch",
  "operationCenter-4fEh6Hs0fbht": "Pending Test Batch",
  "operationCenter-RU4KA41SyZ5f": "Pending Termination",
    "operationCenter-YTomt3CWAqLM": "Pending Termination Official Batch",
  "operationCenter-pbZJyv75QRwp": "Pending Termination Test Batch",
  "operationCenter-IbZToIZ1XCvA": "User Group Count",
  "operationCenter-nqozRzTBM1OR": "Total User Groups",
  "operationCenter-oU4CKmaWqxK0": "Single Execution User Group",
  "operationCenter-LdLyE93luxE7": "Cycle Execution User Group",
  "operationCenter-7CeakWS6dIDM": "User Group Status",
  "operationCenter-hRiVWjSVdGb8": "Draft",
  "operationCenter-DGjKpBfMB8xh": "Enabled",
  "operationCenter-8RIUgybNkdyk": "Disabled",
  "operationCenter-uJMHWNGDxxhS": "Enabled and Referenced by Canvas",
  "operationCenter-gUpAWpynjBDu": "Canvas Reference Rate",
  "operationCenter-9GGUXdhRoQJE": "User Group Approval",
  "operationCenter-T91teANpCljK": "User Group Calculation",
  "operationCenter-ZZJ4WpChvuSV": "Total Calculation Tasks",
  "operationCenter-zHuGgWLcpkoJ": "Today Pending Calculation",
  "operationCenter-xqtWfU6fY4Ni": "Average Calculation Start Delay",
  "operationCenter-BCnKUATv9Efv": "Average Calculation Duration",
  "operationCenter-lKXuxSUkUuMg": "User Group Status",
  "operationCenter-8DZsDkmLpsNX": "Calculation Ended",
  "operationCenter-Eja9lpKVztqz": "Calculation Succeeded",
  "operationCenter-iUrKGvIQiWct": "Calculation Failed",
  "operationCenter-uvBSEdW7rYj6": "Calculating",
  "operationCenter-2KiMyj1dan88": "Canvas Coverage",
  "operationCenter-90HrRltPphab": "Total Coverage",
  "operationCenter-KLP6keb1rIpm": "Average Daily Coverage",
  "operationCenter-IH2TJi2DII6p": "Since This Year",
  "operationCenter-Yuam4KALm6le": "Since This Month",
  "operationCenter-vMi8fDCPDuaf": "Since This Quarter",
  "operationCenter-Ks9LbkfYKIOH": "Q1 of This Year",
  "operationCenter-X1a0mdFWge3J": "Q2 of This Year",
  "operationCenter-ts7GYU7e1x8T": "Q3 of This Year",
  "operationCenter-fuzemAoBAeL8": "Q4 of This Year",
  "operationCenter-uDPNRNMHrbcr": "'Total number of historical canvas batches, including test and official batches'",
  "operationCenter-Gy5H8oA6soHw": "'Number of canvas batch tasks in the queue at the current time'",
  "operationCenter-pwfeB1zQogDh": "'Average start delay time of canvas batch tasks in the past week'",
  "operationCenter-dVLVxIoMTM36": "'Average duration time of canvas batch tasks in the past week'",
  "operationCenter-NsRD5KWQ9tdf": "'Number of enabled user groups that have been referenced by canvas, including both previously referenced and currently referenced'",
  "operationCenter-ZopIEx69G0DZ": "'Number of enabled user groups that have been referenced by canvas / Number of enabled user groups'",
  "operationCenter-myFkiZLswRHW": "'Total number of historical user group calculation tasks'",
  "operationCenter-TKcgz5kzTctN": "'Number of user group calculation tasks pending today'",
  "operationCenter-4BgbjYDsYivs": "'Average start delay time of user group calculation tasks in the past week'",
  "operationCenter-aRrQYObRY1Xj": "'Average duration time of user group calculation tasks in the past week'",
  "operationCenter-52FBI6aWsZpI": "Date",
  "operationCenter-rJVQi6fZ43Q0": "Coverage",
  "operationCenter-r5AfgNuWMLOH": "Time Range",
  "operationCenter-PMx4CGhf477n": "",
  "operationCenter-yPLQUHBXqv7f": "",
  }
};
