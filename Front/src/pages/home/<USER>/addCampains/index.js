import '@ant-design/compatible/assets/index.css';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Col, Drawer, Form, Input, Popover, Radio, Row, Select, Upload, message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import CampaignsService from 'service/CampaignsService';
import RoleService from 'service/roleService';

import data from '@emoji-mart/data';
import zh_ch from '@emoji-mart/data/i18n/zh.json';
import Picker from '@emoji-mart/react';
import { getDeptPath } from 'pages/home/<USER>/dataPermissions/config';
import { t } from 'utils/translation';
import { transformUrl } from 'utils/universal';
import CustomRangePicker from './rangepicker';

import '../index.scss';

const { Option } = Select;

const campaignsService = new CampaignsService();
const roleService = new RoleService();

const EditCampains = (props) => {
  const [form] = Form.useForm();
  const { visible, action, editValue, isShowEdit, onClose, refresh, userId } = props;
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [ownerList, setOwerList] = useState([]);
  const [logoVisible, setLogoVisible] = useState(false);
  const [emojiData, setEmojiData] = useState(undefined);
  const [imageData, setImageData] = useState(undefined);
  const [logoData, setLogoData] = useState(undefined);
  const [emojiVal, setEmojiVal] = useState(undefined);
  const [radioValue, setRadioValue] = useState('emoji');
  const [fileList, setFileList] = useState([]);
  const [campainsData, setCampainsData] = useState(undefined);
  const [url, setUrl] = useState('');
  const [deptPath, setDeptPath] = useState(getDeptPath());

  const saveRecordInfo = async (id) => {
    await campaignsService.saveUserOperationRecord({
      targetId: id,
      id: editValue?.recentUserOperationRecord?.id,
      targetType: 'CAMPAIGNS',
      type: 'RECENT',
      createUserId: userId,
      updateUserId: userId,
      createTime: dayjs().valueOf(),
      updateTime: dayjs().valueOf()
    });

    return true;
  };

  // 初始化
  useEffect(() => {
    (async () => {
      getOwerList();
      // 编辑
      if (isShowEdit.idEdit) {
        setConfirmLoading(true);
        try {
          saveRecordInfo(editValue.id);
          // const value = await campaignsService.getCampaigns(editValue.id);
          const value = await campaignsService.getCampaignsV2({
            id: Number(editValue.id),
            deptId: window.getDeptId()
          });
          setCampainsData(value);
          const { name, ownerId, startTime, endTime, memo, snapshot } = value;

          form.setFieldsValue({
            name,
            ownerId,
            time: [dayjs(startTime), dayjs(endTime)],
            memo
          });

          if (snapshot?.type === 'EMOJI') {
            const emojiSelected = data.emojis[snapshot.id].skins.find(
              (skinItem) => skinItem.unified === snapshot.unified
            );
            setEmojiVal(emojiSelected.native);
            setLogoData({
              type: 'emoji',
              data: { id: snapshot.id, unified: snapshot.unified }
            });
          } else if (snapshot?.type === 'CUSTOM') {
            setLogoData({ type: 'image', data: snapshot.url });
            setUrl(snapshot.url);
            setRadioValue('image');
          }
          setDeptPath(getDeptPath(editValue?.deptId));
          setConfirmLoading(false);
        } catch (error) {
          setConfirmLoading(false);
        }
      }
    })();
  }, []);

  const getOwerList = async (
    params = {
      search: [],
      size: 50,
      page: 1,
      sort: [{ propertyName: 'createTime', direction: 'desc' }]
    }
  ) => {
    const roleList = await roleService.reqGetTableData(params);
    setOwerList(roleList.content);
  };

  // 关闭
  const onClosed = () => {
    onClose && props.onClose();
  };

  const handleVisibleChange = (newVisible) => {
    setLogoVisible(newVisible);
  };

  const customRequest = async (info) => {
    const formData = new FormData();
    formData.append('file', info.file);
    formData.append('type', 'IMAGE');
    const resultData = await campaignsService.upload(formData);
    if (resultData.code !== 1) {
      message.error(t('dataCenter-ySbnNorpDUK1'));
      return;
    }
    setUrl(resultData.result);
    message.success(t('dataCenter-c4HwmLkQVdMt'));
  };

  const okHandle = async () => {
    setConfirmLoading(true);
    try {
      const valueParam = await form.validateFields();
      valueParam.startTime = dayjs(dayjs(valueParam.time[0]).format('YYYY-MM-DD 00:00:00')).valueOf();
      valueParam.endTime = dayjs(dayjs(valueParam.time[1]).format('YYYY-MM-DD 23:59:59')).valueOf();
      valueParam.status = isShowEdit.idEdit ? editValue.status : 'DRAFT';
      if (isShowEdit.idEdit) {
        valueParam.orderNo = campainsData?.orderNo;
        valueParam.id = isShowEdit.idEdit;
      }
      if (logoData) {
        if (logoData.type === 'emoji') {
          valueParam.snapshot = {
            id: logoData.data.id,
            type: 'EMOJI',
            unified: logoData.data.unified
          };
        } else if (logoData.type === 'image') {
          valueParam.snapshot = { type: 'CUSTOM', url };
        }
      }
      valueParam.deptId = editValue?.deptId || window.getDeptId();
      const res = await campaignsService.campainsSave(valueParam);
      saveRecordInfo(res.id);

      setTimeout(() => {
        setConfirmLoading(false);
        onClosed();
        message.success(isShowEdit.idEdit ? t('dataCenter-uf2GLhLRCpIb') : t('dataCenter-0dSW9rjOTgW5'), 1);
        refresh();
      }, 1000);
    } catch (err) {
      setConfirmLoading(false);
      // message.error('保存失败,请检查内容', 1);
    }
  };

  const emojiSelect = (e) => {
    const emojiSelected = data.emojis[e.id].skins.find((skinItem) => skinItem.unified === e.unified);
    setEmojiVal(emojiSelected.native);
    setEmojiData({ type: 'emoji', data: { id: e.id, unified: e.unified } });
    setLogoData({ type: 'emoji', data: { id: e.id, unified: e.unified } });
    setLogoVisible(false);
  };

  const isSize = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.addEventListener(
        'load',
        () => {
          const img = new Image();
          img.src = reader.result;
          img.onload = () => {
            if (img.width < 80 || img.height < 80) {
              message.error(t('dataCenter-AC11CmnFWIRi'));
              reject(new Error(t('dataCenter-AC11CmnFWIRi')));
            } else if (img.width / img.height !== 1) {
              message.error(t('dataCenter-flnX2Sro1U7O'));
              reject(new Error(t('dataCenter-flnX2Sro1U7O')));
            } else {
              resolve();
            }
          };
        },
        false
      );
      reader.readAsDataURL(file);
    });
  };

  const unloadProps = {
    customRequest,
    onRemove: (file) => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    beforeUpload: (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error(t('dataCenter-96pftLUQvOyS'));
      }
      const isLt2M = file.size / 1024 / 1024 < 5;
      if (!isLt2M) {
        message.error(t('dataCenter-3hHpaBlxQtGd'));
      }
      setImageData({ type: 'image', data: file });
      setLogoData({ type: 'image', data: file });
      setFileList([...fileList, file]);
      const size = isSize(file);
      return isJpgOrPng && isLt2M && size;
    },
    fileList,
    showUploadList: false,
    name: 'avatar',
    listType: 'picture-card',
    className: 'avatar-uploader'
  };

  const onChange = (e) => {
    if (e.target.value === 'emoji') {
      setLogoData(emojiData);
    } else {
      setLogoData(imageData);
    }
    setRadioValue(e.target.value);
  };

  const logoContentRender = () => {
    return (
      <div className="emojiWrap">
        <Picker
          previewPosition="none"
          onEmojiSelect={emojiSelect}
          i18n={zh_ch}
          skinTonePosition="search"
          navPosition="none"
          data={data}
        />
      </div>
    );
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div className="text">{t('dataCenter-cBoiZhJnZ84n')}</div>
    </div>
  );

  return (
    <Drawer
      className="campainsDrawerWrap"
      title={(isShowEdit.idEdit && t('dataCenter-9l6cBn4Fr7TC')) || t('dataCenter-aBn3abZVioVX')}
      width={600}
      onClose={action}
      open={visible}
    >
      <div className="FormWrap">
        <div>
          <div className="deptStyle flex">
            <div>{t('dataCenter-8ybX97fJedkh')}</div>
            <div title={deptPath}>{deptPath}</div>
          </div>
        </div>
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="name"
                label={t('dataCenter-b5zwDAyOW4BY')}
                rules={[
                  { required: true, message: t('dataCenter-adoOidS1fZRl') },
                  {
                    pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]*$/,
                    message: t('dataCenter-PzWmDOVnV560')
                  },
                  { max: 64, message: t('dataCenter-B78lJAtbOwTQ') }
                ]}
              >
                <Input placeholder={t('dataCenter-adoOidS1fZRl')} className="bor_ra-6" autocomplete="off" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="ownerId" label={t('dataCenter-IZIrVK5KHHlD')} rules={[{ required: false }]}>
                <Select
                  placeholder={t('dataCenter-mSJZ0UZpN2AS')}
                  allowClear
                  className="bor_ra-6"
                  showSearch
                  filterOption={(input, option) => option.children.toLowerCase().includes(input.toLowerCase())}
                >
                  {ownerList?.map((item) => (
                    <Option key={item.id} value={item.user.id}>
                      {item.user.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="time"
                label={t('dataCenter-dcyJNUiAGpJc')}
                rules={[
                  { required: true, message: '' },
                  () => ({
                    validator(_, value) {
                      if (value) {
                        if (value[0] === null || value[1] === null) {
                          return Promise.reject(new Error(t('dataCenter-95iWPqsp6mZV')));
                        }
                        return Promise.resolve();
                      } else {
                        return Promise.reject(new Error(t('dataCenter-95iWPqsp6mZV')));
                      }
                    }
                  })
                ]}
              >
                <CustomRangePicker
                  // showTime={{ format: 'HH:mm' }}
                  format="YYYY-MM-DD"
                  timeDiff={5}
                  closeBefore
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="logo"
                label={t('dataCenter-O3vZTtX6Wlmd')}
                rules={[
                  {
                    required: false
                  }
                ]}
              >
                <div className="acitveLogoWrap">
                  <Radio.Group onChange={onChange} value={radioValue}>
                    <Radio value="image">{t('dataCenter-TKg9d9UVkFnX')}</Radio>
                    <Radio value="emoji">Emoji</Radio>
                  </Radio.Group>
                  {radioValue === 'emoji' ? (
                    <div className="activeLogo">
                      <Popover
                        content={logoContentRender()}
                        overlayClassName="emojiPopover"
                        placement="bottom"
                        trigger="click"
                        open={logoVisible}
                        onOpenChange={handleVisibleChange}
                      >
                        {emojiVal ? (
                          <div className="emojiValWrap">
                            <span style={{ fontSize: 48 }}>{emojiVal}</span>
                          </div>
                        ) : (
                          <div>
                            <span className="icon">
                              <PlusOutlined />
                            </span>
                            <div className="text">{t('dataCenter-Gf5gE7IGScVm')}</div>
                          </div>
                        )}
                      </Popover>
                    </div>
                  ) : (
                    <Upload {...unloadProps}>
                      {url ? (
                        <img
                          src={transformUrl(url)}
                          alt="avatar"
                          style={{
                            width: '100%'
                          }}
                        />
                      ) : (
                        uploadButton
                      )}
                    </Upload>
                  )}

                  <div className="logoDesc">{t('dataCenter-7CUR9jYScRnj')}</div>
                </div>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="memo"
                label={t('dataCenter-bby8Eq6OFuR4')}
                rules={[
                  {
                    required: false
                  },
                  { max: 150, message: t('dataCenter-rokVdNMPOlXt') }
                ]}
              >
                <Input.TextArea rows={4} placeholder={t('dataCenter-wXYskuHhVTU8')} className="bor_ra-6" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </div>

      <div className="optionWrap">
        <footer>
          <div>
            <Button onClick={() => onClosed()} style={{ marginRight: '8px' }} className="bor_ra-6">
              {t('dataCenter-xujEOCdXqerA')}
            </Button>
            {/* <Button type="primary" onClick={okHandle} loading={saveloading || getLoading} disabled={!saveList.length}> */}
            <Button type="primary" onClick={() => okHandle()} loading={confirmLoading} className="bor_ra-6">
              {t('dataCenter-YqzLKoLR86pw')}
            </Button>
          </div>
        </footer>
      </div>
    </Drawer>
  );
};

export default EditCampains;
