import { DatePicker } from 'antd';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { t } from 'utils/translation';
import './index.scss';

const RangePicker = (props) => {
  const {
    showTime = { format: 'HH:mm' },
    value = [null, null],
    format = 'YYYY-MM-DD HH:mm',
    startPlaceHolder = t('dataCenter-M81OG6BvRbUi'),
    endPlaceHolder = t('dataCenter-xKjzTfv9o44p'),
    closeBefore = false,
    timeDiff = 0,
    unit = 'minutes',
    disabled,
    minDate,
    maxDate
  } = props;

  const [startValue, setStartValue] = useState(value[0]);
  const [endValue, setEndValue] = useState(value[1]);
  const [endOpen, setEndOpen] = useState(false);

  useEffect(() => {
    let isMount = true;
    if (isMount) {
      setStartValue(value[0]);
      setEndValue(value[1]);
    }
    return () => {
      isMount = false;
    };
  }, [value]);

  const disabledStartDate = (currentDate) => {
    // 判断是否开启禁用当前以前时间，
    if (closeBefore) {
      return currentDate && dayjs(currentDate).startOf('day').isBefore(dayjs().startOf('day'));
    }
    if (minDate) {
      return currentDate && dayjs(currentDate).startOf('day').isBefore(dayjs(minDate).startOf('day'));
    }
  };

  const disabledStartTime = (date) => {
    if (showTime && closeBefore) {
      return {
        disabledHours: () => {
          if (date && date.format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD')) {
            return range(0, 24).splice(0, dayjs().hour());
          }
          return [];
        },
        disabledMinutes: () => {
          // 如果天数和小时数一致，禁用当前时间之前的分钟数
          if (date && date.format('YYYY-MM-DD HH') === dayjs().format('YYYY-MM-DD HH')) {
            return range(0, 60).splice(0, dayjs().minute());
          }
          return [];
        }
      };
    }
    return null;
  };

  const disabledEndDate = (currentDate) => {
    if (maxDate) {
      return currentDate && dayjs(currentDate).startOf('day') > dayjs(maxDate).startOf('day');
    } else {
      // 首先判断是否有开始时间，如果有开始时间当天剩余时间是否
      if (!startValue || !currentDate) {
        return false;
      }
      return currentDate && currentDate < dayjs(startValue).add(timeDiff, unit).startOf('day');
    }
  };

  const disabledEndTime = (date) => {
    if (showTime && startValue) {
      return {
        disabledHours: () => {
          const minDate = dayjs(startValue).add(timeDiff, unit).format('YYYY-MM-DD');
          if (
            dayjs(dayjs(date).format('YYYY-MM-DD')).isBefore(minDate) ||
            dayjs(dayjs(date).format('YYYY-MM-DD')).isSame(minDate)
          ) {
            return range(0, 24).splice(0, dayjs(startValue).add(timeDiff, unit).hour());
          }
          return [];
        },
        disabledMinutes: () => {
          const minDate = dayjs(startValue).add(timeDiff, unit).format('YYYY-MM-DD HH');
          if (
            dayjs(dayjs(date).format('YYYY-MM-DD HH')).isBefore(minDate) ||
            dayjs(dayjs(date).format('YYYY-MM-DD HH')).isSame(minDate)
          ) {
            return range(0, 60).splice(0, dayjs(startValue).add(timeDiff, unit).minute());
          }
          return [];
        }
      };
    }
    return null;
  };

  const onStartChange = (value) => {
    const _value = _.cloneDeep(value);

    if (closeBefore && _value && _value.isBefore(dayjs())) {
      setStartValue(dayjs());
    } else {
      setStartValue(value);
    }
    // 判断如果结束时间小于起始时间加间隔，则把结束时间设置为起始时间加最小间隔
    if (endValue && value) {
      const targetValue = _value.add(timeDiff, unit);
      if (endValue.isBefore(targetValue)) {
        setEndValue(targetValue);
      }
    }
    props.onChange && props.onChange([value, endValue]);
  };

  const onEndChange = (value) => {
    const _value = _.cloneDeep(value);
    const _startValue = _.cloneDeep(startValue);
    const targetValue = _startValue && _startValue.add(timeDiff, unit);
    let lastValue = value;
    if (_startValue && _value && _value.isBefore(targetValue)) {
      lastValue = targetValue;
    }
    setEndValue(lastValue);
    props.onChange && props.onChange([startValue, lastValue]);
  };

  const handleStartOpenChange = (open) => {
    if (!open) {
      setEndOpen(true);
    }
  };

  const handleEndOpenChange = (open) => {
    setEndOpen(open);
  };

  return (
    <div className={`${props.className} custom-rangePicker`}>
      <DatePicker
        showToday={false}
        disabled={disabled}
        disabledDate={disabledStartDate}
        // showTime={showTime}
        disabledTime={disabledStartTime}
        format={format}
        value={startValue}
        placeholder={startPlaceHolder}
        onChange={onStartChange}
        onOpenChange={handleStartOpenChange}
      />
      <span className="connector"> ~ </span>
      <DatePicker
        showToday={false}
        disabled={disabled}
        disabledDate={disabledEndDate}
        disabledTime={disabledEndTime}
        // showTime={showTime}
        format={format}
        value={endValue}
        placeholder={endPlaceHolder}
        onChange={onEndChange}
        open={endOpen}
        onOpenChange={handleEndOpenChange}
      />
    </div>
  );
};

function range(start, end) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}

export default RangePicker;
