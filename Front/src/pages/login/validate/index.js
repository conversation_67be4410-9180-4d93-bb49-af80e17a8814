import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Divider, message } from 'antd';
import SsoService from 'service/SsoService';
import { connect } from 'react-redux';
import TextLogo from 'assets/images/<EMAIL>';
import { transformUrl } from 'utils/universal';
import SystemInfo from 'components/bussinesscoms/systeminfocom/index';
import { t } from '@/utils/translation';
import './index.scss';

const mapState = (store) => {
  return { systemInfo: store.systemInfo };
};

const getUrlParam = (search) => {
  const obj = {};
  const data = search.substr(1);
  if (data.length > 0) {
    const dataArr = data.split('&');
    if (dataArr.length > 0) {
      dataArr.forEach((n) => {
        const res = n.split('=');
        if (res.length > 0) {
          obj[res[0]] = res[1];
        }
      });
    }
  }

  return obj;
};

const ValidateComponent = (props) => {
  const [mode, setMode] = useState('fail');
  const [error, setError] = useState('');

  const { systemInfo } = props;

  useEffect(() => {
    (async () => {
      try {
        const param = getUrlParam(props.location.search);
        const res = await SsoService.ssoVerify(param);
        if (res.success) {
          setMode('success');
        } else {
          setMode('fail');
          setError(res.message);
        }
      } catch (error) {
        setMode('fail');
      }
    })();
  }, []);

  const login = async () => {
    const param = getUrlParam(props.location.search);
    await SsoService.ssoLastLogin(param);
    message.success(t('login-c157w79ff6GR'));
    props.history.push('/aimarketer/home');
  };

  return (
    <div className="validateUser">
      <div className="bindInfo">
        <div className="heade">
          <img src={transformUrl(systemInfo['page.light.logoUrl']) || TextLogo} alt="" />
        </div>
        {mode === 'success' && (
          <div className="main">
            <div className="emailTitle">{t('login-lYvNbPXjbDiV')}</div>
            <Button type="primary" className="login-form-button" onClick={login}>
              {t('login-KIwfNrtSk1Mt')}
            </Button>
          </div>
        )}
        {mode === 'fail' && (
          <div className="successMain">
            <div className="failP">{error}</div>
          </div>
        )}
      </div>
      <Divider />
      <SystemInfo />
    </div>
  );
};
export default connect(mapState)(ValidateComponent);
