import React, { useState, useEffect } from 'react';
import { Input, Form, Button, Row, Col, Statistic, message, Divider, Checkbox } from 'antd';
import { connect } from 'react-redux';
import { UserOutlined, PhoneOutlined, SafetyCertificateOutlined } from '@ant-design/icons';
import TextLogo from 'assets/images/<EMAIL>';
import AccountService from 'service/accountService';
import _ from 'lodash';
import SystemInfo from 'components/bussinesscoms/systeminfocom/index';
import { transformUrl } from 'utils/universal';
import { t } from '@/utils/translation';
import './index.scss';

const mapState = (store) => {
  return { systemInfo: store.systemInfo };
};

const { Countdown } = Statistic;

const BindComponent = (props) => {
  const [form] = Form.useForm();
  const [sending, setSending] = useState(false);
  const [info, setInfo] = useState({});
  const { systemInfo } = props;

  const init = async () => {
    const info = {};
    const list = await AccountService.getSystemConfig([]);
    _.map(list, (item) => {
      info[item.name] = item.value;
    });
    setInfo(info);
  };
  useEffect(() => {
    init();
  }, []);

  const onFinish = async (values) => {
    try {
      const res = await AccountService.checkVerifyCode({
        ...values,
        type: 'SMS'
      });
      if (!res) {
        message.error(t('login-sUkvlOb01s1O'));
        return;
      }
      props.history.push({
        pathname: '/aimarketer/perfect',
        state: {
          data: { mobile: values.account, ..._.omit(values, 'account') }
        }
      });
    } catch (error) {
      message.error(t('login-U1Of5A2hQhHh'));
    }
    // props.history.push('/aimarketer/perfect');
  };

  const handleValidator = async (rule, val) => {
    if (val) {
      const res = await AccountService.ensureAccount({ mobile: val.trim() });
      if (!res) {
        return Promise.reject(new Error(rule.message));
      }
    }
    return Promise.resolve();
  };

  const getCaptcha = async () => {
    await form.validateFields(['account']);
    const account = form.getFieldValue('account');
    const res = await AccountService.sendVerifyCode({ type: 'SMS', account });
    if (!res) {
      message.error(t('login-x8sGr1yTEciD'));
      return;
    }
    setSending(true);
    message.success(t('login-952pX9vq4JKh'));
  };
  return (
    <div className="signupUser">
      <div className="bindInfo">
        <div className="heade">
          <img src={transformUrl(systemInfo['page.light.logoUrl']) || TextLogo} alt="" />
        </div>
        <div className="main">
          <div className="emailTitle">{t('login-r0C9BATUSJyz')}</div>
          <Form className="formStyle" onFinish={onFinish} form={form} layout="vertical">
            <Form.Item
              name="name"
              label={t('login-T7mOHW5fT11Z')}
              validateTrigger="onBlur"
              rules={[
                { required: true, message: t('login-mSz5efXolnJb') },
                { max: 32, message: t('login-SLySafVnDE74') }
              ]}
            >
              <Input
                autoComplete="off"
                placeholder={t('login-rxatDWcd6HGU')}
                prefix={<UserOutlined className="site-form-item-icon" style={{ color: 'rgba(0,0,0,.25)' }} />}
                className="inputEmail"
              />
            </Form.Item>
            <Form.Item
              name="account"
              label={t('login-RqCCuoLDoN5Z')}
              validateTrigger="onBlur"
              rules={[
                { required: true, message: t('login-unLwR2bOWK4B') },
                { pattern: /^1[3456789]\d{9}$/, message: t('login-7HzoDgVAprOA') },
                { validator: handleValidator, message: t('login-OvUv2GyDk6Ca') }
              ]}
            >
              <Input
                autoComplete="off"
                placeholder={t('login-gPfLOxUJGBiL')}
                prefix={<PhoneOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                className="inputEmail"
              />
            </Form.Item>
            <Form.Item required validateTrigger="onBlur" label={t('login-PcK47wS9Uu0Z')}>
              <Row gutter={8}>
                <Col span={16}>
                  <Form.Item name="code" noStyle rules={[{ required: true, message: t('login-ocIyNM44mxCf') }]}>
                    <Input
                      autoComplete="off"
                      placeholder={t('login-4LdghRagfAjQ')}
                      prefix={<SafetyCertificateOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                      className="inputEmail"
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Button onClick={getCaptcha} disabled={sending} className="getCaptcha">
                    {sending ? (
                      <Countdown
                        onFinish={() => setSending(false)}
                        valueStyle={{
                          fontSize: 14,
                          color: 'rgba(0, 0, 0, 0.45)'
                        }}
                        value={Date.now() + 1000 * 60}
                        suffix={t('login-FR0p15VNnEXY')}
                        format="ss"
                      />
                    ) : (
                      <span>{t('login-ktY0C1r5r6bz')}</span>
                    )}
                  </Button>
                </Col>
              </Row>
            </Form.Item>

            {info['agreement.policy.status'] === 'ON' ? (
              <Form.Item
                name="state"
                validateTrigger="onBlur"
                valuePropName="checked"
                rules={[
                  {
                    validator: (_, value) =>
                      value ? Promise.resolve() : Promise.reject(new Error(t('login-atSgLBCYaHJp')))
                  }
                ]}
              >
                <Checkbox>
                  {t('login-ebCaP6RJ2PJB')}
                  <a target="_blank" rel="noopener noreferrer" href={info['user.agreement.url']}>
                    {t('login-xOLCqo0PZDal')}
                  </a>
                  {t('login-yt6lljARTomj')}
                  <a target="_blank" rel="noopener noreferrer" href={info['privacy.policy.url']}>
                    {t('login-z9s3hl2684sq')}
                  </a>
                </Checkbox>
              </Form.Item>
            ) : null}

            {/* <Form.Item> */}
            <Button type="primary" className="login-form-button" htmlType="submit">
              {t('login-IXDVrZEEMXpN')}
            </Button>
            {/* </Form.Item> */}
            <div className="desc">
              {t('login-cYbSttXlFSt4')}
              <a onClick={() => props.history.push('/aimarketer/login')}>{t('login-jWeOwc7tIDRS')}</a>
            </div>
          </Form>
        </div>
      </div>
      <Divider />
      <SystemInfo />
    </div>
  );
};
export default connect(mapState)(BindComponent);
