import { message } from 'antd';
import React, { useEffect } from 'react';
import SsoService from 'service/SsoService';
import SystemInfoService from 'service/systemInfoService';
import { setDeptId } from 'utils/commonUtils';
import { t } from '@/utils/translation';

export default (props) => {
  useEffect(() => {
    function getQueryVariable(variable) {
      const query = props.location.search.substring(1);

      const vars = query.split('&');
      for (let i = 0; i < vars.length; i++) {
        const pair = vars[i].split('=');
        if (pair[0] === variable) {
          return pair[1];
        }
      }
      return false;
    }

    (async () => {
      try {
        const domain = getQueryVariable('domain');
        const param = getQueryVariable('param');
        const codeVal = getQueryVariable(param);

        const list = await SystemInfoService.getSystemConfig([]);
        const ssoList = await SsoService.getSsoInfo();
        const { redirectFlag, defaultCompanyId, defaultProjectId } = ssoList[0];

        const systemInfo = list.reduce((obj, n) => {
          obj[n.name] = n.value;
          return obj;
        }, {});

        const res = await SsoService.ssoCheck({ codeVal, domain });
        if (res.success) {
          const loginRes = await SsoService.ssoLogin({ ...res });
          message.success(t('login-69FWDQOryShR'));

          if (redirectFlag) {
            setDeptId(res.deptId, loginRes.userId);
            const defaultDeptId = res.deptId;
            if (defaultCompanyId && defaultDeptId && defaultProjectId) {
              localStorage.setItem('userId', loginRes.userId);

              if (systemInfo['login.redirect'] && systemInfo['login.redirect'] !== 'OFF') {
                const { projectId } = JSON.parse(systemInfo['login.redirect']);

                if (defaultProjectId === projectId) {
                  props.history.push('/aimarketer/home');
                  localStorage.setItem('organizationId', defaultCompanyId);
                  localStorage.setItem('projectId', defaultProjectId);
                } else {
                  props.history.push('/aimarketer/usercenter/productcenter');
                }
              } else {
                props.history.push('/aimarketer/usercenter/productcenter');
              }
            } else {
              props.history.push('/aimarketer/usercenter/productcenter');
            }
          }
        } else {
          props.history.push({
            pathname: '/aimarketer/bind',
            state: { ...res }
          });
        }
      } catch (error) {
        props.history.push('/aimarketer/login');
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return <div />;
};
