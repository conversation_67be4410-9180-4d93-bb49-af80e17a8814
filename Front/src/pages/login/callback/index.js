import { useStore } from '@/store/globalStore';
import { message } from 'antd';
import React, { useEffect } from 'react';
import SsoService from 'service/SsoService';
import SystemInfoService from 'service/systemInfoService';
import { setDeptId } from 'utils/commonUtils';
import { useQuery } from 'utils/customhooks';
import { t } from '@/utils/translation';

export default () => {
  const queryParams = useQuery();
  useEffect(() => {
    (async () => {
      try {
        const code = queryParams.get('code');
        const state = queryParams.get('state');

        const list = await SystemInfoService.getSystemConfig([]);
        const ssoList = await SsoService.getSsoInfo();
        const { redirectFlag, defaultCompanyId, defaultProjectId } = ssoList[0];

        const systemInfo = list.reduce((obj, n) => {
          obj[n.name] = n.value;
          return obj;
        }, {});

        const res = await SsoService.ssoDefaultLogin({ code, state });
        localStorage.setItem('ssoDeptId', res.deptId);
        localStorage.setItem('userId', res.userId);
        // 获取上次登录的部门ID
        const lastLoginDeptId = res.userId ? useStore.getState().lastLoginDeptMap[res.userId] : null;
        console.warn('🚀 ~ 上次登录部门:', lastLoginDeptId, `${lastLoginDeptId ? '命中缓存' : '未命中缓存'}`);
        setDeptId(lastLoginDeptId || res.deptId, res.userId.toString());
        message.success(t('login-T6WpYH775JCX'));

        if (redirectFlag) {
          const defaultDeptId = res.deptId;

          if (defaultCompanyId && defaultDeptId && defaultProjectId) {
            localStorage.setItem('userId', res.userId);

            if (systemInfo['login.redirect'] && systemInfo['login.redirect'] !== 'OFF') {
              const { projectId } = JSON.parse(systemInfo['login.redirect']);

              if (defaultProjectId === projectId) {
                window.top !== window.self
                  ? (window.top.location.href = '/aimarketer/home')
                  : (window.location.href = '/aimarketer/home');
                localStorage.setItem('organizationId', defaultCompanyId);
                localStorage.setItem('projectId', defaultProjectId);
              } else {
                window.top !== window.self
                  ? (window.top.location.href = '/aimarketer/usercenter/productcenter')
                  : (window.location.href = '/aimarketer/usercenter/productcenter');
              }
            } else {
              window.top !== window.self
                ? (window.top.location.href = '/aimarketer/usercenter/productcenter')
                : (window.location.href = '/aimarketer/usercenter/productcenter');
            }
          } else {
            window.top !== window.self
              ? (window.top.location.href = '/aimarketer/usercenter/productcenter')
              : (window.location.href = '/aimarketer/usercenter/productcenter');
          }
        } else {
          window.top !== window.self
            ? (window.top.location.href = '/aimarketer/usercenter/productcenter')
            : (window.location.href = '/aimarketer/usercenter/productcenter');
        }

        // if (window.top !== window.self) {
        //   window.top.location.href = '/aimarketer/home';
        // } else {
        //   window.location.href = '/aimarketer/home';
        // }
      } catch (error) {
        console.error(error);
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <div />;
};
