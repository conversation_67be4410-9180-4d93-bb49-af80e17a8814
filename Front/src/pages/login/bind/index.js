import React, { useState } from 'react';
import { Input, Form, Button, Divider } from 'antd';
import SsoService from 'service/SsoService';
import { connect } from 'react-redux';
import TextLogo from 'assets/images/<EMAIL>';
import SystemInfo from 'components/bussinesscoms/systeminfocom/index';
import { transformUrl } from 'utils/universal';
import { t } from '@/utils/translation';

import './index.scss';

const mapState = (store) => {
  return { systemInfo: store.systemInfo };
};

const BindComponent = (props) => {
  const [form] = Form.useForm();
  const [mode, setMode] = useState('add');
  const [email, setEmail] = useState('');

  const {
    location: { state },
    systemInfo
  } = props;

  const onFinish = async (value) => {
    const data = { ...value, ...state };
    setEmail(value.email);
    await SsoService.ssoBind(data);
    setMode('success');
  };

  return (
    <div className="bindUser">
      <div className="bindInfo">
        <div className="heade">
          <img src={transformUrl(systemInfo['page.light.logoUrl']) || TextLogo} alt="" />
        </div>
        {mode === 'add' && (
          <div className="main">
            <div className="emailTitle">{t('login-aMiHPPXuZBNu')}</div>
            <Form onFinish={onFinish} form={form} layout="vertical">
              <Form.Item name="email" label={t('login-yMW4HDYsZE8G')} rules={[{ required: true, type: 'email' }]}>
                <Input placeholder={t('login-dH7IMmX2JUU5')} className="inputEmail" />
              </Form.Item>
              <Form.Item>
                <Button type="primary" className="login-form-button" htmlType="submit">
                  {t('login-kKumSVV459NL')}
                </Button>
              </Form.Item>
            </Form>
          </div>
        )}
        {mode === 'success' && (
          <div className="successMain">
            <div className="successP">{t('login-kM7Fc9WWSUNk', { email })}</div>
            <div className="successP">{t('login-Ej8Ej8Ej8Ej0')}</div>
            <div className="noResive">
              未收到链接？<a onClick={() => onFinish({ email })}>重新发送</a>
            </div>
          </div>
        )}
      </div>
      <Divider />
      <SystemInfo />
    </div>
  );
};
export default connect(mapState)(BindComponent);
