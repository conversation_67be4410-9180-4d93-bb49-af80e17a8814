import { PhoneOutlined, SafetyCertificateOutlined, UserOutlined } from '@ant-design/icons';
import { Button, Col, Divider, Form, Input, message, Result, Row, Statistic } from 'antd';
import TextLogo from 'assets/images/<EMAIL>';
import SystemInfo from 'components/bussinesscoms/systeminfocom/index';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import AccountService from 'service/accountService';
import UserServiceBefore from 'service/UserService';
import { transformUrl } from 'utils/universal';
import { t } from '@/utils/translation';
import './index.scss';

const UserService = new UserServiceBefore();

const mapState = (store) => {
  return { systemInfo: store.systemInfo };
};

const { Countdown } = Statistic;

const BindComponent = (props) => {
  const [form] = Form.useForm();
  const [sending, setSending] = useState(false);
  const [step, setStep] = useState(1);

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState({});
  const { systemInfo } = props;

  const [vaildData, setVaildData] = useState({});

  useEffect(() => {
    const init = async () => {
      const res = await UserService.getByTypeAndCode({
        type: 'PASSWORD_SAFETY',
        code: 'safety.verify'
      });
      setVaildData(res[0]);
    };
    init();
  }, []);

  const onFinish = async (value) => {
    try {
      setLoading(true);
      if (step === 1) {
        const res = await AccountService.checkVerifyCode({
          ...value,
          type: 'SMS'
        });
        setData({ ...value });
        if (!res) {
          message.error(t('login-6EEX5j3Bs6Au'));
          setLoading(false);
          return;
        }
      } else if (step === 2) {
        await AccountService.resetPwdByMobile({ ...value, ...data });
      }
      setLoading(false);
      setStep(step + 1);
    } catch (error) {
      setLoading(false);
    }
  };

  const handleValidator = async (rule, val) => {
    if (val) {
      const res = await AccountService.ensureAccount({ mobile: val.trim() });
      if (res) {
        return Promise.reject(new Error(rule.message));
      }
    }
    return Promise.resolve();
  };

  const getCaptcha = async () => {
    await form.validateFields(['account']);
    const account = form.getFieldValue('account');
    const res = await AccountService.sendVerifyCode({ type: 'SMS', account });
    if (!res) {
      message.error(t('login-2FStaNZowhDk'));
      return;
    }
    setSending(true);
    message.success(t('login-lS1lAmZ9yfzN'));
  };

  return (
    <div className="forgetPasswordss">
      <div className="bindInfo">
        {[1, 2].includes(step) && (
          <div className="heade">
            <img src={transformUrl(systemInfo['page.light.logoUrl']) || TextLogo} alt="" />
          </div>
        )}
        {[1, 2].includes(step) && (
          <div className="main">
            <div className="emailTitle">{t('login-mvaDHfmGDF87')}</div>
            <Form className="formStyle" onFinish={onFinish} form={form} layout="vertical">
              {step === 2 && (
                <Form.Item
                  name="pwd"
                  label={t('login-0rdA9ES5Y6Xh')}
                  validateTrigger="onBlur"
                  rules={[
                    { required: true, message: t('login-pmTBaCjj3YA6') },
                    {
                      pattern:
                        vaildData?.value === 'ON'
                          ? new RegExp(
                              `^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()\\-_=+{};:,<.>])(?!.*[\u4E00-\u9FA5])(?!.*\\s).{${vaildData.config.minLength},24}$`
                            )
                          : /^[0-9A-Za-z\\W_=?.。~!@#$%^&*()-=]{6,32}$/,
                      message: vaildData?.value === 'ON' ? t('login-jxqi770uiUPo') : t('login-tGibcJYHuSop')
                    }
                  ]}
                >
                  <Input.Password
                    autoComplete="off"
                    placeholder={t('login-nBN2IcGv14Y6')}
                    prefix={<UserOutlined className="site-form-item-icon" style={{ color: 'rgba(0,0,0,.25)' }} />}
                    className="inputEmail"
                  />
                </Form.Item>
              )}
              {step === 1 && (
                <Form.Item
                  name="account"
                  validateTrigger="onBlur"
                  label={t('login-VfIHM3eBsWvr')}
                  rules={[
                    { required: true, message: t('login-tGXh1rk7uIW2') },
                    {
                      pattern: /^1[3456789]\d{9}$/,
                      message: t('login-CpGJIIKem7gk')
                    },
                    { validator: handleValidator, message: t('login-7pF5mzcBwljZ') }
                  ]}
                >
                  <Input
                    autoComplete="off"
                    placeholder={t('login-BsmLMgOqYvYh')}
                    prefix={<PhoneOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                    className="inputEmail"
                  />
                </Form.Item>
              )}
              {step === 1 && (
                <Form.Item required label={t('login-x10SyPw8qgji')}>
                  <Row gutter={8}>
                    <Col span={16}>
                      <Form.Item
                        name="code"
                        noStyle
                        validateTrigger="onBlur"
                        rules={[{ required: true, message: t('login-ENC5I2LoSd8Q') }]}
                      >
                        <Input
                          autoComplete="off"
                          placeholder={t('login-xJV8AP8lJ2Im')}
                          prefix={<SafetyCertificateOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                          className="inputEmail"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Button onClick={getCaptcha} disabled={sending} className="getCaptcha">
                        {sending ? (
                          <Countdown
                            onFinish={() => setSending(false)}
                            valueStyle={{
                              fontSize: 14,
                              color: 'rgba(0, 0, 0, 0.45)'
                            }}
                            value={Date.now() + 1000 * 60}
                            suffix={t('login-QEXgCLfv8q7U')}
                            format="ss"
                          />
                        ) : (
                          <span>{t('login-JTZfAOjm6MV8')}</span>
                        )}
                      </Button>
                    </Col>
                  </Row>
                </Form.Item>
              )}
              {/* <Form.Item> */}
              <Button type="primary" disabled={loading} className="login-form-button" htmlType="submit">
                {step === 1 && t('login-qP0IcKDcEVg5')}
                {step === 2 && t('login-PbCm8e64RlJe')}
              </Button>
              {/* </Form.Item> */}
              <div className="desc">
                {t('login-i3St29vPyVcb')}
                <a onClick={() => props.history.push('/aimarketer/login')}>{t('login-8tNFjvEUJABy')}</a>
              </div>
            </Form>
          </div>
        )}
        {step === 3 && (
          <Result
            status="success"
            title={t('login-3t0iFeKth7bJ')}
            style={{ marginTop: 200 }}
            extra={[
              <Button onClick={() => props.history.push('/aimarketer/login')} type="primary" key="console">
                {t('login-yk5nildxfxzE')}
              </Button>
            ]}
          />
        )}
      </div>
      <Divider />
      <SystemInfo />
    </div>
  );
};
export default connect(mapState)(BindComponent);
