import { KeyOutlined, LockOutlined, UserOutlined } from '@ant-design/icons';
import { Button, Form, Input, Space, Spin } from 'antd';
import { t } from '@/utils/translation';
import background from '../../assets/images/loginYJ.png';
import logo from '../../assets/images/logoYJ.png';

type LoginYJProps = {
  handleSubmit: (values: any) => void;
  loading: boolean;
  codeStatus: boolean;
  codeImg: string;
  getCodeImg: () => void;
};

export default function LoginYJ({ handleSubmit, loading, codeStatus, codeImg, getCodeImg }: LoginYJProps) {
  return (
    <Spin spinning={loading}>
      <div className="loginYJ">
        <div className="bgimg">
          <img src={background} tabIndex={-1} alt="" />
        </div>
        <div className="loginForm">
          <img src={logo} alt="" />
          <div className="title">{t('login-E7Earog3pftp')}</div>
          <div>
            <Form name="basic" onFinish={handleSubmit} autoComplete="off">
              <Form.Item
                name="username"
                rules={[
                  {
                    required: true,
                    message: t('login-3gOMmFz6Q5Y6')
                  },
                  () => ({
                    validator(_, value) {
                      const valueValid =
                        /^[a-zA-Z0-9]{1,8}$/.test(value) ||
                        /^(?:(?:\+|00)86)?1\d{10}$/.test(value) ||
                        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
                          value
                        );
                      if (!valueValid) {
                        return Promise.reject(new Error(t('login-saQJG2FFc9YZ')));
                      } else {
                        return Promise.resolve();
                      }
                    }
                  })
                ]}
              >
                <Input
                  prefix={<UserOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                  placeholder={t('login-yCSGfX4IukoG')}
                  style={{ height: 38 }}
                />
              </Form.Item>

              <Form.Item name="password" rules={[{ required: true, message: t('login-UGRwHocEa76Z') }]}>
                <Input
                  type="password"
                  prefix={<LockOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                  placeholder={t('login-AegIu30WLiF0')}
                  style={{ height: 38 }}
                />
              </Form.Item>

              {codeStatus && (
                <Form.Item name="code" rules={[{ required: true, message: t('login-L1axbsklvYfa') }]}>
                  <Space>
                    <Input
                      prefix={<KeyOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                      placeholder={t('login-nx9nxLzMnlHf')}
                      style={{ height: 38, width: 212 }}
                    />
                    <div onClick={getCodeImg}>
                      <img src={codeImg} alt="" className="w-[100px] cursor-pointer" />
                    </div>
                  </Space>
                </Form.Item>
              )}

              <Form.Item>
                <Button type="primary" htmlType="submit" style={{ width: '100%', height: 40, borderRadius: 8 }}>
                  {t('login-UtoKI1NDqfhr')}
                </Button>
              </Form.Item>
            </Form>
          </div>
        </div>
      </div>
    </Spin>
  );
}
