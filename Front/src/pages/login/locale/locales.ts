export default {
  cn: {
    // Front/src/pages/login/Login.jsx
    'login-M6E6RkiFBrcP': '登录成功',
    'login-AUwMFo0coAFY': '验证码过期,请重新输入',
    'login-AY8EFjPhm4bk': '验证码错误,请重新输入',
    'login-4x05CsRfr4XM': '登录Datatist预测营销云',
    'login-CjEqJ8mKhREM': '手机号/邮箱/员工号',
    'login-xoUAPNBDPjoG': '登录手机号/邮箱/员工号!',
    'login-1XAsM3eIQAX2': '请输入正确的手机号或邮箱或员工号',
    'login-EfZjXSCHq8R2': '用户名',
    'login-6lWzw4Rm7HR2': '密码',

    // Front/src/pages/login/LoginYJ.tsx
    'login-E7Earog3pftp': '欢迎使用客户运营自动化平台',
    'login-3gOMmFz6Q5Y6': '登录手机号/邮箱/员工号!',
    'login-saQJG2FFc9YZ': '请输入正确的手机号或邮箱或员工号',
    'login-yCSGfX4IukoG': '用户名',
    'login-UGRwHocEa76Z': '请输入密码',
    'login-AegIu30WLiF0': '密码',
    'login-L1axbsklvYfa': '请输入验证码',
    'login-nx9nxLzMnlHf': '验证码',
    'login-UtoKI1NDqfhr': '登录',

    // Front/src/pages/login/LoginSW.jsx
    'login-uakMhQsuRmNZ': '欢迎使用客户运营自动化平台',
    'login-1P7iTEaLLNmC': '员工号格式不正确',
    'login-kw7tPjucwWUo': '员工号',
    'login-Sy1TW89aFVbp': '请输入密码',
    'login-rS6k8dbJBek2': '密码',
    'login-smow1UZDgLrB': '请输入验证码',
    'login-ar5EsTI6tCUs': '验证码',
    'login-w4dFGRM2P6SS': '登录',

    // Front/src/pages/login/bind/index.js
    'login-aMiHPPXuZBNu': '请绑定企业邮箱',
    'login-yMW4HDYsZE8G': '企业邮箱',
    'login-dH7IMmX2JUU5': '请输入企业邮箱',
    'login-kKumSVV459NL': '发送验证链接',
    'login-kM7Fc9WWSUNk': '已发送链接到邮箱 {email}',
    'login-j7wr8JdIsV3z': '请点击邮箱中的链接完成验证',
    'login-3Bi1K1rDtpDe': '未收到链接？',
    'login-iQXtkno8jK6O': '重新发送',

    // Front/src/pages/login/forgetPassword/index.js
    'login-6EEX5j3Bs6Au': '验证失败,请重试',
    'login-2FStaNZowhDk': '发送失败，请重试',
    'login-lS1lAmZ9yfzN': '发送成功',
    'login-mvaDHfmGDF87': '忘记密码',
    'login-0rdA9ES5Y6Xh': '设置新密码',
    'login-pmTBaCjj3YA6': '密码必填',
    'login-jxqi770uiUPo': '密码必须包含大写字母、小写字母、数字和特殊字符',
    'login-tGibcJYHuSop': '必须为6-32位的数字字母或特殊字符',
    'login-nBN2IcGv14Y6': '请输入新密码',
    'login-VfIHM3eBsWvr': '手机号码',
    'login-tGXh1rk7uIW2': '手机号必填',
    'login-CpGJIIKem7gk': '请输入正确的手机号',
    'login-7pF5mzcBwljZ': '手机号不存在',
    'login-BsmLMgOqYvYh': '请输入手机号码',
    'login-x10SyPw8qgji': '验证码',
    'login-ENC5I2LoSd8Q': '请输入验证码!',
    'login-xJV8AP8lJ2Im': '请输入验证码',
    'login-QEXgCLfv8q7U': '秒后重试',
    'login-JTZfAOjm6MV8': '获取验证码',
    'login-qP0IcKDcEVg5': '下一步',
    'login-PbCm8e64RlJe': '完成',
    'login-i3St29vPyVcb': '已经有账号？',
    'login-8tNFjvEUJABy': '登录',
    'login-3t0iFeKth7bJ': '重置密码成功',
    'login-yk5nildxfxzE': '返回登陆',

    // Front/src/pages/login/perfect/index.js
    'login-zZiKnF0vO9v9': '操作完成',
    'login-nbQMBFPt3c2v': '注册成功',
    'login-6TxpBWbw8Ixp': '前往产品中心',
    'login-d5DXTQNq4G4Q': '完善信息',
    'login-aYa6telGOwxj': '邮箱',
    'login-GADikqOMsWGP': '邮箱必填',
    'login-VNbxXjCZVxPi': '邮箱已存在',
    'login-m89ru6OUaCCs': '请输入正确邮箱',
    'login-Je53gvuT4rMJ': '请输入邮箱',
    'login-UTGPT4bdyZR9': '工作职位',
    'login-FxtcCVsSW6tU': '工作职位必填',
    'login-wRGHMS9eO0EB': '仅支持英文、中文最大支持32个字符',
    'login-qb8kuYNNqPiu': '请输入工作职位',
    'login-m5WypxCam4I4': '设置登录密码',
    'login-Gt3f7Jdvwghp': '密码必填',
    'login-rNGIuiWeglH6': '密码必须包含大写字母、小写字母、数字和特殊字符',
    'login-6Gm5QS7LCycx': '必须为6-32位的数字字母或特殊字符',
    'login-QOnrg0jQQWAW': '请输入登录密码',
    'login-KuFAefU3y2E6': '完成',
    'login-BPyXg6OGEv5U': '已经有账号？',
    'login-oJsUiPtVSY5Z': '登录',

    // Front/src/pages/login/signUp/index.js
    'login-sUkvlOb01s1O': '验证码错误，请重新输入',
    'login-U1Of5A2hQhHh': '验证失败,请重试',
    'login-x8sGr1yTEciD': '发送失败，请重试',
    'login-952pX9vq4JKh': '发送成功',
    'login-r0C9BATUSJyz': '注册账号',
    'login-T7mOHW5fT11Z': '姓名',
    'login-mSz5efXolnJb': '请输入姓名',
    'login-SLySafVnDE74': '最多输入32个字符',
    'login-rxatDWcd6HGU': '请输入姓名',
    'login-RqCCuoLDoN5Z': '手机号码',
    'login-unLwR2bOWK4B': '手机号必填',
    'login-7HzoDgVAprOA': '请输入正确的手机号',
    'login-OvUv2GyDk6Ca': '手机号已存在',
    'login-gPfLOxUJGBiL': '请输入手机号码',
    'login-PcK47wS9Uu0Z': '验证码',
    'login-ocIyNM44mxCf': '请输入验证码!',
    'login-4LdghRagfAjQ': '请输入验证码',
    'login-FR0p15VNnEXY': '秒后重试',
    'login-ktY0C1r5r6bz': '获取验证码',
    'login-atSgLBCYaHJp': '请阅读并勾选接受用户协议和隐私政策',
    'login-ebCaP6RJ2PJB': '我已阅读并接受',
    'login-xOLCqo0PZDal': '用户协议',
    'login-yt6lljARTomj': '和',
    'login-z9s3hl2684sq': '隐私政策',
    'login-IXDVrZEEMXpN': '下一步',
    'login-cYbSttXlFSt4': '已经有账号？',
    'login-jWeOwc7tIDRS': '登录',

    // Front/src/pages/login/validate/index.js
    'login-c157w79ff6GR': '登录成功',
    'login-lYvNbPXjbDiV': '已完成验证',
    'login-KIwfNrtSk1Mt': '进入产品',

    // Front/src/pages/login/success/index.jsx
    'login-DbWhraiIA5mQ': 'props中systemInfo为undefined时重新请求系统信息接口',
    'login-0QHppxXY5zXf': '登录成功',
    'login-VW9SpHq41qkk': '登录失败',
    'login-D7loeqrz1b3r': '稍后将跳转到首页',
    'login-YcG1d1SWoGXo': '请联系管理员处理',

    // Front/src/pages/login/error/index.tsx
    'login-zCgvpicnOXOQ': '账号锁定',
    'login-1iZphyAJVAmu': '该账号密码已过期，请联系管理员',
    'login-THvcARfK820M': '账号密码错误累计超过 {failedCount} 次，',
    'login-z0mbbisrGucf': '已锁定该账号，请联系管理员解锁账号',
    'login-EGS4SFfdgimY': '请 {lockedUntil} 后重试',
    'login-upJxPvZMXUdQ': '忘记密码',
    'login-AdYDpE3YWi6X': '返回登录',

    // Front/src/pages/login/callback/index.js
    'login-bR7Q3Ynt3zw3': '获取上次登录的部门ID',
    'login-POY969HSmW8O': '上次登录部门:',
    'login-T6WpYH775JCX': '登录成功',

    // Front/src/pages/login/receive/index.js
    'login-69FWDQOryShR': '登录成功',

    // Front/src/pages/login/ssoLogin/index.js
    'login-ws8VW1WWPXaP': '获取单点登录地址失败！，请重试！',

    // Front/src/pages/login/testCallback/index.tsx
    'login-MvMTvYx3orSB': '定义接口类型',
    'login-FFXoga6M3XAK': '提取常量',
    'login-ylnaklfe6UqF': '请输入userId',
    'login-gF77XCoYSn6F': '请输入deptId',
    'login-YoK4eVuHbhfE': '请输入defaultProjectId',
    'login-plCNNevbhA2M': '请输入defaultCompanyId',
    'login-9qfFA8GwtDx4': '请输入token',
    'login-Z5AQbWAZLfpX': '处理页面跳转逻辑',
    'login-DsP11KuOC6pR': '获取系统配置',
    'login-p2JNIWE436yW': '设置本地存储',
    'login-ILr19dFhWSNS': '上次登录部门:',
    'login-RN1ahDD9jR36': '命中缓存',
    'login-W8NMKXq4KQVS': '未命中缓存',
    'login-7Ah9wAOBIaHA': '登录成功',
    'login-z3p5GEljctkz': '登录失败，请重试',
    'login-oPEl1MtGv7BM': '保存成功',
    'login-JO2jY3Dl1jMX': '保存失败，请重试',
    'login-xbFqZDIqWmM4': '模拟sso登录',
    'login-IXvpTOz5NoNx': '保存当前页面数据'
  },
  en: {
    // Front/src/pages/login/Login.jsx
    'login-M6E6RkiFBrcP': 'Login successful',
    'login-AUwMFo0coAFY': 'Verification code expired, please re-enter',
    'login-AY8EFjPhm4bk': 'Verification code error, please re-enter',
    'login-4x05CsRfr4XM': 'Login to Datatist Predictive Marketing Cloud',
    'login-CjEqJ8mKhREM': 'Mobile/Email/Employee ID',
    'login-xoUAPNBDPjoG': 'Login mobile/email/employee ID!',
    'login-1XAsM3eIQAX2': 'Please enter correct mobile number or email or employee ID',
    'login-EfZjXSCHq8R2': 'Username',
    'login-6lWzw4Rm7HR2': 'Password',

    // Front/src/pages/login/LoginYJ.tsx
    'login-E7Earog3pftp': 'Welcome to Customer Operations Automation Platform',
    'login-3gOMmFz6Q5Y6': 'Login mobile/email/employee ID!',
    'login-saQJG2FFc9YZ': 'Please enter correct mobile number or email or employee ID',
    'login-yCSGfX4IukoG': 'Username',
    'login-UGRwHocEa76Z': 'Please enter password',
    'login-AegIu30WLiF0': 'Password',
    'login-L1axbsklvYfa': 'Please enter verification code',
    'login-nx9nxLzMnlHf': 'Verification Code',
    'login-UtoKI1NDqfhr': 'Login',

    // Front/src/pages/login/LoginSW.jsx
    'login-uakMhQsuRmNZ': 'Welcome to Customer Operations Automation Platform',
    'login-1P7iTEaLLNmC': 'Employee ID format is incorrect',
    'login-kw7tPjucwWUo': 'Employee ID',
    'login-Sy1TW89aFVbp': 'Please enter password',
    'login-rS6k8dbJBek2': 'Password',
    'login-smow1UZDgLrB': 'Please enter verification code',
    'login-ar5EsTI6tCUs': 'Verification Code',
    'login-w4dFGRM2P6SS': 'Login',

    // Front/src/pages/login/bind/index.js
    'login-aMiHPPXuZBNu': 'Please bind enterprise email',
    'login-yMW4HDYsZE8G': 'Enterprise Email',
    'login-dH7IMmX2JUU5': 'Please enter enterprise email',
    'login-kKumSVV459NL': 'Send verification link',
    'login-kM7Fc9WWSUNk': 'Link sent to email {email}',
    'login-j7wr8JdIsV3z': 'Please click the link in the email to complete verification',
    'login-3Bi1K1rDtpDe': 'Did not receive the link?',
    'login-iQXtkno8jK6O': 'Resend',

    // Front/src/pages/login/forgetPassword/index.js
    'login-6EEX5j3Bs6Au': 'Verification failed, please try again',
    'login-2FStaNZowhDk': 'Send failed, please try again',
    'login-lS1lAmZ9yfzN': 'Send successful',
    'login-mvaDHfmGDF87': 'Forgot Password',
    'login-0rdA9ES5Y6Xh': 'Set new password',
    'login-pmTBaCjj3YA6': 'Password is required',
    'login-jxqi770uiUPo': 'Password must contain uppercase letters, lowercase letters, numbers and special characters',
    'login-tGibcJYHuSop': 'Must be 6-32 digits, letters or special characters',
    'login-nBN2IcGv14Y6': 'Please enter new password',
    'login-VfIHM3eBsWvr': 'Mobile Number',
    'login-tGXh1rk7uIW2': 'Mobile number is required',
    'login-CpGJIIKem7gk': 'Please enter correct mobile number',
    'login-7pF5mzcBwljZ': 'Mobile number does not exist',
    'login-BsmLMgOqYvYh': 'Please enter mobile number',
    'login-x10SyPw8qgji': 'Verification Code',
    'login-ENC5I2LoSd8Q': 'Please enter verification code!',
    'login-xJV8AP8lJ2Im': 'Please enter verification code',
    'login-QEXgCLfv8q7U': 'seconds to retry',
    'login-JTZfAOjm6MV8': 'Get verification code',
    'login-qP0IcKDcEVg5': 'Next',
    'login-PbCm8e64RlJe': 'Complete',
    'login-i3St29vPyVcb': 'Already have an account?',
    'login-8tNFjvEUJABy': 'Login',
    'login-3t0iFeKth7bJ': 'Password reset successful',
    'login-yk5nildxfxzE': 'Back to login',

    // Front/src/pages/login/perfect/index.js
    'login-zZiKnF0vO9v9': 'Operation completed',
    'login-nbQMBFPt3c2v': 'Registration successful',
    'login-6TxpBWbw8Ixp': 'Go to Product Center',
    'login-d5DXTQNq4G4Q': 'Complete Information',
    'login-aYa6telGOwxj': 'Email',
    'login-GADikqOMsWGP': 'Email is required',
    'login-VNbxXjCZVxPi': 'Email already exists',
    'login-m89ru6OUaCCs': 'Please enter correct email',
    'login-Je53gvuT4rMJ': 'Please enter email',
    'login-UTGPT4bdyZR9': 'Job Position',
    'login-FxtcCVsSW6tU': 'Job position is required',
    'login-wRGHMS9eO0EB': 'Only English and Chinese supported, maximum 32 characters',
    'login-qb8kuYNNqPiu': 'Please enter job position',
    'login-m5WypxCam4I4': 'Set login password',
    'login-Gt3f7Jdvwghp': 'Password is required',
    'login-rNGIuiWeglH6': 'Password must contain uppercase letters, lowercase letters, numbers and special characters',
    'login-6Gm5QS7LCycx': 'Must be 6-32 digits, letters or special characters',
    'login-QOnrg0jQQWAW': 'Please enter login password',
    'login-KuFAefU3y2E6': 'Complete',
    'login-BPyXg6OGEv5U': 'Already have an account?',
    'login-oJsUiPtVSY5Z': 'Login',

    // Front/src/pages/login/signUp/index.js
    'login-sUkvlOb01s1O': 'Verification code error, please re-enter',
    'login-U1Of5A2hQhHh': 'Verification failed, please try again',
    'login-x8sGr1yTEciD': 'Send failed, please try again',
    'login-952pX9vq4JKh': 'Send successful',
    'login-r0C9BATUSJyz': 'Register Account',
    'login-T7mOHW5fT11Z': 'Name',
    'login-mSz5efXolnJb': 'Please enter name',
    'login-SLySafVnDE74': 'Maximum 32 characters',
    'login-rxatDWcd6HGU': 'Please enter name',
    'login-RqCCuoLDoN5Z': 'Mobile Number',
    'login-unLwR2bOWK4B': 'Mobile number is required',
    'login-7HzoDgVAprOA': 'Please enter correct mobile number',
    'login-OvUv2GyDk6Ca': 'Mobile number already exists',
    'login-gPfLOxUJGBiL': 'Please enter mobile number',
    'login-PcK47wS9Uu0Z': 'Verification Code',
    'login-ocIyNM44mxCf': 'Please enter verification code!',
    'login-4LdghRagfAjQ': 'Please enter verification code',
    'login-FR0p15VNnEXY': 'seconds to retry',
    'login-ktY0C1r5r6bz': 'Get verification code',
    'login-atSgLBCYaHJp': 'Please read and check to accept user agreement and privacy policy',
    'login-ebCaP6RJ2PJB': 'I have read and accept',
    'login-xOLCqo0PZDal': 'User Agreement',
    'login-yt6lljARTomj': 'and',
    'login-z9s3hl2684sq': 'Privacy Policy',
    'login-IXDVrZEEMXpN': 'Next',
    'login-cYbSttXlFSt4': 'Already have an account?',
    'login-jWeOwc7tIDRS': 'Login',

    // Front/src/pages/login/validate/index.js
    'login-c157w79ff6GR': 'Login successful',
    'login-lYvNbPXjbDiV': 'Verification completed',
    'login-KIwfNrtSk1Mt': 'Enter Product',

    // Front/src/pages/login/success/index.jsx
    'login-DbWhraiIA5mQ': 'Re-request system info interface when systemInfo is undefined in props',
    'login-0QHppxXY5zXf': 'Login successful',
    'login-VW9SpHq41qkk': 'Login failed',
    'login-D7loeqrz1b3r': 'Will redirect to homepage shortly',
    'login-YcG1d1SWoGXo': 'Please contact administrator',

    // Front/src/pages/login/error/index.tsx
    'login-zCgvpicnOXOQ': 'Account Locked',
    'login-1iZphyAJVAmu': 'Account password has expired, please contact administrator',
    'login-THvcARfK820M': 'Account password error accumulated more than {failedCount} times,',
    'login-z0mbbisrGucf': 'Account has been locked, please contact administrator to unlock',
    'login-EGS4SFfdgimY': 'Please try again after {lockedUntil}',
    'login-upJxPvZMXUdQ': 'Forgot Password',
    'login-AdYDpE3YWi6X': 'Back to Login',

    // Front/src/pages/login/callback/index.js
    'login-bR7Q3Ynt3zw3': 'Get last login department ID',
    'login-POY969HSmW8O': 'Last login department:',
    'login-T6WpYH775JCX': 'Login successful',

    // Front/src/pages/login/receive/index.js
    'login-69FWDQOryShR': 'Login successful',

    // Front/src/pages/login/ssoLogin/index.js
    'login-ws8VW1WWPXaP': 'Failed to get SSO login address! Please try again!',

    // Front/src/pages/login/testCallback/index.tsx
    'login-MvMTvYx3orSB': 'Define interface types',
    'login-FFXoga6M3XAK': 'Extract constants',
    'login-ylnaklfe6UqF': 'Please enter userId',
    'login-gF77XCoYSn6F': 'Please enter deptId',
    'login-YoK4eVuHbhfE': 'Please enter defaultProjectId',
    'login-plCNNevbhA2M': 'Please enter defaultCompanyId',
    'login-9qfFA8GwtDx4': 'Please enter token',
    'login-Z5AQbWAZLfpX': 'Handle page navigation logic',
    'login-DsP11KuOC6pR': 'Get system configuration',
    'login-p2JNIWE436yW': 'Set local storage',
    'login-ILr19dFhWSNS': 'Last login department:',
    'login-RN1ahDD9jR36': 'Cache hit',
    'login-W8NMKXq4KQVS': 'Cache miss',
    'login-7Ah9wAOBIaHA': 'Login successful',
    'login-z3p5GEljctkz': 'Login failed, please try again',
    'login-oPEl1MtGv7BM': 'Save successful',
    'login-JO2jY3Dl1jMX': 'Save failed, please try again',
    'login-xbFqZDIqWmM4': 'Simulate SSO login',
    'login-IXvpTOz5NoNx': 'Save current page data'
  }
};
