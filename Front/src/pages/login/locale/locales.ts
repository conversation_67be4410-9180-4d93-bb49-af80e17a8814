export default {
  cn: {
    // Front/src/pages/login/Login.jsx
    'login-WHFAXCIKmdWp': '登录成功',
    'login-eQYOd1wKX90O': '验证码过期,请重新输入',
    'login-ChfAVnS6ZI4P': '验证码错误,请重新输入',
    'login-irFcQ8TzlM7I': '登录Datatist预测营销云',
    'login-ptdKTgqpOMyg': '手机号/邮箱/员工号',
    'login-CijH0QCNEkLA': '登录手机号/邮箱/员工号!',
    'login-axF4mdh3uvX7': '请输入正确的手机号或邮箱或员工号',
    'login-0UYMlZQSfE1e': '用户名',
    'login-wBuk1F1LxPhJ': '密码',

    // Front/src/pages/login/LoginYJ.tsx
    'login-nbx2J6BqraJ1': '欢迎使用客户运营自动化平台',
    'login-7c6nQ3OI7yKV': '登录手机号/邮箱/员工号!',
    'login-hSEp9aVxxsY3': '请输入正确的手机号或邮箱或员工号',
    'login-dIXAtjd9iawP': '用户名',
    'login-78dqg8TAQqBP': '请输入密码',
    'login-cdBWxBBY3S88': '密码',
    'login-7o2PLDASJdo9': '请输入验证码',
    'login-AXUkRZl764ij': '验证码',
    'login-4OAzddiikokG': '登录',

    // Front/src/pages/login/LoginSW.jsx
    'login-NTZ2INPgjn6T': '欢迎使用客户运营自动化平台',
    'login-ADeYMrVRXIcT': '员工号格式不正确',
    'login-blnJosOYMkJU': '员工号',
    'login-zwQo6vfaMzR8': '请输入密码',
    'login-4mDVU6iXWKH2': '密码',
    'login-QjPqHn8wzUdq': '请输入验证码',
    'login-eQpIXF2EKybU': '验证码',
    'login-d3ffPdc9xY0S': '登录',

    // Front/src/pages/login/bind/index.js
    'login-xF7WbKkU4Mr7': '请绑定企业邮箱',
    'login-pDPCqRAI02nx': '企业邮箱',
    'login-NwkRWL1sCqdk': '请输入企业邮箱',
    'login-g0CrYZIAScDP': '发送验证链接',
    'login-RdoyVtldOXnu': '已发送链接到邮箱 {email}',
    'login-UR0SrB97Kvv5': '请点击邮箱中的链接完成验证',
    'login-UvX5s1W2KXYJ': '未收到链接？',
    'login-UdQs4eRAqQ4J': '重新发送',

    // Front/src/pages/login/forgetPassword/index.js
    'login-VZNFTmHKECbq': '验证失败,请重试',
    'login-KdnUSWBGmtww': '发送失败，请重试',
    'login-Hy1w9MKRawCp': '发送成功',
    'login-PcDgZXe5sFTd': '忘记密码',
    'login-miOEB0B6Iz9O': '设置新密码',
    'login-0lZNouE5cYzO': '密码必填',
    'login-m7T9rHhlnnck': '密码必须包含大写字母、小写字母、数字和特殊字符',
    'login-q6heShCLLtX0': '必须为6-32位的数字字母或特殊字符',
    'login-Ot5vEaasGFFc': '请输入新密码',
    'login-fCAG5TCMdzS2': '手机号码',
    'login-4a49CtvSKxE4': '手机号必填',
    'login-u0hEftN7eQiB': '请输入正确的手机号',
    'login-01cvlunhVBD1': '手机号不存在',
    'login-6wVmDbNKywYx': '请输入手机号码',
    'login-kpgmXXIrkEcQ': '验证码',
    'login-DOTA7ZPhk6Py': '请输入验证码!',
    'login-FM0dGiQkRHbx': '请输入验证码',
    'login-gx6sgjN5IQJV': '秒后重试',
    'login-g9yKfzfuI5S6': '获取验证码',
    'login-SHZccGvVvSmq': '下一步',
    'login-dn47s536MNGk': '完成',
    'login-RL0TxwkgnenY': '已经有账号？',
    'login-DMSwt0BEyQf5': '登录',
    'login-110tqJRD0xS2': '重置密码成功',
    'login-32PAhlpnefft': '返回登陆',

    // Front/src/pages/login/perfect/index.js
    'login-BpoXzEUQoljT': '操作完成',
    'login-SwPKZ4apmKP8': '注册成功',
    'login-HaCpt1PwDWvR': '前往产品中心',
    'login-CKnV0n6V1Ehb': '完善信息',
    'login-7XN2zvFqq3ZB': '邮箱',
    'login-LO3jpTzDT2bZ': '邮箱必填',
    'login-XmEDMPQ8rEm1': '邮箱已存在',
    'login-Y3V9cLjiVFwH': '请输入正确邮箱',
    'login-hhI3zTDlPPu6': '请输入邮箱',
    'login-njmMA8phBi26': '工作职位',
    'login-ZNIOMoKCk1U9': '工作职位必填',
    'login-Awc3EZ5pwOgG': '仅支持英文、中文最大支持32个字符',
    'login-1OjmhZG011eC': '请输入工作职位',
    'login-F5OfrImVzRtY': '设置登录密码',
    'login-H9eU6sNjH9GN': '密码必填',
    'login-WI9Zj57LURrn': '密码必须包含大写字母、小写字母、数字和特殊字符',
    'login-Dgyd8AIGxnnS': '必须为6-32位的数字字母或特殊字符',
    'login-xsc42wpak77M': '请输入登录密码',
    'login-xmxoyO45qhqV': '完成',
    'login-rvo1UBmQ4Qw6': '已经有账号？',
    'login-EFxEPeeEa8xZ': '登录',

    // Front/src/pages/login/signUp/index.js
    'login-JtdGLSznyW4f': '验证码错误，请重新输入',
    'login-QoqUUUjRpHYw': '验证失败,请重试',
    'login-WLOl2iAPlkAs': '发送失败，请重试',
    'login-FgVKTTk4Omy1': '发送成功',
    'login-2vLehr1tQ3Os': '注册账号',
    'login-g7MT294sHJVE': '姓名',
    'login-OHN4SnJl2FIH': '请输入姓名',
    'login-gTFuUFTtwvCc': '最多输入32个字符',
    'login-SP0lERYJSbU0': '请输入姓名',
    'login-nAK3RH914q2j': '手机号码',
    'login-ktFU2UNFOBO2': '手机号必填',
    'login-j1ijqo89vW3J': '请输入正确的手机号',
    'login-hYspHq5ezsvc': '手机号已存在',
    'login-WBTzLYkYOOWr': '请输入手机号码',
    'login-yhikztyODHcA': '验证码',
    'login-q5648Bnpir7r': '请输入验证码!',
    'login-fasiHGSl7uHU': '请输入验证码',
    'login-ze69IstaA1ud': '秒后重试',
    'login-fjEKhQEb9AiJ': '获取验证码',
    'login-lVNOSOFV3Nxx': '请阅读并勾选接受用户协议和隐私政策',
    'login-9b2iaw94f7MU': '我已阅读并接受',
    'login-T7fzGb4WOfCC': '用户协议',
    'login-WooDZRGFS9Cv': '和',
    'login-jQIyc7C1bFhX': '隐私政策',
    'login-eTQC7MymS6uz': '下一步',
    'login-tOLqdmtBE5Mw': '已经有账号？',
    'login-EzWlL36bleGp': '登录',

    // Front/src/pages/login/validate/index.js
    'login-nbx2J6BqraJ1': '登录成功',
    'login-7c6nQ3OI7yKV': '已完成验证',
    'login-hSEp9aVxxsY3': '进入产品',

    // Front/src/pages/login/success/index.jsx
    'login-dIXAtjd9iawP': 'props中systemInfo为undefined时重新请求系统信息接口',
    'login-78dqg8TAQqBP': '登录成功',
    'login-cdBWxBBY3S88': '登录失败',
    'login-7o2PLDASJdo9': '稍后将跳转到首页',
    'login-AXUkRZl764ij': '请联系管理员处理',

    // Front/src/pages/login/error/index.tsx
    'login-4OAzddiikokG': '账号锁定',
    'login-NTZ2INPgjn6T': '该账号密码已过期，请联系管理员',
    'login-ADeYMrVRXIcT': '账号密码错误累计超过 {failedCount} 次，',
    'login-blnJosOYMkJU': '已锁定该账号，请联系管理员解锁账号',
    'login-zwQo6vfaMzR8': '请 {lockedUntil} 后重试',
    'login-4mDVU6iXWKH2': '忘记密码',
    'login-QjPqHn8wzUdq': '返回登录',

    // Front/src/pages/login/callback/index.js
    'login-eQpIXF2EKybU': '获取上次登录的部门ID',
    'login-d3ffPdc9xY0S': '上次登录部门:',
    'login-xF7WbKkU4Mr7': '登录成功',

    // Front/src/pages/login/receive/index.js
    'login-pDPCqRAI02nx': '登录成功',

    // Front/src/pages/login/ssoLogin/index.js
    'login-NwkRWL1sCqdk': '获取单点登录地址失败！，请重试！',

    // Front/src/pages/login/testCallback/index.tsx
    'login-g0CrYZIAScDP': '定义接口类型',
    'login-RdoyVtldOXnu': '提取常量',
    'login-UR0SrB97Kvv5': '请输入userId',
    'login-UvX5s1W2KXYJ': '请输入deptId',
    'login-UdQs4eRAqQ4J': '请输入defaultProjectId',
    'login-VZNFTmHKECbq': '请输入defaultCompanyId',
    'login-KdnUSWBGmtww': '请输入token',
    'login-Hy1w9MKRawCp': '处理页面跳转逻辑',
    'login-PcDgZXe5sFTd': '获取系统配置',
    'login-miOEB0B6Iz9O': '设置本地存储',
    'login-0lZNouE5cYzO': '上次登录部门:',
    'login-m7T9rHhlnnck': '命中缓存',
    'login-q6heShCLLtX0': '未命中缓存',
    'login-Ot5vEaasGFFc': '登录成功',
    'login-fCAG5TCMdzS2': '登录失败，请重试',
    'login-4a49CtvSKxE4': '保存成功',
    'login-u0hEftN7eQiB': '保存失败，请重试',
    'login-01cvlunhVBD1': '模拟sso登录',
    'login-6wVmDbNKywYx': '保存当前页面数据'
  },
  en: {
    // Front/src/pages/login/Login.jsx
    'login-WHFAXCIKmdWp': 'Login successful',
    'login-eQYOd1wKX90O': 'Verification code expired, please re-enter',
    'login-ChfAVnS6ZI4P': 'Verification code error, please re-enter',
    'login-irFcQ8TzlM7I': 'Login to Datatist Predictive Marketing Cloud',
    'login-ptdKTgqpOMyg': 'Mobile/Email/Employee ID',
    'login-CijH0QCNEkLA': 'Login mobile/email/employee ID!',
    'login-axF4mdh3uvX7': 'Please enter correct mobile number or email or employee ID',
    'login-0UYMlZQSfE1e': 'Username',
    'login-wBuk1F1LxPhJ': 'Password',

    // Front/src/pages/login/LoginYJ.tsx
    'login-nbx2J6BqraJ1': 'Welcome to Customer Operations Automation Platform',
    'login-7c6nQ3OI7yKV': 'Login mobile/email/employee ID!',
    'login-hSEp9aVxxsY3': 'Please enter correct mobile number or email or employee ID',
    'login-dIXAtjd9iawP': 'Username',
    'login-78dqg8TAQqBP': 'Please enter password',
    'login-cdBWxBBY3S88': 'Password',
    'login-7o2PLDASJdo9': 'Please enter verification code',
    'login-AXUkRZl764ij': 'Verification Code',
    'login-4OAzddiikokG': 'Login',

    // Front/src/pages/login/LoginSW.jsx
    'login-NTZ2INPgjn6T': 'Welcome to Customer Operations Automation Platform',
    'login-ADeYMrVRXIcT': 'Employee ID format is incorrect',
    'login-blnJosOYMkJU': 'Employee ID',
    'login-zwQo6vfaMzR8': 'Please enter password',
    'login-4mDVU6iXWKH2': 'Password',
    'login-QjPqHn8wzUdq': 'Please enter verification code',
    'login-eQpIXF2EKybU': 'Verification Code',
    'login-d3ffPdc9xY0S': 'Login',

    // Front/src/pages/login/bind/index.js
    'login-xF7WbKkU4Mr7': 'Please bind enterprise email',
    'login-pDPCqRAI02nx': 'Enterprise Email',
    'login-NwkRWL1sCqdk': 'Please enter enterprise email',
    'login-g0CrYZIAScDP': 'Send verification link',
    'login-RdoyVtldOXnu': 'Link sent to email {email}',
    'login-UR0SrB97Kvv5': 'Please click the link in the email to complete verification',
    'login-UvX5s1W2KXYJ': 'Did not receive the link?',
    'login-UdQs4eRAqQ4J': 'Resend',

    // Front/src/pages/login/forgetPassword/index.js
    'login-VZNFTmHKECbq': 'Verification failed, please try again',
    'login-KdnUSWBGmtww': 'Send failed, please try again',
    'login-Hy1w9MKRawCp': 'Send successful',
    'login-PcDgZXe5sFTd': 'Forgot Password',
    'login-miOEB0B6Iz9O': 'Set new password',
    'login-0lZNouE5cYzO': 'Password is required',
    'login-m7T9rHhlnnck': 'Password must contain uppercase letters, lowercase letters, numbers and special characters',
    'login-q6heShCLLtX0': 'Must be 6-32 digits, letters or special characters',
    'login-Ot5vEaasGFFc': 'Please enter new password',
    'login-fCAG5TCMdzS2': 'Mobile Number',
    'login-4a49CtvSKxE4': 'Mobile number is required',
    'login-u0hEftN7eQiB': 'Please enter correct mobile number',
    'login-01cvlunhVBD1': 'Mobile number does not exist',
    'login-6wVmDbNKywYx': 'Please enter mobile number',
    'login-kpgmXXIrkEcQ': 'Verification Code',
    'login-WHFAXCIKmdWp': 'Please enter verification code!',
    'login-eQYOd1wKX90O': 'Please enter verification code',
    'login-ChfAVnS6ZI4P': 'seconds to retry',
    'login-irFcQ8TzlM7I': 'Get verification code',
    'login-ptdKTgqpOMyg': 'Next',
    'login-CijH0QCNEkLA': 'Complete',
    'login-axF4mdh3uvX7': 'Already have an account?',
    'login-0UYMlZQSfE1e': 'Login',
    'login-wBuk1F1LxPhJ': 'Password reset successful',
    'login-nbx2J6BqraJ1': 'Back to login',

    // Front/src/pages/login/perfect/index.js
    'login-7c6nQ3OI7yKV': 'Operation completed',
    'login-hSEp9aVxxsY3': 'Registration successful',
    'login-dIXAtjd9iawP': 'Go to product center',
    'login-78dqg8TAQqBP': 'Complete information',
    'login-cdBWxBBY3S88': 'Email',
    'login-7o2PLDASJdo9': 'Email is required',
    'login-AXUkRZl764ij': 'Email already exists',
    'login-4OAzddiikokG': 'Please enter correct email',
    'login-NTZ2INPgjn6T': 'Please enter email',
    'login-ADeYMrVRXIcT': 'Job Position',
    'login-blnJosOYMkJU': 'Job position is required',
    'login-zwQo6vfaMzR8': 'Only supports English and Chinese, maximum 32 characters',
    'login-4mDVU6iXWKH2': 'Please enter job position',
    'login-QjPqHn8wzUdq': 'Set login password',
    'login-eQpIXF2EKybU': 'Password is required',
    'login-d3ffPdc9xY0S': 'Password must contain uppercase letters, lowercase letters, numbers and special characters',
    'login-xF7WbKkU4Mr7': 'Must be 6-32 digits, letters or special characters',
    'login-pDPCqRAI02nx': 'Please enter login password',
    'login-NwkRWL1sCqdk': 'Complete',
    'login-g0CrYZIAScDP': 'Already have an account?',
    'login-RdoyVtldOXnu': 'Login',

    // Front/src/pages/login/signUp/index.js
    'login-UR0SrB97Kvv5': 'Verification code error, please re-enter',
    'login-UvX5s1W2KXYJ': 'Verification failed, please try again',
    'login-UdQs4eRAqQ4J': 'Send failed, please try again',
    'login-VZNFTmHKECbq': 'Send successful',
    'login-KdnUSWBGmtww': 'Register Account',
    'login-Hy1w9MKRawCp': 'Name',
    'login-PcDgZXe5sFTd': 'Please enter name',
    'login-miOEB0B6Iz9O': 'Maximum 32 characters',
    'login-0lZNouE5cYzO': 'Please enter name',
    'login-m7T9rHhlnnck': 'Mobile Number',
    'login-q6heShCLLtX0': 'Mobile number is required',
    'login-Ot5vEaasGFFc': 'Please enter correct mobile number',
    'login-fCAG5TCMdzS2': 'Mobile number already exists',
    'login-4a49CtvSKxE4': 'Please enter mobile number',
    'login-u0hEftN7eQiB': 'Verification Code',
    'login-01cvlunhVBD1': 'Please enter verification code!',
    'login-6wVmDbNKywYx': 'Please enter verification code',
    'login-kpgmXXIrkEcQ': 'seconds to retry',
    'login-WHFAXCIKmdWp': 'Get verification code',
    'login-eQYOd1wKX90O': 'Please read and check to accept user agreement and privacy policy',
    'login-ChfAVnS6ZI4P': 'I have read and accept',
    'login-irFcQ8TzlM7I': 'User Agreement',
    'login-ptdKTgqpOMyg': 'and',
    'login-CijH0QCNEkLA': 'Privacy Policy',
    'login-axF4mdh3uvX7': 'Next',
    'login-0UYMlZQSfE1e': 'Already have an account?',
    'login-wBuk1F1LxPhJ': 'Login',

    // Front/src/pages/login/validate/index.js
    'login-nbx2J6BqraJ1': 'Login successful',
    'login-7c6nQ3OI7yKV': 'Verification completed',
    'login-hSEp9aVxxsY3': 'Enter product',

    // Front/src/pages/login/success/index.jsx
    'login-dIXAtjd9iawP': 'Re-request system info when systemInfo in props is undefined',
    'login-78dqg8TAQqBP': 'Login successful',
    'login-cdBWxBBY3S88': 'Login failed',
    'login-7o2PLDASJdo9': 'Will redirect to homepage shortly',
    'login-AXUkRZl764ij': 'Please contact administrator',

    // Front/src/pages/login/error/index.tsx
    'login-4OAzddiikokG': 'Account Locked',
    'login-NTZ2INPgjn6T': 'This account password has expired, please contact administrator',
    'login-ADeYMrVRXIcT': 'Account password error accumulated more than {failedCount} times,',
    'login-blnJosOYMkJU': 'Account has been locked, please contact administrator to unlock',
    'login-zwQo6vfaMzR8': 'Please try again after {lockedUntil}',
    'login-4mDVU6iXWKH2': 'Forgot Password',
    'login-QjPqHn8wzUdq': 'Back to Login',

    // Front/src/pages/login/callback/index.js
    'login-eQpIXF2EKybU': 'Get last login department ID',
    'login-d3ffPdc9xY0S': 'Last login department:',
    'login-xF7WbKkU4Mr7': 'Login successful',

    // Front/src/pages/login/receive/index.js
    'login-pDPCqRAI02nx': 'Login successful',

    // Front/src/pages/login/ssoLogin/index.js
    'login-NwkRWL1sCqdk': 'Failed to get SSO login address! Please try again!',

    // Front/src/pages/login/testCallback/index.tsx
    'login-g0CrYZIAScDP': 'Define interface types',
    'login-RdoyVtldOXnu': 'Extract constants',
    'login-UR0SrB97Kvv5': 'Please enter userId',
    'login-UvX5s1W2KXYJ': 'Please enter deptId',
    'login-UdQs4eRAqQ4J': 'Please enter defaultProjectId',
    'login-VZNFTmHKECbq': 'Please enter defaultCompanyId',
    'login-KdnUSWBGmtww': 'Please enter token',
    'login-Hy1w9MKRawCp': 'Handle page redirect logic',
    'login-PcDgZXe5sFTd': 'Get system configuration',
    'login-miOEB0B6Iz9O': 'Set local storage',
    'login-0lZNouE5cYzO': 'Last login department:',
    'login-m7T9rHhlnnck': 'Cache hit',
    'login-q6heShCLLtX0': 'Cache miss',
    'login-Ot5vEaasGFFc': 'Login successful',
    'login-fCAG5TCMdzS2': 'Login failed, please try again',
    'login-4a49CtvSKxE4': 'Save successful',
    'login-u0hEftN7eQiB': 'Save failed, please try again',
    'login-01cvlunhVBD1': 'Simulate SSO login',
    'login-6wVmDbNKywYx': 'Save current page data'
  }
};
