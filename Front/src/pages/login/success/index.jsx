import React, { useState, useEffect } from 'react';
import { Spin, Result } from 'antd';
import querystring from 'querystring';
import UserService from 'service/UserService';
import SystemInfoService from 'service/systemInfoService';
import _organizationManageService from 'service/organizationmanageService';
import _ from 'lodash';
import { useSelector, useDispatch } from 'react-redux';
import { t } from '@/utils/translation';

const userService = new UserService();
const style = {
  position: 'absolute',
  left: '50%',
  top: '50%',
  transform: 'translate(-50%,-50%)'
};

export default function Sucess(props) {
  const [isLoginSuccess, setIsLoginSuccess] = useState(null);
  const [loading, setLoading] = useState(true);
  const dispatch = useDispatch();
  const systemInfo = useSelector((state) => state.systemInfo);
  props = { ...props, systemInfo };

  useEffect(() => {
    const params = querystring.parse(window.location.search.substr(1));

    const init = async () => {
      try {
        const ssoLogin = await userService.ssoLogin({ ...params });
        setIsLoginSuccess(ssoLogin);
        setLoading(true);
        // props中systemInfo为undefined时重新请求系统信息接口
        const { id } = await userService.getCurrentUser();
        if (props.systemInfo['login.redirect'] !== 'OFF') {
          let systemInfo;
          if (!props.systemInfo['login.redirect']) {
            const list = await SystemInfoService.getSystemConfig([]);
            systemInfo = list.reduce((obj, n) => {
              obj[n.name] = n.value;
              return obj;
            }, {});
            dispatch({ type: 'systemInfo', systemInfo });
          }
          let { productId, projectId } = props.systemInfo['login.redirect']
            ? JSON.parse(props.systemInfo['login.redirect'])
            : JSON.parse(systemInfo['login.redirect']);
          const data = await _organizationManageService.findAllByUserId();
          let currentOrganization = localStorage.getItem('organizationId');
          if (_.isEmpty(currentOrganization) || !_.find(data, (item) => `${item.id}` === currentOrganization)) {
            currentOrganization = data[0]?.id || '';
            localStorage.setItem('organizationId', currentOrganization);
          }
          let productParam = 0;
          if (projectId === 'lastProject') {
            if (`${productId}` === localStorage.getItem('productId')) {
              projectId = localStorage.getItem('projectId');
              productParam = localStorage.getItem('productId');
            } else {
              productParam = productId;
            }
          } else {
            productParam = productId;
          }
          const reData = await _organizationManageService.getBizProductProject({
            testStatus: false,
            companyId: localStorage.getItem('organizationId'),
            bizProductId: productParam
            // projectId
          });

          const { defaultPageAuthMenu, projectList } = reData;
          if (projectId === 'lastProject' && `${productId}` !== localStorage.getItem('productId')) {
            projectList ? (projectId = projectList[0].id) : (projectId = '');
          }
          const pageData = await _organizationManageService.getDefaultPageAuthMenu({
            bizProductId: productParam,
            loginId: id,
            myProjectId: projectId
          });
          setTimeout(() => {
            if (!_.find(projectList, (item) => `${item.id}` === `${projectId}`)) {
              props.history.push('/aimarketer/usercenter/productcenter');
            } else {
              localStorage.setItem('productId', productId);
              localStorage.setItem('projectId', projectId);
              props.history.push(defaultPageAuthMenu.route);
              // props.history.push('/aimarketer/home');
              props.history.push(pageData.route);
            }
          }, 1000);
        } else {
          setTimeout(() => {
            props.history.push('/aimarketer/usercenter/productcenter');
          }, 1000);
        }
        // setTimeout(() => {
        //   props.history.push('/aimarketer/usercenter/productcenter');
        // }, 1000);
        setLoading(false);
      } catch (error) {
        setLoading(false);
      }
    };
    init();
  }, []);
  return (
    <div>
      {loading ? (
        <div className="spinning" style={style}>
          <Spin />
        </div>
      ) : (
        <Result
          status={isLoginSuccess ? 'success' : 'error'}
          title={isLoginSuccess ? t('login-0QHppxXY5zXf') : t('login-VW9SpHq41qkk')}
          subTitle={isLoginSuccess ? t('login-D7loeqrz1b3r') : t('login-YcG1d1SWoGXo')}
        />
      )}
    </div>
  );
}
