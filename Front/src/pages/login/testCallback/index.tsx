import { useStore } from '@/store/globalStore';
import { Button, Form, Input, message } from 'antd';
import React, { useEffect } from 'react';
import SystemInfoService from 'service/systemInfoService';
import { setDeptId } from 'utils/commonUtils';
import { t } from '@/utils/translation';
// 定义接口类型
interface LoginFormValues {
  userId: number;
  deptId: number;
  token: string;
  defaultCompanyId: string;
  defaultProjectId: string;
  redirectFlag?: 'ON' | 'OFF';
}

// 提取常量
const DEFAULT_FORM_VALUES: LoginFormValues[] = [
  {
    userId: 249,
    deptId: 975,
    token:
      'ANALYZER.Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyb2xlIjoiTk8tUk9MRSIsInVzZXJJZCI6IjI0OSIsInRlcm1pbmFsSWQiOiJ0ZXJtaW5hbElkIiwic3ViIjoiIiwiaXNzIjoiMDk4ZjZiY2Q0NjIxZDM3M2NhZGU0ZTgzMjYyN2I0ZjYiLCJpYXQiOjE3MzM5OTY5NzUsImF1ZCI6InJlc3RhcGl1c2VyLWR0IiwiZXhwIjoxNzM0MDA0MTc1LCJuYmYiOjE3MzM5OTY5NzV9.d8e8dVJ1x-B0b8G8O3hk5oE957E1pBd3LaO8AoPjdfo',
    defaultCompanyId: '1',
    defaultProjectId: 'qvAD1jk8q0hA0Oxm'
  },
  {
    userId: 2,
    deptId: 172,
    token:
      'ANALYZER.Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJyb2xlIjoiTk8tUk9MRSIsInVzZXJJZCI6IjIiLCJ0ZXJtaW5hbElkIjoidGVybWluYWxJZCIsInN1YiI6IiIsImlzcyI6IjA5OGY2YmNkNDYyMWQzNzNjYWRlNGU4MzI2MjdiNGY2IiwiaWF0IjoxNzMzOTk2OTk3LCJhdWQiOiJyZXN0YXBpdXNlci1kdCIsImV4cCI6MTczNDAwNDE5NywibmJmIjoxNzMzOTk2OTk3fQ.SqBUlkEhaB8lI3PdQn9ayZnkBHnEHHmKNDYgpltA3sU',
    defaultCompanyId: '1',
    defaultProjectId: 'qvAD1jk8q0hA0Oxm'
  }
];

const formItems = [
  { name: 'userId', label: 'userId', rules: [{ required: true, message: t('login-ylnaklfe6UqF') }] },
  { name: 'deptId', label: 'deptId 部门id', rules: [{ required: true, message: t('login-gF77XCoYSn6F') }] },
  {
    name: 'defaultProjectId',
    label: 'defaultProjectId 项目id',
    rules: [{ required: true, message: t('login-YoK4eVuHbhfE') }]
  },
  {
    name: 'defaultCompanyId',
    label: 'defaultCompanyId 组织id',
    rules: [{ required: true, message: t('login-plCNNevbhA2M') }]
  },
  { name: 'token', label: 'token | aim_authorization', rules: [{ required: true, message: t('login-9qfFA8GwtDx4') }] }
];

const STORAGE_KEY = 'ssoTestInfo';
const ROUTES = {
  HOME: '/aimarketer/home',
  PRODUCT_CENTER: '/aimarketer/usercenter/productcenter'
} as const;

// 处理页面跳转逻辑
const handleRedirect = (path: string) => {
  const targetWindow = window.top !== window.self ? window.top : window;
  targetWindow!.location.href = path;
};

const TestCallback: React.FC = () => {
  const [form1] = Form.useForm<LoginFormValues>();
  const [form2] = Form.useForm<LoginFormValues>();

  useEffect(() => {
    const initializeForms = () => {
      const savedData = localStorage.getItem(STORAGE_KEY);
      const formData = savedData ? JSON.parse(savedData) : DEFAULT_FORM_VALUES;

      form1.setFieldsValue(formData[0]);
      form2.setFieldsValue(formData[1]);
    };

    initializeForms();
  }, []);

  const handleLogin = async (values: LoginFormValues) => {
    try {
      const { redirectFlag, defaultCompanyId, defaultProjectId, deptId, token, userId } = values;
      // 获取系统配置
      const systemConfigList = await SystemInfoService.getSystemConfig([]);
      const systemInfo = systemConfigList.reduce((acc: Record<string, string>, curr: any) => {
        acc[curr.name] = curr.value;
        return acc;
      }, {});

      // 设置本地存储
      localStorage.setItem('aim_authorization', token);
      localStorage.setItem('userId', userId.toString());

      // 处理部门ID
      const lastLoginDeptId = userId ? useStore.getState().lastLoginDeptMap[userId] : null;
      console.warn('🚀 ~ 上次登录部门:', lastLoginDeptId, `${lastLoginDeptId ? '命中缓存' : '未命中缓存'}`);
      setDeptId(lastLoginDeptId || deptId, userId.toString());
      // 处理重定向逻辑
      if (redirectFlag === 'ON' && defaultCompanyId && deptId && defaultProjectId) {
        localStorage.setItem('organizationId', defaultCompanyId);
        localStorage.setItem('projectId', defaultProjectId);
        const loginRedirect = systemInfo['login.redirect'];
        if (loginRedirect && loginRedirect !== 'OFF') {
          const { projectId } = JSON.parse(loginRedirect);
          handleRedirect(defaultProjectId === projectId ? ROUTES.HOME : ROUTES.PRODUCT_CENTER);
        } else {
          handleRedirect(ROUTES.PRODUCT_CENTER);
        }
      } else {
        handleRedirect(ROUTES.PRODUCT_CENTER);
      }

      message.success(t('login-7Ah9wAOBIaHA'));
    } catch (error) {
      console.error('登录失败:', error);
      message.error(t('login-z3p5GEljctkz'));
    }
  };

  const handleSaveData = () => {
    try {
      const formData = [form1.getFieldsValue(), form2.getFieldsValue()];
      localStorage.setItem(STORAGE_KEY, JSON.stringify(formData));
      message.success(t('login-oPEl1MtGv7BM'));
    } catch (error) {
      console.error('保存失败:', error);
      message.error(t('login-JO2jY3Dl1jMX'));
    }
  };

  return (
    <div className="flex gap-200">
      {[form1, form2].map((form, index) => (
        <div key={index} className={index === 0 ? 'w-300 h-300' : ''}>
          <Form form={form}>
            {formItems.map((item) => (
              <Form.Item key={item.name} {...item}>
                {item.name === 'token' ? <Input.TextArea /> : <Input />}
              </Form.Item>
            ))}
            <Form.Item>
              <Button type="primary" onClick={() => handleLogin({ ...form.getFieldsValue(), redirectFlag: 'ON' })}>
                {t('login-xbFqZDIqWmM4')}
              </Button>
            </Form.Item>
          </Form>
          <Button onClick={() => form.setFieldsValue(window.getSsoInfo())}>把之前登录的数据 赋值给这个表单</Button>
        </div>
      ))}

      <div>
        <Button type="primary" onClick={handleSaveData}>
          {t('login-IXvpTOz5NoNx')}
        </Button>
      </div>
    </div>
  );
};

export default TestCallback;
