import { Form } from '@ant-design/compatible';
import '@ant-design/compatible/assets/index.css';
import { CloseOutlined, KeyOutlined, LockOutlined, UserOutlined } from '@ant-design/icons';
import { Button, Divider, Input, Space, Spin, message } from 'antd';
import loginSideImg from 'assets/images/loginSideImg.png';
import TextLogo from 'assets/images/<EMAIL>';
import SystemInfo from 'components/bussinesscoms/systeminfocom/index';
import dayjs from 'dayjs';
import _ from 'lodash';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import SsoService from 'service/SsoService';
import UserService from 'service/UserService';
import _organizationManageService from 'service/organizationmanageService';
import SystemInfoService from 'service/systemInfoService';
import { transformUrl } from 'utils/universal';
import { t } from '@/utils/translation';
import LoginSW from './LoginSW';
import LoginYJ from './LoginYJ';

import './Login.scss';

const bc = new BroadcastChannel('refresh');

const querystring = require('querystring');

const mapState = (store) => {
  return { systemInfo: store.systemInfo };
};

const renderLoginComponent = (Component, props) => (
  <div className="loginWrap" style={{ padding: 0 }}>
    <Component {...props} />
  </div>
);

const isLoginPath = (path) => {
  return window.location.pathname.endsWith(path);
};

class Login extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      codeImg: '',
      codeStatus: false,
      forgetStatus: false,
      codeKey: '',
      ssoList: []
    };
    this.userService = new UserService();
    this.handleSubmit = this.handleSubmit.bind(this);
    this.getCodeImg = this.getCodeImg.bind(this);
    this.getForgetSetting = this.getForgetSetting.bind(this);
  }

  async componentDidMount() {
    this.getForgetSetting();
    this.getCodeImg();
    // 如果有部门信息的缓存 就清除掉
    if (window.getDeptId()) localStorage.removeItem('deptId');
    // if (localStorage.getItem('deptShowType')) localStorage.removeItem('deptShowType');
    this.props.dispatch({ type: 'logout' });
    this.props.dispatch({ type: 'initLoading', initLoading: true });
    const ssoList = await SsoService.getSsoInfo();
    this.setState({
      ssoList
    });
    localStorage.getItem('env') !== 'SW' &&
      !isLoginPath('login-yj') &&
      window.addEventListener('keypress', this.handleKyePress);
    // 判断url是否有自动登录的要求，如果有，取出相应的用户名密码自动登录
    const params = querystring.parse(window.location.search.substr(1));

    if (!_.isEmpty(_.trim(params.username)) && !_.isEmpty(_.trim(params.password))) {
      this.setState({ loading: true });
      try {
        await this.UserService.login({
          username: params.username,
          password: params.password
          // rememberMe: false
        });
        this.setState({ loading: false });
        message.success(t('login-M6E6RkiFBrcP'), 1);
        setTimeout(() => {
          this.props.history.push('/aimarketer/home');
        }, 1000);
      } catch (error) {
        this.setState({ loading: false });
      }
    }
    this.props.dispatch({ type: 'MENUINIT', isInit: false });
  }

  componentWillUnmount() {
    window.removeEventListener('keypress', this.handleKyePress);
  }

  async getCodeImg() {
    try {
      const res = await this.userService.getCodeBase64();

      this.setState({
        codeImg: `data:image/gif;base64,${res.imgCode}`,
        codeStatus: res.captchaEnabled,
        codeKey: res.codeKey
      });
    } catch (e) {
      console.error(e);
    }
  }

  async getForgetSetting() {
    try {
      const res = await this.userService.getByTypeAndCode({
        type: 'ACCOUNT_VERIFY',
        code: 'login.forgot.password'
      });

      this.setState({
        forgetStatus: res[0].value === 'ON'
      });
    } catch (err) {
      console.error(err);
    }
  }

  handleKyePress = (e) => {
    if (e.keyCode === 13) {
      this.handleSubmit();
    }
  };

  handleSubmit(childrenValue) {
    this.props.form.validateFields(async (err, values) => {
      if (!err) {
        this.setState({ loading: true });
        const loginValue = childrenValue && childrenValue.target ? values : { ...values, ...childrenValue };
        localStorage.setItem('errorUser', loginValue?.username);
        if (this.state.codeStatus) {
          loginValue.codeKey = this.state.codeKey;
        }
        try {
          const lastInfo = await this.userService.getLastRecord(loginValue?.username);

          const res = await this.userService.login(loginValue);
          if (res.code === 4006 || res.code === 4007) {
            res.code === 4006 && this.getCodeImg();
            message.error(res.code === 4006 ? t('login-AUwMFo0coAFY') : t('login-AY8EFjPhm4bk'));
            this.setState({ loading: false });
            return;
          }
          localStorage.setItem('userId', res.userId);
          if (res.modifyPassword) {
            localStorage.setItem('modifyPassword', res.modifyPassword);
          } else {
            localStorage.removeItem('modifyPassword');
          }
          this.setState({ loading: false });
          const key = `login-success${new Date().getTime()}`;
          const tip = lastInfo ? (
            <span>
              {t('login-M6E6RkiFBrcP')}，上次登录时间：
              {dayjs(lastInfo.time).format('YYYY-MM-DD HH:mm:ss')} {lastInfo.os} {lastInfo.browser}
              <CloseOutlined
                style={{ cursor: 'pointer', color: '#000', marginLeft: 8 }}
                onClick={() => message.destroy(key)}
              />
            </span>
          ) : (
            t('login-M6E6RkiFBrcP')
          );
          message.success({
            content: tip,
            duration: 5,
            key
          });
          bc.postMessage(new Date().getTime());
          // props中systemInfo为undefined时重新请求系统信息接口
          const { id } = await this.userService.getCurrentUser();
          if (this.props.systemInfo['login.redirect'] !== 'OFF') {
            let systemInfo;
            if (!this.props.systemInfo['login.redirect']) {
              const { dispatch } = this.props;
              const list = await SystemInfoService.getSystemConfig([]);
              systemInfo = list.reduce((obj, n) => {
                obj[n.name] = n.value;
                return obj;
              }, {});
              dispatch({ type: 'systemInfo', systemInfo });
            }
            let { productId, projectId } = this.props.systemInfo['login.redirect']
              ? JSON.parse(this.props.systemInfo['login.redirect'])
              : JSON.parse(systemInfo['login.redirect']);
            const data = await _organizationManageService.findAllByUserId();
            let currentOrganization = localStorage.getItem('organizationId');
            if (_.isEmpty(currentOrganization) || !_.find(data, (item) => `${item.id}` === currentOrganization)) {
              currentOrganization = data[0]?.id || '';
              localStorage.setItem('organizationId', currentOrganization);
            }
            let productParam = 0;
            if (projectId === 'lastProject') {
              if (`${productId}` === localStorage.getItem('productId')) {
                projectId = localStorage.getItem('projectId');
                productParam = localStorage.getItem('productId');
              } else {
                productParam = productId;
              }
            } else {
              productParam = productId;
            }
            const reData = await _organizationManageService.getBizProductProject({
              testStatus: false,
              companyId: localStorage.getItem('organizationId'),
              bizProductId: productParam
              // projectId
            });

            const { defaultPageAuthMenu, projectList } = reData;
            if (projectId === 'lastProject' && `${productId}` !== localStorage.getItem('productId')) {
              projectList ? (projectId = projectList[0].id) : (projectId = '');
            }
            const pageData = await _organizationManageService.getDefaultPageAuthMenu({
              bizProductId: productParam,
              loginId: id,
              myProjectId: projectId
            });
            setTimeout(() => {
              if (!_.find(projectList, (item) => `${item.id}` === `${projectId}`)) {
                this.props.history.push('/aimarketer/usercenter/productcenter');
              } else {
                localStorage.setItem('productId', productId);
                localStorage.setItem('projectId', projectId);
                this.props.history.push(defaultPageAuthMenu.route);
                // this.props.history.push('/aimarketer/home');
                this.props.history.push(pageData.route);
              }
            }, 1000);
          } else {
            setTimeout(() => {
              this.props.history.push('/aimarketer/usercenter/productcenter');
            }, 1000);
          }
          // setTimeout(() => {
          //   this.props.history.push('/aimarketer/usercenter/productcenter');
          // }, 1000);
        } catch (error) {
          console.error(error.message, 'error');
          this.setState({ loading: false });
        }
      }
    });
  }

  checkAccount = async (_, value, callback) => {
    const valueValid =
      /^[a-zA-Z0-9]{1,8}$/.test(value) ||
      /^(?:(?:\+|00)86)?1\d{10}$/.test(value) ||
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
        value
      );
    if (valueValid) {
      callback();
    } else {
      callback(true);
    }
  };

  render() {
    const { getFieldDecorator } = this.props.form;
    const { systemInfo } = this.props;
    const { ssoList } = this.state;
    const props = {
      handleSubmit: this.handleSubmit,
      loading: this.state.loading,
      codeImg: this.state.codeImg,
      codeStatus: this.state.codeStatus,
      codeKey: this.state.codeKey,
      getCodeImg: this.getCodeImg
    };

    if (isLoginPath('login-yj')) {
      return renderLoginComponent(LoginYJ, props);
    }

    if (localStorage.getItem('env') === 'SW') {
      return renderLoginComponent(LoginSW, props);
    }
    return (
      <Spin spinning={this.state.loading}>
        <div className="loginWrap">
          <div className="login">
            <div className="heade">
              <img src={transformUrl(systemInfo['page.light.logoUrl']) || TextLogo} alt="" />
            </div>
            <div className="main">
              <div className="mainLeft">
                <h3>{`${systemInfo['aimarketer.page.login.welcomeMsg'] || t('login-4x05CsRfr4XM')}`}</h3>
                <Form onSubmit={this.handleSubmit} hideRequiredMark className="login-form">
                  <Form.Item label={t('login-CjEqJ8mKhREM')}>
                    {getFieldDecorator('username', {
                      validateFirst: true,
                      validateTrigger: 'onBlur',
                      rules: [
                        {
                          required: true,
                          message: t('login-xoUAPNBDPjoG')
                        },
                        {
                          validator: this.checkAccount,
                          message: t('login-1XAsM3eIQAX2')
                        }
                      ]
                    })(
                      <Input
                        prefix={<UserOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                        placeholder={t('login-EfZjXSCHq8R2')}
                        autoComplete="off"
                      />
                    )}
                  </Form.Item>
                  <Form.Item label={t('login-6lWzw4Rm7HR2')}>
                    {getFieldDecorator('password', {
                      rules: [{ required: true, message: '请输入密码!' }]
                    })(
                      <Input
                        prefix={<LockOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                        type="password"
                        placeholder={t('login-6lWzw4Rm7HR2')}
                        autoComplete="off"
                      />
                    )}
                  </Form.Item>
                  {this.state.codeStatus && (
                    <Form.Item label="验证码">
                      {getFieldDecorator('code', {
                        rules: [{ required: true, message: '请输入验证码!' }]
                      })(
                        <Space className="codeSpace">
                          <Input
                            prefix={<KeyOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                            placeholder="验证码"
                            autoComplete="off"
                            className="!w-full"
                          />
                          <div onClick={() => this.getCodeImg()}>
                            <img
                              src={this.state.codeImg}
                              alt=""
                              className="w-[100px] cursor-pointer h-[46px] relative top-[2px]"
                            />
                          </div>
                        </Space>
                      )}
                    </Form.Item>
                  )}

                  <Form.Item>
                    {/* {getFieldDecorator('rememberMe', {
                      valuePropName: 'checked',
                      initialValue: true
                    })(<Checkbox>记住密码</Checkbox>)} */}
                    <br />
                    <Button type="primary" onClick={this.handleSubmit} className="login-form-button">
                      登录
                    </Button>
                  </Form.Item>
                </Form>
                {this.state.forgetStatus && (
                  <div className="reminder">
                    <div className="forgetPassword" onClick={() => this.props.history.push('/aimarketer/forget')}>
                      忘记密码？
                    </div>
                  </div>
                )}

                {ssoList.length ? (
                  <>
                    <Divider style={{ minWidth: 3, width: 340 }} />
                    <div className="otherLogin">
                      <span>使用SSO登录：</span>
                      {ssoList.map((n) => {
                        return (
                          <a style={{ marginRight: 5 }} key={n.ssoCode} href={n.requestUrl}>
                            {n.ssoDisplayName}
                          </a>
                        );
                      })}
                    </div>
                  </>
                ) : null}
              </div>
              <div className="mainRight">
                <img src={transformUrl(systemInfo['aimarketer.page.login.imageUrl']) || loginSideImg} alt="" />
              </div>
            </div>
            <SystemInfo />
          </div>
        </div>
      </Spin>
    );
  }
}

export default Form.create({ name: 'login' })(connect(mapState)(Login));
