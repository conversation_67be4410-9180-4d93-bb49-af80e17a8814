import { WarningFilled } from '@ant-design/icons';
import { Button, Space } from 'antd';
import SystemInfo from 'components/bussinesscoms/systeminfocom/index';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import UserService from 'service/UserService';
import { t } from '@/utils/translation';

const userService = new UserService();

interface UserInfo {
  createTime: number;
  updateTime: number;
  createUserId: number;
  updateUserId: number;
  id: number;
  account: string;
  userId: number;
  failedCount: number;
  lockedUntil: number;
  lockedStatus: number;
}

export default function ErrorPage() {
  const user = window.location.href.split('?')[1]!.split('&')[0]!.split('=')[1] || '';
  const isOverdue = window.location.href.includes('isOverdue');
  const [userInfo, setUserInfo] = useState<UserInfo | undefined>(undefined);
  useEffect(() => {
    const init = async () => {
      const userInfo = await userService.findByAccount(user);
      setUserInfo(userInfo);
    };
    !isOverdue && init();
  }, []);

  const toLogin = () => {
    window.location.href = '/aimarketer/login';
  };

  const goForget = () => {
    window.location.href = '/aimarketer/forget';
  };

  return (
    <div>
      <div
        style={{
          height: '100vh',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <div style={{ display: 'grid', textAlign: 'center' }}>
          <WarningFilled style={{ color: '#ffc53d', fontSize: 55, marginBottom: 24 }} />
          <span style={{ fontSize: 24, fontWeight: 'bold' }}>{t('login-zCgvpicnOXOQ')}</span>
          <div>{user}</div>
          {isOverdue ? (
            <div>{t('login-1iZphyAJVAmu')}</div>
          ) : (
            <div className="tip" style={{ color: 'rgba(0,0,0,.45)' }}>
              {t('login-THvcARfK820M', { failedCount: userInfo?.failedCount })}
              {userInfo && userInfo?.lockedStatus === 1
                ? t('login-z0mbbisrGucf')
                : t('login-EGS4SFfdgimY', { lockedUntil: dayjs(userInfo?.lockedUntil).format('YYYY-MM-DD HH:mm:ss') })}
            </div>
          )}
          <Space align="center" style={{ display: 'flex', justifyContent: 'center', marginTop: 24 }}>
            <Button
              hidden={(userInfo && userInfo?.lockedStatus === 1) || localStorage.getItem('env') === 'SW' || isOverdue}
              style={{ marginRight: 8, borderRadius: 6 }}
              onClick={goForget}
            >
              {t('login-upJxPvZMXUdQ')}
            </Button>
            <Button type="primary" style={{ borderRadius: 6 }} onClick={toLogin}>
              {t('login-AdYDpE3YWi6X')}
            </Button>
          </Space>
        </div>
      </div>
      <SystemInfo style={{ position: 'fixed', bottom: 5, left: 0, right: 0 }} />
    </div>
  );
}
