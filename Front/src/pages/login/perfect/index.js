import { ContactsOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import { Button, Divider, Form, Input, message } from 'antd';
import TextLogo from 'assets/images/<EMAIL>';
import SystemInfo from 'components/bussinesscoms/systeminfocom/index';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import AccountService from 'service/accountService';
import { transformUrl } from 'utils/universal';
import { t } from '@/utils/translation';

import UserServiceBefore from 'service/UserService';

import JSEncrypt from 'jsencrypt';

import './index.scss';

const UserService = new UserServiceBefore();

const encrypt = new JSEncrypt();
const publicKey =
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCg0gUguagtMMjJ1KD6fpPkWfofbncqpcdJeVBA764C1VjY3/ttzck6+hKsiAdboQJtugH/X4MjRM8EFe488jdPbh64DO/pmXObWj701lPLuHmZ+H9H1MTCiPRGH8UOdHde9r7ZVl1JsOYQHEf8nLQIkU2ifUhjOHFA+5ReH9gTEQIDAQAB';
encrypt.setPublicKey(publicKey);

const mapState = (store) => {
  return { systemInfo: store.systemInfo };
};

const BindComponent = (props) => {
  const [form] = Form.useForm();
  const {
    location: { state }
  } = props;
  const [loading, setLoading] = useState(false);

  const { systemInfo } = props;

  const [vaildData, setVaildData] = useState({});

  useEffect(() => {
    const init = async () => {
      const res = await UserService.getByTypeAndCode({
        type: 'PASSWORD_SAFETY',
        code: 'safety.verify'
      });
      setVaildData(res[0]);
    };
    init();
  }, []);

  const onFinish = async (value) => {
    // const data = { ...value, ...state };
    const encrypted = encrypt.encrypt(JSON.stringify(value.passwordToUse));
    try {
      setLoading(true);
      await AccountService.register({
        ...value,
        ...state?.data,
        verifyCode: state?.data?.code,
        scope: 'ANALYZER',
        passwordToUse: encrypted
      });
      setLoading(false);
      message.success(t('login-zZiKnF0vO9v9'));
      props.history.replace({
        pathname: '/aimarketer/usercenter/succeed',
        state: {
          title: t('login-nbQMBFPt3c2v'),
          backUrl: '/aimarketer/usercenter/productcenter',
          backButtonText: t('login-6TxpBWbw8Ixp'),
          exploreMore: true
        }
      });
    } catch (error) {
      setLoading(false);
    }
  };

  const handleValidator = async (rule, val) => {
    if (val) {
      const res = await AccountService.ensureAccount({ email: val.trim() });
      if (!res) {
        return Promise.reject(new Error(rule.message));
      }
    }

    return Promise.resolve();
  };

  return (
    <div className="perfectInfo">
      <div className="bindInfo">
        <div className="heade">
          <img src={transformUrl(systemInfo['page.light.logoUrl']) || TextLogo} alt="" />
        </div>
        <div className="main">
          <div className="emailTitle">{t('login-d5DXTQNq4G4Q')}</div>
          <Form className="formStyle" onFinish={onFinish} form={form} layout="vertical">
            <Form.Item
              name="email"
              label={t('login-aYa6telGOwxj')}
              validateTrigger="onBlur"
              rules={[
                { required: true, message: t('login-GADikqOMsWGP') },
                // { type: 'email' },
                { validator: handleValidator, message: t('login-VNbxXjCZVxPi') },
                {
                  pattern: /^([a-z0-9A-Z]+[-|.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\.)+[a-zA-Z]{2,}$/,
                  message: t('login-m89ru6OUaCCs')
                }
              ]}
            >
              <Input
                autoComplete="off"
                placeholder={t('login-Je53gvuT4rMJ')}
                prefix={<MailOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                className="inputEmail"
              />
            </Form.Item>
            <Form.Item
              name="position"
              validateTrigger="onBlur"
              label={t('login-UTGPT4bdyZR9')}
              rules={[
                { required: true, message: t('login-FxtcCVsSW6tU') },
                {
                  pattern: /^[\u4e00-\u9fffa-zA-Z]{0,32}$/,
                  message: t('login-wRGHMS9eO0EB')
                }
              ]}
            >
              <Input
                autoComplete="off"
                placeholder={t('login-qb8kuYNNqPiu')}
                prefix={<ContactsOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                className="inputEmail"
              />
            </Form.Item>
            <Form.Item
              name="passwordToUse"
              validateTrigger="onBlur"
              label={t('login-m5WypxCam4I4')}
              rules={[
                { required: true, message: t('login-Gt3f7Jdvwghp') },
                {
                  pattern:
                    vaildData?.value === 'ON'
                      ? new RegExp(
                          `^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*()\\-_=+{};:,<.>])(?!.*[\u4E00-\u9FA5])(?!.*\\s).{${vaildData.config.minLength},24}$`
                        )
                      : /^[0-9A-Za-z\\W_=?.。~!@#$%^&*()-=]{6,32}$/,
                  message: vaildData?.value === 'ON' ? t('login-rNGIuiWeglH6') : t('login-6Gm5QS7LCycx')
                }
              ]}
            >
              <Input.Password
                autoComplete="off"
                placeholder={t('login-QOnrg0jQQWAW')}
                prefix={<LockOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
                className="inputEmail"
              />
            </Form.Item>
            {/* <Form.Item> */}
            <Button type="primary" disabled={loading} className="login-form-button" htmlType="submit">
              {t('login-KuFAefU3y2E6')}
            </Button>
            {/* </Form.Item> */}
            <div className="desc">
              {t('login-BPyXg6OGEv5U')}
              <a onClick={() => props.history.push('/aimarketer/login')}>{t('login-oJsUiPtVSY5Z')}</a>
            </div>
          </Form>
        </div>
      </div>
      <Divider />
      <SystemInfo />
    </div>
  );
};
export default connect(mapState)(BindComponent);
