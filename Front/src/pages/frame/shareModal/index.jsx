import { useStore } from '@/store/share';
import { Avatar, Form, Modal, Select, Spin, Tooltip, message } from 'antd';
import React, { useEffect, useState } from 'react';

import { getDeptPath } from '@/pages/home/<USER>/dataPermissions/config';
import shareService from '@/service/shareService';
import { getCurrentPageRolesList } from '@/utils/universal';
import _ from 'lodash';
import './index.scss';
import { t } from '@/utils/translation';

const { Option } = Select;
const mapArr = ['aim_segment_edit', 'aim_campaignV2_edit'];

const ShareModal = () => {
  const [userList, setUserList] = useState([]);
  const [rolesList, setRolesList] = useState([]);
  const [cooperateList, setCooperateList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [displayName, setDisplayName] = useState('');

  const [param] = useState({
    page: 1,
    search: [],
    size: 9999,
    sorts: [{ propertyName: 'createTime', direction: 'desc' }]
  });
  // const [publicStatus, setPublicStatus] = useState(false);

  const { shareOpen, dispatchShare, shareInfo, reflash, cooperateOpen } = useStore();

  const [form] = Form.useForm();

  useEffect(() => {
    const init = async () => {
      try {
        const rolesRes = await shareService.getActionGroupByRole({
          id: Number(localStorage.getItem('roleId'))
        });

        let rolesResult = getCurrentPageRolesList(shareInfo.type, rolesRes) || [];

        rolesResult = _.map(rolesResult, (item) => {
          if (_.includes(mapArr, item.code)) {
            return {
              ...item,
              name: t('setting-i3OEEtTie0x0')
            };
          }
          return item;
        });
        // todo 本次只做查看
        rolesResult = rolesResult.filter(
          (item) =>
            item.code === 'aim_segment_view' ||
            item.code === 'aim_campaignV2_view' ||
            item.code === 'aim_campaigns_view' ||
            item.code === 'aim_segment_edit' ||
            item.code === 'aim_campaignV2_edit'
        );
        setRolesList(rolesResult);
      } catch (error) {
        console.error(error);
      }
    };

    shareOpen && init();
  }, [shareOpen]);

  useEffect(() => {
    const getData = async () => {
      try {
        const _param = _.cloneDeep(param);
        _param.search = [
          {
            propertyName: 'shareContentId',
            operator: 'EQ',
            value: shareInfo?.id
          }
        ];
        const res = await shareService.query(_param);

        setCooperateList(res.content);
      } catch (error) {
        console.error(error);
      }
    };

    shareOpen && getData();
  }, [shareOpen]);

  useEffect(() => {
    const getShareUser = async () => {
      setSearchLoading(true);
      const res = await shareService.getProjectUserDeptList({
        companyId: Number(localStorage.getItem('organizationId')),
        userName: displayName || ''
      });
      setUserList(res.filter((item) => item.userId !== Number(localStorage.getItem('userId'))));
      setSearchLoading(false);
    };

    shareOpen && getShareUser();
  }, [shareOpen, displayName]);

  const onCancel = () => {
    form.setFieldsValue({
      shareUsers: undefined,
      roles: undefined
    });
    setDisplayName('');
    dispatchShare({ shareOpen: false });
  };

  const onFinish = async () => {
    setLoading(true);
    try {
      const { shareUsers, roles } = await form.validateFields();
      const params = {
        projectId: localStorage.getItem('projectId'),
        type: shareInfo.type,
        grantShareInfoList: shareUsers.map((item) => {
          return {
            grantShareUserId: Number(item.key.split(',')[0]),
            deptId: Number(item.key.split(',')[1])
          };
        }),
        authIds: roles.join(','),
        shareContentId: shareInfo.id,
        shareUserDeptId: Number(window.getDeptId())
      };

      await shareService.newSave(params);

      message.success(t('setting-7VccEmDxUe6O'));

      form.setFieldsValue({
        shareUsers: undefined,
        roles: undefined
      });

      setDisplayName('');

      dispatchShare({
        shareOpen: false,
        reflash: !reflash
      });

      setLoading(false);
    } catch (error) {
      console.error(error);
      setLoading(false);
    }
  };

  const cooperateShow = () => {
    form.setFieldsValue({
      shareUsers: undefined,
      roles: undefined
    });
    if (cooperateOpen) {
      dispatchShare({
        shareOpen: false,
        shareInfo: { type: shareInfo.type, id: shareInfo.id }
      });
    } else {
      dispatchShare({
        cooperateOpen: true,
        shareOpen: false,
        shareInfo: { type: shareInfo.type, id: shareInfo.id }
      });
    }
  };

  const onShareUserChange = _.debounce((val) => {
    setDisplayName(val);
  }, 1000);

  const onDeselect = () => {
    setDisplayName('');
  };

  const onClear = () => {
    setDisplayName('');
  };

  return (
    <Modal
      open={shareOpen}
      onCancel={onCancel}
      onOk={onFinish}
      confirmLoading={loading}
      className="shareModal"
      destroyOnClose
      width={560}
      title={t('setting-pShaHYaZwZ8M')}
    >
      <div>
        <Form form={form} layout="vertical">
          <Form.Item
            label={t('setting-3U8X9rMDuQh8')}
            rules={[{ required: true, message: t('setting-XgUT3TyGCxxz') }]}
            name="shareUsers"
          >
            <Select
              placeholder={t('setting-KVOlBCJMbo8e')}
              mode="multiple"
              allowClear
              labelInValue
              filterOption={false}
              onSearch={(e) => onShareUserChange(e)}
              popupClassName="shareSelect"
              optionLabelProp="label"
              notFoundContent={searchLoading ? <Spin size="small" /> : null}
              onDeselect={onDeselect}
              onClear={onClear}
            >
              {userList.map((item) => (
                <Option key={`${item.userId},${item.deptId}`} value={item.userId} label={item.userName}>
                  <div>
                    <div className="w-full">{item.userName}</div>
                    <div className="text-[12px] text-[rgba(0,0,0,.45)] whitespace-nowrap overflow-hidden text-ellipsis">
                      {getDeptPath(item.deptId)}
                    </div>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label="操作权限" rules={[{ required: true, message: '请选择操作权限' }]} name="roles">
            <Select
              mode="multiple"
              placeholder="请选择"
              showSearch
              filterOption={(input, option) => option.children.toLowerCase().includes(input.toLowerCase())}
            >
              {rolesList.map((item) => (
                <Option key={item.id} value={item.code}>
                  {item.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>

        <div>
          <div>
            <div className="mb-[8px]">协作者列表</div>
            <div className="flex items-center mb-[24px]">
              {cooperateList.length ? (
                <Avatar.Group
                  className="mr-[8px]"
                  maxCount={8}
                  maxStyle={{
                    color: 'var(--ant-primary-color)',
                    backgroundColor: 'var(--primary-hover-bg) '
                  }}
                >
                  {cooperateList.map((item) => (
                    <Tooltip title={item.grantShareUserName} placement="top" key={item.id}>
                      <Avatar style={{ backgroundColor: 'var(--ant-primary-color)' }}>
                        {item.grantShareUserName[0]}
                      </Avatar>
                    </Tooltip>
                  ))}
                </Avatar.Group>
              ) : null}

              <div>
                <a onClick={cooperateShow}>{t('setting-lOpkAVvCvvU5')}</a>
              </div>
            </div>
          </div>

          {/* <div className="flex items-center">
            <Switch
              size="small"
              checked={publicStatus}
              onChange={(e) => setPublicStatus(e)}
            />
            <div className="ml-[8px]">项目内公开，项目内成员获得可查看</div>
          </div> */}
        </div>
      </div>
    </Modal>
  );
};

export default ShareModal;
