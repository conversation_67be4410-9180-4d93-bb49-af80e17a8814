// 菜单配置文件，支持一级二级菜单配置。
import { t } from '@/utils/translation';

const menuConfig = [
  {
    id: 12,
    name: t('setting-Ej8Ej8Ej8Ej8'),
    parentId: 0,
    orderNum: 1,
    icon: 'pie-chart',
    route: 'kanban',
    children: []
  },
  {
    id: 13,
    name: t('setting-Ej8Ej8Ej8Ej9'),
    parentId: 0,
    orderNum: 2,
    icon: 'pie-chart',
    route: 'yingxiaozhongxin',
    children: []
  },
  {
    id: 14,
    name: '分析中心',
    parentId: 0,
    orderNum: 3,
    icon: 'pie-chart',
    route: 'fenxizhongxin',
    children: []
  },
  {
    id: 15,
    name: '画像中心',
    parentId: 0,
    orderNum: 4,
    icon: 'pie-chart',
    route: '',
    children: [
      {
        id: 18,
        name: '用户分群',
        parentId: 15,
        orderNum: 1,
        icon: 'pie-chart',
        route: '/aimarketer/home/<USER>/userGroup',
        domain: 'http://aimarketer/home.dev.datatist.cn',
        children: []
      }
    ]
  },
  {
    id: 16,
    name: '数据中心',
    parentId: 0,
    orderNum: 5,
    icon: 'pie-chart',
    route: 'shujuzhongxin',
    children: []
  },
  {
    id: 17,
    name: 'AI智能中心',
    parentId: 0,
    orderNum: 6,
    icon: 'pie-chart',
    route: 'aizhongxin',
    children: []
  }
];

export { menuConfig };
