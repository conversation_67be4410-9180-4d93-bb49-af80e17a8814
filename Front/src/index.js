import { message, Modal } from 'antd';
import 'assets/css/reset.scss';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import _ from 'lodash';
import React from 'react';
import ReactDOM from 'react-dom';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import ssoService from 'service/SsoService';
import UserService from 'service/UserService';
import 'utils/console';
import App from './App';
import './index.css';
import store from './store';
import { setDeptId } from './utils/commonUtils';
import { t } from 'utils/translation';

dayjs.locale('zh-cn');

const _userService = new UserService();

global.__SSOCONFIG__ = null;

const getConfirmation = (message, callback) => {
  Modal.confirm({
    title: message,
    onCancel: () => {
      callback(false);
    },
    onOk: () => {
      callback(true);
    }
  });
};

global.__LOGOUT__ = () => {
  setDeptId();

  _userService
    .logout()
    .then(() => {
      localStorage.removeItem('aim_authorization');
      localStorage.removeItem('url');
      localStorage.removeItem('defaultLogin');
      localStorage.removeItem('login');
      localStorage.removeItem('defaultUrl');
      localStorage.removeItem('modifyPassword');
      localStorage.removeItem('deptId');
      // localStorage.removeItem('deptShowType');
      localStorage.removeItem('userId');
      // localStorage.removeItem('projectId');
    })
    .then(() => {
      if (!_.isEmpty(global.__SSOCONFIG__) && global.__SSOCONFIG__.redirectFlag) {
        if (global.__SSOCONFIG__.ssoCode === 'SW') {
          window.location.replace('/aimarketer/login');
        } else {
          window.location.replace('/aimarketer/ssologin');
        }
      } else {
        window.location.replace('/aimarketer/login');
      }
    })
    .finally(() => message.success(t('global-logout'), 1));
};

const getDefaultSsoLoginUrl = async () => {
  // console.log('login');
  // localStorage.setItem('login', true);
  try {
    const reData = await ssoService.getDefaultSsoLoginUrl();
    global.__SSOCONFIG__ = reData;
  } catch (error) {
    console.error(error);
  }
};

global.__SSOCONFIG__ === null && getDefaultSsoLoginUrl();
global.__PROXYURL__ = window.location.hostname === 'localhost' ? process?.env?.__PROXYURL__ : null;

ReactDOM.render(
  <Provider store={store}>
    <BrowserRouter getUserConfirmation={getConfirmation}>
      <App />
    </BrowserRouter>
  </Provider>,
  document.getElementById('root')
);
