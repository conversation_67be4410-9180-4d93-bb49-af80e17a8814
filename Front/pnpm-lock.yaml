lockfileVersion: '6.0'

settings:
  autoInstallPeers: false
  excludeLinksFromLockfile: false

overrides:
  path-to-regexp: 1.9.0

dependencies:
  '@ant-design/charts':
    specifier: ^1.4.2
    version: 1.4.2(@ant-design/icons@4.8.1)(antd@4.24.9)(classnames@2.5.1)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0)
  '@ant-design/compatible':
    specifier: ^1.0.8
    version: 1.1.2(antd@4.24.9)(prop-types@15.8.1)(react-dom@16.14.0)(react@16.14.0)
  '@ant-design/icons':
    specifier: ^4.8.1
    version: 4.8.1(react-dom@16.14.0)(react@16.14.0)
  '@antv/g2':
    specifier: ^4.2.11
    version: 4.2.11
  '@antv/g6':
    specifier: ^4.8.24
    version: 4.8.24
  '@antv/l7':
    specifier: 2.9.27
    version: 2.9.27
  '@antv/l7-district':
    specifier: ^2.3.12
    version: 2.3.12(@antv/l7-maps@2.9.27)(@antv/l7@2.9.27)(prop-types@15.8.1)(react-dom@16.14.0)(react@16.14.0)
  '@antv/l7-maps':
    specifier: 2.9.27
    version: 2.9.27
  '@antv/layout':
    specifier: ^0.3.25
    version: 0.3.25
  '@antv/x6':
    specifier: ^2.18.1
    version: 2.18.1
  '@antv/x6-plugin-clipboard':
    specifier: ^2.1.6
    version: 2.1.6(@antv/x6@2.18.1)
  '@antv/x6-plugin-history':
    specifier: ^2.2.4
    version: 2.2.4(@antv/x6@2.18.1)
  '@antv/x6-plugin-keyboard':
    specifier: ^2.2.3
    version: 2.2.3(@antv/x6@2.18.1)
  '@antv/x6-plugin-selection':
    specifier: ^2.2.2
    version: 2.2.2(@antv/x6@2.18.1)
  '@antv/x6-plugin-snapline':
    specifier: ^2.1.7
    version: 2.1.7(@antv/x6@2.18.1)
  '@antv/x6-plugin-stencil':
    specifier: ^2.1.5
    version: 2.1.5(@antv/x6@2.18.1)
  '@antv/x6-plugin-transform':
    specifier: ^2.1.8
    version: 2.1.8(@antv/x6@2.18.1)
  '@antv/x6-react-shape':
    specifier: 2.0.8
    version: 2.0.8(@antv/x6@2.18.1)(react-dom@16.14.0)(react@16.14.0)
  '@antv/xflow-extension':
    specifier: 1.0.52
    version: 1.0.52(@ant-design/icons@4.8.1)(@antv/x6-react-shape@2.0.8)(@antv/x6@2.18.1)(antd@4.24.9)(classnames@2.5.1)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0)
  '@babel/plugin-proposal-decorators':
    specifier: ^7.25.9
    version: 7.25.9
  '@emoji-mart/data':
    specifier: ^1.2.1
    version: 1.2.1
  '@emoji-mart/react':
    specifier: ^1.1.1
    version: 1.1.1(emoji-mart@5.6.0)(react@16.14.0)
  '@umijs/hooks':
    specifier: ^1.9.3
    version: 1.9.3(react@16.14.0)
  ahooks:
    specifier: ^3.8.4
    version: 3.8.4(react@16.14.0)
  antd:
    specifier: 4.24.9
    version: 4.24.9(react-dom@16.14.0)(react@16.14.0)
  axios:
    specifier: ^0.19.2
    version: 0.19.2
  buffer:
    specifier: ^6.0.3
    version: 6.0.3
  classnames:
    specifier: ^2.5.1
    version: 2.5.1
  codemirror:
    specifier: ^5.65.18
    version: 5.65.18
  copy-to-clipboard:
    specifier: ^3.3.3
    version: 3.3.3
  dayjs:
    specifier: ^1.11.13
    version: 1.11.13
  emoji-mart:
    specifier: ^5.6.0
    version: 5.6.0
  html2canvas:
    specifier: ^1.4.1
    version: 1.4.1
  i18next:
    specifier: ^23.16.8
    version: 23.16.8
  immutability-helper:
    specifier: ^3.1.1
    version: 3.1.1
  jsencrypt:
    specifier: ^3.3.2
    version: 3.3.2
  json-beautify:
    specifier: ^1.1.1
    version: 1.1.1
  lodash:
    specifier: ^4.17.21
    version: 4.17.21
  memory-cache:
    specifier: ^0.2.0
    version: 0.2.0
  process:
    specifier: ^0.11.10
    version: 0.11.10
  prop-types:
    specifier: ^15.8.1
    version: 15.8.1
  querystring:
    specifier: ^0.2.1
    version: 0.2.1
  rc-picker:
    specifier: ^4.9.2
    version: 4.11.2(dayjs@1.11.13)(react-dom@16.14.0)(react@16.14.0)
  rc-resize-observer:
    specifier: 1.2.1
    version: 1.2.1(react-dom@16.14.0)(react@16.14.0)
  react:
    specifier: ^16.14.0
    version: 16.14.0
  react-codemirror2:
    specifier: ^7.3.0
    version: 7.3.0(codemirror@5.65.18)(react@16.14.0)
  react-color:
    specifier: ^2.19.3
    version: 2.19.3(react@16.14.0)
  react-dnd:
    specifier: ^11.1.3
    version: 11.1.3(react-dom@16.14.0)(react@16.14.0)
  react-dnd-html5-backend:
    specifier: ^11.1.3
    version: 11.1.3
  react-dom:
    specifier: ^16.14.0
    version: 16.14.0(react@16.14.0)
  react-fast-marquee:
    specifier: ^1.6.5
    version: 1.6.5(react-dom@16.14.0)(react@16.14.0)
  react-grid-layout:
    specifier: ^1.5.0
    version: 1.5.0(react-dom@16.14.0)(react@16.14.0)
  react-i18next:
    specifier: ^15.4.0
    version: 15.4.1(i18next@23.16.8)(react-dom@16.14.0)(react@16.14.0)
  react-loadable:
    specifier: npm:@docusaurus/react-loadable
    version: /@docusaurus/react-loadable@6.0.0(react@16.14.0)
  react-redux:
    specifier: ^7.2.9
    version: 7.2.9(react-dom@16.14.0)(react@16.14.0)
  react-resizable:
    specifier: ^1.11.1
    version: 1.11.1(react-dom@16.14.0)(react@16.14.0)
  react-router-dom:
    specifier: ^5.3.4
    version: 5.3.4(react@16.14.0)
  react-slick:
    specifier: ^0.28.1
    version: 0.28.1(react-dom@16.14.0)(react@16.14.0)
  react-visibility-sensor:
    specifier: ^5.1.1
    version: 5.1.1(react-dom@16.14.0)(react@16.14.0)
  react-window:
    specifier: ^1.8.11
    version: 1.8.11(react-dom@16.14.0)(react@16.14.0)
  redux:
    specifier: ^4.2.1
    version: 4.2.1
  redux-thunk:
    specifier: ^2.4.2
    version: 2.4.2(redux@4.2.1)
  slick-carousel:
    specifier: ^1.8.1
    version: 1.8.1
  sortablejs:
    specifier: ^1.15.6
    version: 1.15.6
  uuid:
    specifier: ^8.3.2
    version: 8.3.2
  wolf-static-cpnt:
    specifier: 1.5.2
    version: 1.5.2(classnames@2.5.1)(dayjs@1.11.13)(prop-types@15.8.1)(react-dom@16.14.0)(react@16.14.0)
  zustand:
    specifier: ^4.5.6
    version: 4.5.6(@types/react@16.14.62)(react@16.14.0)

devDependencies:
  '@rsbuild/core':
    specifier: ^1.2.19
    version: 1.2.19
  '@rsbuild/plugin-react':
    specifier: ^1.1.0
    version: 1.1.1(@rsbuild/core@1.2.19)
  '@rsbuild/plugin-sass':
    specifier: ^1.2.1
    version: 1.2.2(@rsbuild/core@1.2.19)
  '@rspack/core':
    specifier: ^1.2.8
    version: 1.2.8
  '@types/jest':
    specifier: ^29.5.14
    version: 29.5.14
  '@types/lodash':
    specifier: ^4.17.15
    version: 4.17.15
  '@types/node':
    specifier: ^20.17.17
    version: 20.17.19
  '@types/react':
    specifier: ^16.14.62
    version: 16.14.62
  '@types/react-dom':
    specifier: ^18.3.5
    version: 18.3.5(@types/react@16.14.62)
  '@types/react-redux':
    specifier: ^7.1.34
    version: 7.1.34
  '@types/react-router-dom':
    specifier: ^5.3.3
    version: 5.3.3
  '@types/sortablejs':
    specifier: ^1.15.8
    version: 1.15.8
  '@typescript-eslint/eslint-plugin':
    specifier: ^6.21.0
    version: 6.21.0(@typescript-eslint/parser@6.21.0)(eslint@6.6.0)(typescript@5.0.4)
  '@typescript-eslint/parser':
    specifier: ^6.21.0
    version: 6.21.0(eslint@6.6.0)(typescript@5.0.4)
  ant-path-matcher:
    specifier: ^0.0.5
    version: 0.0.5
  antd-dayjs-webpack-plugin:
    specifier: ^1.0.6
    version: 1.0.6(dayjs@1.11.13)
  babel-eslint:
    specifier: 10.0.3
    version: 10.0.3(eslint@6.6.0)
  eslint:
    specifier: 6.6.0
    version: 6.6.0
  eslint-config-airbnb:
    specifier: 18.0.0
    version: 18.0.0(eslint-plugin-import@2.31.0)(eslint-plugin-jsx-a11y@6.2.3)(eslint-plugin-react-hooks@3.0.0)(eslint-plugin-react@7.14.3)(eslint@6.6.0)
  eslint-config-prettier:
    specifier: ^9.1.0
    version: 9.1.0(eslint@6.6.0)
  eslint-plugin-import:
    specifier: ^2.31.0
    version: 2.31.0(@typescript-eslint/parser@6.21.0)(eslint@6.6.0)
  eslint-plugin-jsx-a11y:
    specifier: 6.2.3
    version: 6.2.3(eslint@6.6.0)
  eslint-plugin-react:
    specifier: 7.14.3
    version: 7.14.3(eslint@6.6.0)
  eslint-plugin-react-hooks:
    specifier: ^3.0.0
    version: 3.0.0(eslint@6.6.0)
  eslint-plugin-unused-imports:
    specifier: ^3.2.0
    version: 3.2.0(@typescript-eslint/eslint-plugin@6.21.0)(eslint@6.6.0)
  husky:
    specifier: ^4.3.8
    version: 4.3.8
  lint-staged:
    specifier: ^10.5.4
    version: 10.5.4
  node-polyfill-webpack-plugin:
    specifier: ^4.1.0
    version: 4.1.0
  postcss:
    specifier: ^8.5.1
    version: 8.5.3
  postcss-loader:
    specifier: ^8.1.1
    version: 8.1.1(@rspack/core@1.2.8)(postcss@8.5.3)(typescript@5.0.4)
  prettier:
    specifier: ^3.4.2
    version: 3.5.2
  rsbuild-cli:
    specifier: ^0.0.0
    version: 0.0.0
  sass:
    specifier: ^1.84.0
    version: 1.85.1
  sass-loader:
    specifier: ^16.0.4
    version: 16.0.4(@rspack/core@1.2.8)(sass@1.85.1)
  sass-resources-loader:
    specifier: ^2.2.5
    version: 2.2.5
  stream-browserify:
    specifier: ^3.0.0
    version: 3.0.0
  tailwindcss:
    specifier: ^3.4.17
    version: 3.4.17
  typescript:
    specifier: 5.0.4
    version: 5.0.4

packages:

  /@alloc/quick-lru@5.2.0:
    resolution: {integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==}
    engines: {node: '>=10'}
    dev: true

  /@amap/amap-jsapi-loader@0.0.3:
    resolution: {integrity: sha512-3Tz50UdmRY2BiONK/mafEQzshYGUinK2hmDlKjYtoJHC/aVydiMOolHENWmP98F603RcrWTM7aLxOFMgesFfug==}
    dev: false

  /@amap/amap-jsapi-loader@1.0.1:
    resolution: {integrity: sha512-nPyLKt7Ow/ThHLkSvn2etQlUzqxmTVgK7bIgwdBRTg2HK5668oN7xVxkaiRe3YZEzGzfV2XgH5Jmu2T73ljejw==}
    dev: false

  /@ant-design/charts@1.4.2(@ant-design/icons@4.8.1)(antd@4.24.9)(classnames@2.5.1)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-BcVx6AAnwxSdzAVUZReSuvUVtnT5AkJivq3wmcYj17scll26HHficg35yimGskAj3Gu1upYjBQBz6Tk7GEMJsQ==}
    peerDependencies:
      '@ant-design/icons': ^4.6.0
      antd: ^4.6.3
      lodash: ^4.17.20
      react: '>=16.8.4'
      react-dom: '>=16.8.4'
    dependencies:
      '@ant-design/flowchart': 1.2.2(@ant-design/icons@4.8.1)(antd@4.24.9)(classnames@2.5.1)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0)
      '@ant-design/graphs': 1.4.0(react-dom@16.14.0)(react@16.14.0)
      '@ant-design/icons': 4.8.1(react-dom@16.14.0)(react@16.14.0)
      '@ant-design/maps': 1.0.7(react-dom@16.14.0)(react@16.14.0)
      '@ant-design/plots': 1.2.5(react-dom@16.14.0)(react@16.14.0)
      antd: 4.24.9(react-dom@16.14.0)(react@16.14.0)
      lodash: 4.17.21
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    transitivePeerDependencies:
      - classnames
    dev: false

  /@ant-design/colors@4.0.5:
    resolution: {integrity: sha512-3mnuX2prnWOWvpFTS2WH2LoouWlOgtnIpc6IarWN6GOzzLF8dW/U8UctuvIPhoboETehZfJ61XP+CGakBEPJ3Q==}
    dependencies:
      tinycolor2: 1.6.0
    dev: false

  /@ant-design/colors@6.0.0:
    resolution: {integrity: sha512-qAZRvPzfdWHtfameEGP2Qvuf838NhergR35o+EuVyB5XvSA98xod5r4utvi4TJ3ywmevm290g9nsCG5MryrdWQ==}
    dependencies:
      '@ctrl/tinycolor': 3.6.1
    dev: false

  /@ant-design/compatible@1.1.2(antd@4.24.9)(prop-types@15.8.1)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-Qsx5Qw97eiSgcxyQDlY45QSbvGn0gUdpX8XFImPvzZpKwabqQ2HnXXuUlb8RbrkURswaPIoyLEGKDPeogIaURA==}
    peerDependencies:
      antd: 3.x
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@ant-design/icons': 4.8.1(react-dom@16.14.0)(react@16.14.0)
      antd: 4.24.9(react-dom@16.14.0)(react@16.14.0)
      classnames: 2.5.1
      lodash.camelcase: 4.3.0
      lodash.upperfirst: 4.3.1
      omit.js: 1.0.2
      rc-animate: 2.11.1(react-dom@16.14.0)(react@16.14.0)
      rc-editor-mention: 1.1.13(react-dom@16.14.0)(react@16.14.0)
      rc-form: 2.4.12(prop-types@15.8.1)
      rc-util: 4.21.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    transitivePeerDependencies:
      - prop-types
    dev: false

  /@ant-design/flowchart@1.2.2(@ant-design/icons@4.8.1)(antd@4.24.9)(classnames@2.5.1)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-bzHZ81qqHjWIrXcCMInDB6eMTzbswaVCOAdUTCQJ+B45aybkVsU9LMCKquyOwRM7U7PWNfPC9S/lAlx2i2BvBQ==}
    peerDependencies:
      '@ant-design/icons': ^4.6.0
      antd: ^4.6.3
      lodash: ^4.17.20
      react: '>=16.8.4'
      react-dom: '>=16.8.4'
    dependencies:
      '@ant-design/icons': 4.8.1(react-dom@16.14.0)(react@16.14.0)
      '@antv/layout': 0.1.31
      '@antv/x6': 1.34.14
      '@antv/x6-react-components': 1.1.20(antd@4.24.9)(react-dom@16.14.0)(react@16.14.0)
      '@antv/x6-react-shape': 1.6.4(@antv/x6@1.34.14)(react-dom@16.14.0)(react@16.14.0)
      '@antv/xflow': 1.0.55(@ant-design/icons@4.8.1)(antd@4.24.9)(classnames@2.5.1)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0)
      antd: 4.24.9(react-dom@16.14.0)(react@16.14.0)
      lodash: 4.17.21
      react: 16.14.0
      react-color: 2.17.3(react@16.14.0)
      react-dom: 16.14.0(react@16.14.0)
      react-use: 17.3.1(react-dom@16.14.0)(react@16.14.0)
    transitivePeerDependencies:
      - classnames
    dev: false

  /@ant-design/graphs@1.4.0(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-S7tbxSxrIqOSJL23S2M1GPyNnBgoaVB1xKytZeczx4YxMvuapEav+pLJy9X/TbraK0ArTNev97FQN9tD9ei6jQ==}
    peerDependencies:
      react: '>=16.8.4'
      react-dom: '>=16.8.4'
    dependencies:
      '@antv/dom-util': 2.0.4
      '@antv/g6': 4.8.24
      '@antv/layout': 0.1.31
      '@antv/util': 2.0.17
      insert-css: 2.0.0
      react: 16.14.0
      react-content-loader: 5.1.4(react@16.14.0)
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /@ant-design/icons-svg@4.3.1:
    resolution: {integrity: sha512-4QBZg8ccyC6LPIRii7A0bZUk3+lEDCLnhB+FVsflGdcWPPmV+j3fire4AwwoqHV/BibgvBmR9ZIo4s867smv+g==}
    dev: false

  /@ant-design/icons@4.8.1(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-JRAuiqllnMsiZIO8OvBOeFconprC3cnMpJ9MvXrHh+H5co9rlg8/aSHQfLf5jKKe18lUgRaIwC2pz8YxH9VuCA==}
    engines: {node: '>=8'}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@ant-design/colors': 6.0.0
      '@ant-design/icons-svg': 4.3.1
      '@babel/runtime': 7.22.11
      classnames: 2.5.1
      lodash: 4.17.21
      rc-util: 5.37.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /@ant-design/maps@1.0.7(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-mGi5vODWsXuYz2HstsG+faql/nAJ7v4V1sUfJjgD0mo2kNEPpEfcw0vbkQyNpyn/DZrR5Ue40Twv0N5IoCmxaA==}
    peerDependencies:
      react: '>=16.8.4'
      react-dom: '>=16.8.4'
    dependencies:
      '@antv/l7': 2.22.4
      '@antv/l7plot': 0.5.5(@antv/l7@2.22.4)
      '@antv/util': 2.0.17
      react: 16.14.0
      react-content-loader: 5.1.4(react@16.14.0)
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /@ant-design/plots@1.2.5(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-8Jvu2xC5y5/B38/9Qr6CBiXCZopsGEA3IR4pjLFlkLoT4OHIKr4y8oIvhahM9mh9ZATyjkrZLWJBI8yETrReGg==}
    peerDependencies:
      react: '>=16.8.4'
      react-dom: '>=16.8.4'
    dependencies:
      '@antv/g2plot': 2.4.31
      '@antv/util': 2.0.17
      react: 16.14.0
      react-content-loader: 5.1.4(react@16.14.0)
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /@ant-design/react-slick@0.29.2(react@16.14.0):
    resolution: {integrity: sha512-kgjtKmkGHa19FW21lHnAfyyH9AAoh35pBdcJ53rHmQ3O+cfFHGHnUbj/HFrRNJ5vIts09FKJVAD8RpaC+RaWfA==}
    peerDependencies:
      react: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      json2mq: 0.2.0
      lodash: 4.17.21
      react: 16.14.0
      resize-observer-polyfill: 1.5.1
    dev: false

  /@antv/adjust@0.2.5:
    resolution: {integrity: sha512-MfWZOkD9CqXRES6MBGRNe27Q577a72EIwyMnE29wIlPliFvJfWwsrONddpGU7lilMpVKecS3WAzOoip3RfPTRQ==}
    dependencies:
      '@antv/util': 2.0.17
      tslib: 1.14.1
    dev: false

  /@antv/algorithm@0.1.26:
    resolution: {integrity: sha512-DVhcFSQ8YQnMNW34Mk8BSsfc61iC1sAnmcfYoXTAshYHuU50p/6b7x3QYaGctDNKWGvi1ub7mPcSY0bK+aN0qg==}
    dependencies:
      '@antv/util': 2.0.17
      tslib: 2.8.1
    dev: false

  /@antv/async-hook@2.2.9:
    resolution: {integrity: sha512-4BUp2ZUaTi2fYL67Ltkf6eV912rYJeSBokGhd5fhhnpUkMA1LEI1mg97Pqmx3yC50VEQ+LKXZxj9ePZs80ECfw==}
    dependencies:
      async: 3.2.4
    dev: false

  /@antv/attr@0.3.5:
    resolution: {integrity: sha512-wuj2gUo6C8Q2ASSMrVBuTcb5LcV+Tc0Egiy6bC42D0vxcQ+ta13CLxgMmHz8mjD0FxTPJDXSciyszRSC5TdLsg==}
    dependencies:
      '@antv/color-util': 2.0.6
      '@antv/scale': 0.3.18
      '@antv/util': 2.0.17
      tslib: 2.8.1
    dev: false

  /@antv/color-util@2.0.6:
    resolution: {integrity: sha512-KnPEaAH+XNJMjax9U35W67nzPI+QQ2x27pYlzmSIWrbj4/k8PGrARXfzDTjwoozHJY8qG62Z+Ww6Alhu2FctXQ==}
    dependencies:
      '@antv/util': 2.0.17
      tslib: 2.8.1
    dev: false

  /@antv/component@0.8.35:
    resolution: {integrity: sha512-VnRa5X77nBPI952o2xePEEMSNZ6g2mcUDrQY8mVL2kino/8TFhqDq5fTRmDXZyWyIYd4ulJTz5zgeSwAnX/INQ==}
    dependencies:
      '@antv/color-util': 2.0.6
      '@antv/dom-util': 2.0.4
      '@antv/g-base': 0.5.15
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/path-util': 2.0.15
      '@antv/scale': 0.3.18
      '@antv/util': 2.0.17
      fecha: 4.2.3
      tslib: 2.8.1
    dev: false

  /@antv/coord@0.3.1:
    resolution: {integrity: sha512-rFE94C8Xzbx4xmZnHh2AnlB3Qm1n5x0VT3OROy257IH6Rm4cuzv1+tZaUBATviwZd99S+rOY9telw/+6C9GbRw==}
    dependencies:
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/util': 2.0.17
      tslib: 2.8.1
    dev: false

  /@antv/dom-util@2.0.4:
    resolution: {integrity: sha512-2shXUl504fKwt82T3GkuT4Uoc6p9qjCKnJ8gXGLSW4T1W37dqf9AV28aCfoVPHp2BUXpSsB+PAJX2rG/jLHsLQ==}
    dependencies:
      tslib: 2.8.1
    dev: false

  /@antv/event-emitter@0.1.3:
    resolution: {integrity: sha512-4ddpsiHN9Pd4UIlWuKVK1C4IiZIdbwQvy9i7DUSI3xNJ89FPUFt8lxDYj8GzzfdllV0NkJTRxnG+FvLk0llidg==}
    dev: false

  /@antv/g-base@0.5.15:
    resolution: {integrity: sha512-QOtq50QpnKez9J75/Z8j2yZ7QDQdk8R8mVQJiHtaEO5eI7DM4ZbrsWff/Ew26JYmPWdq7nbRuARMAD4PX9uuLA==}
    dependencies:
      '@antv/event-emitter': 0.1.3
      '@antv/g-math': 0.1.9
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/path-util': 2.0.15
      '@antv/util': 2.0.17
      '@types/d3-timer': 2.0.1
      d3-ease: 1.0.7
      d3-interpolate: 3.0.1
      d3-timer: 1.0.10
      detect-browser: 5.3.0
      tslib: 2.8.1
    dev: false

  /@antv/g-canvas@0.5.14:
    resolution: {integrity: sha512-IUGLEMIMAUYgaBMT8h3FTmYQYz7sjQkKWwh6Psqx+UPK86fySa+G8fMRrh1EqAL07jVB+GRnn6Ym+3FoFUgeFg==}
    dependencies:
      '@antv/g-base': 0.5.15
      '@antv/g-math': 0.1.9
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/path-util': 2.0.15
      '@antv/util': 2.0.17
      gl-matrix: 3.4.3
      tslib: 2.8.1
    dev: false

  /@antv/g-device-api@1.6.13:
    resolution: {integrity: sha512-lTvlSHYDZyWJnAR1W8DOQLwUo32VpRopbS/BPQqStcOV6FqaC+u5YjT50KbJ+oBWcorpzfknhICRwEA3Xm8t9Q==}
    dependencies:
      '@antv/util': 3.3.4
      '@webgpu/types': 0.1.54
      eventemitter3: 5.0.1
      gl-matrix: 3.4.3
      tslib: 2.8.1
    dev: false

  /@antv/g-math@0.1.9:
    resolution: {integrity: sha512-KHMSfPfZ5XHM1PZnG42Q2gxXfOitYveNTA7L61lR6mhZ8Y/aExsYmHqaKBsSarU0z+6WLrl9C07PQJZaw0uljQ==}
    dependencies:
      '@antv/util': 2.0.17
      gl-matrix: 3.4.3
    dev: false

  /@antv/g-svg@0.5.7:
    resolution: {integrity: sha512-jUbWoPgr4YNsOat2Y/rGAouNQYGpw4R0cvlN0YafwOyacFFYy2zC8RslNd6KkPhhR3XHNSqJOuCYZj/YmLUwYw==}
    dependencies:
      '@antv/g-base': 0.5.15
      '@antv/g-math': 0.1.9
      '@antv/util': 2.0.17
      detect-browser: 5.3.0
      tslib: 2.8.1
    dev: false

  /@antv/g-webgpu-core@0.5.6:
    resolution: {integrity: sha512-DPiH3GkAUiT0Q+LAKeImpI+IOQ/gP2w6HstYKivpFIpBPIvZ/9equM3icVrn1iDfDkZANVXQ1PppcO3xBv1ZTw==}
    dependencies:
      eventemitter3: 4.0.7
      gl-matrix: 3.4.3
      inversify: 5.1.1
      inversify-inject-decorators: 3.1.0
      probe.gl: 3.6.0
      reflect-metadata: 0.1.13
    dev: false

  /@antv/g-webgpu-core@0.7.2:
    resolution: {integrity: sha512-xUMmop7f3Rs34zFYKXLqHhDR1CQTeDl/7vI7Sn3X/73BqJc3X3HIIRvm83Fg2CjVACaOzw4WeLRXNaOCp9fz9w==}
    dependencies:
      eventemitter3: 4.0.7
      gl-matrix: 3.4.3
      lodash: 4.17.21
      probe.gl: 3.6.0
    dev: false

  /@antv/g-webgpu-engine@0.5.6:
    resolution: {integrity: sha512-D311qYUefdEFwLayutIHqucrAY3cAGH3BdnXS37nq+0nsglrHcNP0Ab1YTinn9RihLoY3yXFTLzrYkJHJbZXDg==}
    dependencies:
      '@antv/g-webgpu-core': 0.5.6
      '@webgpu/glslang': 0.0.15
      '@webgpu/types': 0.0.31
      gl-matrix: 3.4.3
      hammerjs: 2.0.8
      inversify: 5.1.1
      inversify-inject-decorators: 3.1.0
      probe.gl: 3.6.0
      reflect-metadata: 0.1.13
      regl: 1.7.0
    dev: false

  /@antv/g-webgpu-engine@0.7.2:
    resolution: {integrity: sha512-lx8Y93IW2cnJvdoDRKyMmTdYqSC1pOmF0nyG3PGGyA0NI9vBYVgO0KTF6hkyWjdTWVq7XDZyf/h8CJridLh3lg==}
    dependencies:
      '@antv/g-webgpu-core': 0.7.2
      gl-matrix: 3.4.3
      lodash: 4.17.21
      regl: 1.7.0
    dev: false

  /@antv/g-webgpu@0.5.5:
    resolution: {integrity: sha512-TxtBniINFq1jFGEPo46xjJfrbJbUqkFd5wmsRs3tcg/7J7xoldOP1kEadpI3AJG9knMYdE92VpILw1VPd6DgzQ==}
    dependencies:
      '@antv/g-webgpu-core': 0.5.6
      '@antv/g-webgpu-engine': 0.5.6
      '@webgpu/types': 0.0.31
      gl-matrix: 3.4.3
      gl-vec2: 1.3.0
      hammerjs: 2.0.8
      inversify: 5.1.1
      inversify-inject-decorators: 3.1.0
      polyline-miter-util: 1.0.1
      polyline-normals: 2.0.2
      probe.gl: 3.6.0
      reflect-metadata: 0.1.13
    dev: false

  /@antv/g-webgpu@0.7.2:
    resolution: {integrity: sha512-kw+oYGsdvj5qeUfy5DPb/jztZBV+2fmqBd3Vv8NlKatfBmv8AirYX/CCW74AUSdWm99rEiLyxFB1VdRZ6b/wnQ==}
    dependencies:
      '@antv/g-webgpu-core': 0.7.2
      '@antv/g-webgpu-engine': 0.7.2
      gl-matrix: 3.4.3
      gl-vec2: 1.3.0
      lodash: 4.17.21
    dev: false

  /@antv/g2@4.2.11:
    resolution: {integrity: sha512-QiqxLLYDWkv9c4oTcXscs6NMxBuWZ1JCarHPZ27J43IN2BV+qUKw8yce0A8CBR8fCILEFqQAfS00Szqpye036Q==}
    dependencies:
      '@antv/adjust': 0.2.5
      '@antv/attr': 0.3.5
      '@antv/color-util': 2.0.6
      '@antv/component': 0.8.35
      '@antv/coord': 0.3.1
      '@antv/dom-util': 2.0.4
      '@antv/event-emitter': 0.1.3
      '@antv/g-base': 0.5.15
      '@antv/g-canvas': 0.5.14
      '@antv/g-svg': 0.5.7
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/path-util': 2.0.15
      '@antv/scale': 0.3.18
      '@antv/util': 2.0.17
      tslib: 2.8.1
    dev: false

  /@antv/g2plot@2.4.31:
    resolution: {integrity: sha512-SlWHYVsJgRN7E1Oe5Qk6yWBrSWmctmloknFmklaqe9vEeK+YB9ZLUffZvtAHT10mA2NZ+VjGUhlnMNgR9M1PQg==}
    dependencies:
      '@antv/color-util': 2.0.6
      '@antv/event-emitter': 0.1.3
      '@antv/g-base': 0.5.15
      '@antv/g2': 4.2.11
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/path-util': 3.0.1
      '@antv/scale': 0.3.18
      '@antv/util': 2.0.17
      d3-hierarchy: 2.0.0
      d3-regression: 1.3.10
      fmin: 0.0.2
      pdfast: 0.2.0
      size-sensor: 1.0.1
      tslib: 2.8.1
    dev: false

  /@antv/g6-core@0.8.24:
    resolution: {integrity: sha512-rgI3dArAD8uoSz2+skS4ctN4x/Of33ivTIKaEYYvClxgkLZWVz9zvocy+5AWcVPBHZsAXkZcdh9zndIoWY/33A==}
    dependencies:
      '@antv/algorithm': 0.1.26
      '@antv/dom-util': 2.0.4
      '@antv/event-emitter': 0.1.3
      '@antv/g-base': 0.5.15
      '@antv/g-math': 0.1.9
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/path-util': 2.0.15
      '@antv/util': 2.0.17
      ml-matrix: 6.10.4
      tslib: 2.8.1
    dev: false

  /@antv/g6-element@0.8.24(@antv/g6@4.8.24):
    resolution: {integrity: sha512-61FXkt9LY+6EOUtSam1iFTOW2AM59sPVcV1BuPj4dXiD0dluLE+R7d8B/94g1tKDw9tsjhfUQGC7hTXscJRJFw==}
    peerDependencies:
      '@antv/g6': 4.8.24
    dependencies:
      '@antv/g-base': 0.5.15
      '@antv/g6': 4.8.24
      '@antv/g6-core': 0.8.24
      '@antv/util': 2.0.17
      tslib: 2.8.1
    dev: false

  /@antv/g6-pc@0.8.24(@antv/g6@4.8.24):
    resolution: {integrity: sha512-nf0y1lrp8J5DotqRryXd2S/J30COW8spVcLF9gUqywGqQAHfE00Ywkqr+PZBnsfCZXsXCi9o0+CE9NrkWs4SBQ==}
    dependencies:
      '@ant-design/colors': 4.0.5
      '@antv/algorithm': 0.1.26
      '@antv/dom-util': 2.0.4
      '@antv/event-emitter': 0.1.3
      '@antv/g-base': 0.5.15
      '@antv/g-canvas': 0.5.14
      '@antv/g-math': 0.1.9
      '@antv/g-svg': 0.5.7
      '@antv/g6-core': 0.8.24
      '@antv/g6-element': 0.8.24(@antv/g6@4.8.24)
      '@antv/g6-plugin': 0.8.24(@antv/g6@4.8.24)
      '@antv/hierarchy': 0.6.11
      '@antv/layout': 0.3.25(dagre@0.8.5)
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/path-util': 2.0.15
      '@antv/util': 2.0.17
      color: 3.2.1
      d3-force: 2.1.1
      dagre: 0.8.5
      insert-css: 2.0.0
      ml-matrix: 6.10.4
      tslib: 2.8.1
    transitivePeerDependencies:
      - '@antv/g6'
    dev: false

  /@antv/g6-plugin@0.8.24(@antv/g6@4.8.24):
    resolution: {integrity: sha512-ZIOnwLTC7SM2bFiJZ3vYFWnkyOCWKqnU96i/fBh1qAoY5slDS3hatenZWEXUtOcqaKw1h+5A5f72MRXqBBVn0g==}
    peerDependencies:
      '@antv/g6': 4.8.24
    dependencies:
      '@antv/dom-util': 2.0.4
      '@antv/g-base': 0.5.15
      '@antv/g-canvas': 0.5.14
      '@antv/g-svg': 0.5.7
      '@antv/g6': 4.8.24
      '@antv/g6-core': 0.8.24
      '@antv/g6-element': 0.8.24(@antv/g6@4.8.24)
      '@antv/matrix-util': 3.1.0-beta.3
      '@antv/path-util': 2.0.15
      '@antv/scale': 0.3.18
      '@antv/util': 2.0.17
      insert-css: 2.0.0
    dev: false

  /@antv/g6@4.8.24:
    resolution: {integrity: sha512-bgj7sZ+z45JmOngIpYpwmSIg7SboMLZBoAlX0+RoAETZB3/xvZO0MXT3lCSyAhIgm5Sb68pekKi7OStuo04NyQ==}
    dependencies:
      '@antv/g6-pc': 0.8.24(@antv/g6@4.8.24)
    dev: false

  /@antv/graphlib@1.2.0:
    resolution: {integrity: sha512-hhJOMThec51nU4Fe5p/viLlNIL71uDEgYFzKPajWjr2715SFG1HAgiP6AVylIeqBcAZ04u3Lw7usjl/TuI5RuQ==}
    dev: false

  /@antv/hierarchy@0.6.11:
    resolution: {integrity: sha512-RJVhEMCuu4vj+Dt25lXIiNdd7jaqm/fqWGYikiELha4S5tnzdJoTUaUvvpfWlxLx4B0RsS9XRwBs1bOKN71TKg==}
    dependencies:
      '@antv/util': 2.0.17
    dev: false

  /@antv/l7-component@2.22.4:
    resolution: {integrity: sha512-qgMNGc84NyRymGvDDs5GZpx8otqQeC1CS3dh5kEsChsoZk/ir80Q8enLaVs5m8Tbpx/emLj2BxijwE+Snvg7FA==}
    dependencies:
      '@antv/l7-core': 2.22.4
      '@antv/l7-layers': 2.22.4
      '@antv/l7-utils': 2.22.4
      '@babel/runtime': 7.25.6
      eventemitter3: 4.0.7
      supercluster: 7.1.5
    dev: false

  /@antv/l7-component@2.9.27:
    resolution: {integrity: sha512-C7a+3sPHjjfo21KWo/C7tIcJzQ27XpMYbEWTzM49O4vo1iFxcp2yK8JbJFymDvYfMqorCL3zyXnx0pWImVpAPA==}
    dependencies:
      '@antv/l7-core': 2.9.27
      '@antv/l7-utils': 2.9.27
      '@babel/runtime': 7.25.6
      eventemitter3: 4.0.7
      inversify: 5.1.1
      lodash: 4.17.21
      reflect-metadata: 0.1.13
      supercluster: 7.1.5
    dev: false

  /@antv/l7-core@2.22.4:
    resolution: {integrity: sha512-3bG9Jl70CJw3F2F4BLGmVGXFrB80DhME9BX9ZEQU8BDh7sc+rrezVuVDG67FxUufIPTXQNqWUPL9De11VJwEZw==}
    dependencies:
      '@antv/async-hook': 2.2.9
      '@antv/l7-utils': 2.22.4
      '@babel/runtime': 7.25.6
      '@mapbox/tiny-sdf': 1.2.5
      '@turf/helpers': 6.5.0
      ajv: 6.12.6
      element-resize-detector: 1.2.4
      eventemitter3: 4.0.7
      gl-matrix: 3.4.3
      hammerjs: 2.0.8
      viewport-mercator-project: 6.2.3
    dev: false

  /@antv/l7-core@2.9.27:
    resolution: {integrity: sha512-OhB25nzZiMqKiEtAbTQUeALGB0E2vXVRn8EbhllAAa4Eyuj9MGIP+RT/ObZSoDeFXjNJlTv92CmS+g3XzCsiFw==}
    dependencies:
      '@antv/async-hook': 2.2.9
      '@antv/l7-utils': 2.9.27
      '@babel/runtime': 7.25.6
      '@turf/helpers': 6.5.0
      ajv: 6.12.6
      element-resize-event: 3.0.6
      eventemitter3: 4.0.7
      gl-matrix: 3.4.3
      inversify: 5.1.1
      inversify-inject-decorators: 3.1.0
      l7-tiny-sdf: 0.0.3
      l7hammerjs: 0.0.7
      lodash: 4.17.21
      reflect-metadata: 0.1.13
      viewport-mercator-project: 6.2.3
    dev: false

  /@antv/l7-district@2.3.12(@antv/l7-maps@2.9.27)(@antv/l7@2.9.27)(prop-types@15.8.1)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-kggpT7jRKh9lZdoS382g7n/MjJVOFolq2ebApT5YDGPGa2k/lI9FMKnQi/ZIGgiS+9G9VElmBvC5XOfD+UwNAQ==}
    peerDependencies:
      '@antv/l7': ^2.3.0
      '@antv/l7-maps': ^2.3.0
    dependencies:
      '@antv/l7': 2.9.27
      '@antv/l7-maps': 2.9.27
      '@babel/runtime': 7.25.6
      '@turf/circle': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/midpoint': 5.1.5
      '@turf/turf': 5.1.6
      '@types/mapbox-gl': 1.13.6
      eventemitter3: 4.0.7
      geobuf: 3.0.2
      lodash: 4.17.21
      pbf: 3.2.1
      react-dat-gui: 4.1.1(prop-types@15.8.1)(react-dom@16.14.0)(react@16.14.0)
      simplify-geojson: 1.0.5
    transitivePeerDependencies:
      - prop-types
      - react
      - react-dom
    dev: false

  /@antv/l7-layers@2.22.4:
    resolution: {integrity: sha512-BAGUpjxpDDh9zqWBwyP/IegOsgJfs9L7W2lUdddJ766k8qC6kvzYna+H0RPO39Nx1w79f3oKBUeQW5ESEJK6rA==}
    dependencies:
      '@antv/async-hook': 2.2.9
      '@antv/l7-core': 2.22.4
      '@antv/l7-maps': 2.22.4
      '@antv/l7-source': 2.22.4
      '@antv/l7-utils': 2.22.4
      '@babel/runtime': 7.25.6
      '@mapbox/martini': 0.2.0
      '@turf/clone': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/polygon-to-line': 6.5.0
      '@turf/union': 6.5.0
      d3-array: 2.12.1
      d3-color: 1.4.1
      d3-interpolate: 1.4.0
      d3-scale: 2.2.2
      earcut: 2.2.4
      eventemitter3: 4.0.7
      extrude-polyline: 1.0.6
      gl-matrix: 3.4.3
      gl-vec2: 1.3.0
      polyline-miter-util: 1.0.1
    dev: false

  /@antv/l7-layers@2.9.27:
    resolution: {integrity: sha512-bVJe8RdXW9xRKn4l6IzqOZoCHUlGAj8d96JASMoj1SRZgBIlCczw/a15SfQpW2lmsTkafBeUCVxDO5JUqAFCSA==}
    dependencies:
      '@antv/async-hook': 2.2.9
      '@antv/l7-core': 2.9.27
      '@antv/l7-maps': 2.9.27
      '@antv/l7-source': 2.9.27
      '@antv/l7-utils': 2.9.27
      '@babel/runtime': 7.25.6
      '@mapbox/martini': 0.2.0
      '@turf/clone': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/polygon-to-line': 6.5.0
      '@turf/union': 6.5.0
      d3-array: 1.2.4
      d3-color: 1.4.1
      d3-interpolate: 1.4.0
      d3-scale: 2.2.2
      earcut: 2.2.4
      eventemitter3: 4.0.7
      extrude-polyline: 1.0.6
      gl-matrix: 3.4.3
      gl-vec2: 1.3.0
      inversify: 5.1.1
      lodash: 4.17.21
      polyline-miter-util: 1.0.1
      reflect-metadata: 0.1.13
    dev: false

  /@antv/l7-map@2.22.4:
    resolution: {integrity: sha512-PMQtVvCIB+/27ykI9uTSJjYkQDm/wVsATbp3tXbPBsIz0da3GgAPbNPMDDnp0nA7PnKwbyitIWxLQ2fplQj/Ww==}
    dependencies:
      '@antv/l7-utils': 2.22.4
      '@babel/runtime': 7.25.6
      '@mapbox/point-geometry': 0.1.0
      '@mapbox/unitbezier': 0.0.1
      eventemitter3: 4.0.7
      gl-matrix: 3.4.3
    dev: false

  /@antv/l7-map@2.9.27:
    resolution: {integrity: sha512-aFfNIjc3kWMWeMm5OGF3NTXfPi6YAV1R5lOeiChcdbMeTHA/VcuyVIegXLHoGzOOA39+YUfsncHqj8M3XXuwVA==}
    dependencies:
      '@antv/l7-utils': 2.9.27
      '@babel/runtime': 7.25.6
      '@mapbox/point-geometry': 0.1.0
      '@mapbox/unitbezier': 0.0.0
      eventemitter3: 4.0.7
      gl-matrix: 3.4.3
      lodash: 4.17.21
    dev: false

  /@antv/l7-maps@2.22.4:
    resolution: {integrity: sha512-/0DquIc2otk0Dg4mFuYfj/YzNPkSkjOeYpO/4Ucf1FcCXUKomjDuTUtPbQScDLuiR3wTQTpYoGYPBVLFtyTQTQ==}
    dependencies:
      '@amap/amap-jsapi-loader': 1.0.1
      '@antv/l7-core': 2.22.4
      '@antv/l7-map': 2.22.4
      '@antv/l7-utils': 2.22.4
      '@babel/runtime': 7.25.6
      eventemitter3: 4.0.7
      gl-matrix: 3.4.3
      mapbox-gl: 1.13.3
      maplibre-gl: 3.6.2
      pmtiles: 2.11.0
      viewport-mercator-project: 6.2.3
    dev: false

  /@antv/l7-maps@2.9.27:
    resolution: {integrity: sha512-NG/TfZ8z7s+ZhSjEbVMDJft/5eMvgRs7kj9kbL5Re3uVyn5NqJDWa1eyK3nGY4LB/UrBMUhuR6azJpLBX3VZHA==}
    dependencies:
      '@amap/amap-jsapi-loader': 0.0.3
      '@antv/l7-core': 2.9.27
      '@antv/l7-map': 2.9.27
      '@antv/l7-utils': 2.9.27
      '@babel/runtime': 7.25.6
      '@types/amap-js-api': 1.4.16
      '@types/mapbox-gl': 1.13.6
      gl-matrix: 3.4.3
      inversify: 5.1.1
      mapbox-gl: 1.13.3
      reflect-metadata: 0.1.13
      viewport-mercator-project: 6.2.3
    dev: false

  /@antv/l7-renderer@2.22.4:
    resolution: {integrity: sha512-ewE9gW3w+DQA6bTSmDQYkPM4rGoET/aoEJIHSnqqgODAB3+Y8u6pKq8AJQo3zf+32JO6fhItPZmm8z+7hH1qKg==}
    dependencies:
      '@antv/g-device-api': 1.6.13
      '@antv/l7-core': 2.22.4
      '@antv/l7-utils': 2.22.4
      '@babel/runtime': 7.25.6
      regl: 1.6.1
    dev: false

  /@antv/l7-renderer@2.9.27:
    resolution: {integrity: sha512-EAFIeNaPkIf9UHTRkQZx5DG3bxahY6qJslV7qbjuJwkK2/Xzt4z7Z6L2r6yo05RB4JRFL52RuhlnkIKcWrZBFQ==}
    dependencies:
      '@antv/l7-core': 2.9.27
      '@antv/l7-utils': 2.9.27
      '@babel/runtime': 7.25.6
      inversify: 5.1.1
      l7regl: 0.0.20
      lodash: 4.17.21
      reflect-metadata: 0.1.13
    dev: false

  /@antv/l7-scene@2.22.4:
    resolution: {integrity: sha512-wU5nAtxQhRgHyV4Q9d+u/QDKkdY1dKP35mdoqTMKEWrwixCxFHmMa5E4K3zmu02buN7PWDuNRZp6zAGKGYAAsg==}
    dependencies:
      '@antv/l7-component': 2.22.4
      '@antv/l7-core': 2.22.4
      '@antv/l7-layers': 2.22.4
      '@antv/l7-maps': 2.22.4
      '@antv/l7-renderer': 2.22.4
      '@antv/l7-utils': 2.22.4
      '@babel/runtime': 7.25.6
      eventemitter3: 4.0.7
    dev: false

  /@antv/l7-scene@2.9.27:
    resolution: {integrity: sha512-KR92sZ30Fj7wXUIedYKMHXSDLNW501dZeI+83N5IdRUbS8ahCNocIIsdyEocUA01r5cfBkJD3FZx7J2vl+asyw==}
    dependencies:
      '@antv/l7-component': 2.9.27
      '@antv/l7-core': 2.9.27
      '@antv/l7-layers': 2.9.27
      '@antv/l7-maps': 2.9.27
      '@antv/l7-renderer': 2.9.27
      '@antv/l7-utils': 2.9.27
      '@babel/runtime': 7.25.6
      inversify: 5.1.1
      mapbox-gl: 1.13.3
      reflect-metadata: 0.1.13
    dev: false

  /@antv/l7-source@2.22.4:
    resolution: {integrity: sha512-BBpqXSmKXwdo0rQdjL5L4V6oJDZ/oQY/wVqfMsU0cxg53BPqWw/yfEmWOfMFVHO7aSxu9FBOW+MclkL47jD51A==}
    dependencies:
      '@antv/async-hook': 2.2.9
      '@antv/l7-core': 2.22.4
      '@antv/l7-utils': 2.22.4
      '@babel/runtime': 7.25.6
      '@mapbox/geojson-rewind': 0.5.2
      '@mapbox/vector-tile': 1.3.1
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      d3-dsv: 1.2.0
      d3-hexbin: 0.2.2
      eventemitter3: 4.0.7
      geojson-vt: 3.2.1
      pbf: 3.2.1
      supercluster: 7.1.5
    dev: false

  /@antv/l7-source@2.9.27:
    resolution: {integrity: sha512-7295IwVU9K3vsVpuT/pGuLn1S42Af6ZuQX7BsTaH+//kRXKMlFebIVDnb6KvvwSRAaNBTqtVlMJA0RjnPDAtcA==}
    dependencies:
      '@antv/async-hook': 2.2.9
      '@antv/l7-core': 2.9.27
      '@antv/l7-utils': 2.9.27
      '@babel/runtime': 7.25.6
      '@mapbox/geojson-rewind': 0.5.2
      '@mapbox/vector-tile': 1.3.1
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      d3-dsv: 1.2.0
      d3-hexbin: 0.2.2
      eventemitter3: 4.0.7
      geojson-vt: 3.2.1
      inversify: 5.1.1
      lodash: 4.17.21
      pbf: 3.2.1
      reflect-metadata: 0.1.13
      supercluster: 7.1.5
    dev: false

  /@antv/l7-utils@2.22.4:
    resolution: {integrity: sha512-tvmlwoyAulPaXMnaJVtp+Tq26atZ3fFbEUMOXVolzQJvV4Wi44sJGjlUlJ9WnC7J9tZGEkSCH7dailCa25OC8Q==}
    dependencies:
      '@babel/runtime': 7.25.6
      '@turf/bbox': 6.5.0
      '@turf/bbox-polygon': 6.5.0
      '@turf/helpers': 6.5.0
      d3-color: 1.4.1
      earcut: 2.2.4
      eventemitter3: 4.0.7
      gl-matrix: 3.4.3
      lodash: 4.17.21
      web-worker-helper: 0.0.3
    dev: false

  /@antv/l7-utils@2.9.27:
    resolution: {integrity: sha512-hyt5xl36Tlna8LQOGczKXcXsLddsz21wablLjqjZlSCIN7UGMW8mr7ya0HjsYcNLdoOYEgsxHdqoQfvjkRET6g==}
    dependencies:
      '@babel/runtime': 7.25.6
      '@turf/bbox-polygon': 6.5.0
      '@turf/helpers': 6.5.0
      d3-color: 1.4.1
      earcut: 2.2.4
      eventemitter3: 4.0.7
      gl-matrix: 3.4.3
      web-worker-helper: 0.0.3
    dev: false

  /@antv/l7@2.22.4:
    resolution: {integrity: sha512-MtRAnQo6bbHg2fbPxCK+cJubOcrvFyNmaYdzyFrz5WV38Y4SQsL1PUlJ5905nxe0bh/Jev6+vYUsef+K/CZemA==}
    dependencies:
      '@antv/l7-component': 2.22.4
      '@antv/l7-core': 2.22.4
      '@antv/l7-layers': 2.22.4
      '@antv/l7-maps': 2.22.4
      '@antv/l7-scene': 2.22.4
      '@antv/l7-source': 2.22.4
      '@antv/l7-utils': 2.22.4
      '@babel/runtime': 7.25.6
    dev: false

  /@antv/l7@2.9.27:
    resolution: {integrity: sha512-5jiS1IiJDX09cYGyCLEQepWf8cq4oPYVrhJEniXDEKfUAsB1gQypnzgUb5JVlgvLKCbC7lMt0Q2RkKkTLPguBw==}
    dependencies:
      '@antv/l7-component': 2.9.27
      '@antv/l7-core': 2.9.27
      '@antv/l7-layers': 2.9.27
      '@antv/l7-maps': 2.9.27
      '@antv/l7-scene': 2.9.27
      '@antv/l7-source': 2.9.27
      '@antv/l7-utils': 2.9.27
      '@babel/runtime': 7.25.6
    dev: false

  /@antv/l7plot-component@0.0.10:
    resolution: {integrity: sha512-jnlg2Qm4tcUUuei+7fVk/HwRqhgxWnfdawOdB3C2u7YJqhkpk2mb7FPY+KGeZd+pqEQcAJTFi/1NeKpnZcP8kg==}
    dependencies:
      '@antv/dom-util': 2.0.4
      '@antv/util': 2.0.17
    dev: false

  /@antv/l7plot@0.5.5(@antv/l7@2.22.4):
    resolution: {integrity: sha512-x6gJx3vE/rkq/2SSJWGEv8c+2KuP89aldvDARPoOc8n1WUynjCNnie9vGzSCb6gja8EbOd0Knwhj7b3Hdk1bCg==}
    peerDependencies:
      '@antv/l7': ^2.11.5
    dependencies:
      '@antv/event-emitter': 0.1.3
      '@antv/l7': 2.22.4
      '@antv/l7plot-component': 0.0.10
      '@antv/util': 2.0.17
      lodash-es: 4.17.21
      topojson-client: 3.1.0
    dev: false

  /@antv/layout@0.1.31:
    resolution: {integrity: sha512-iz9i19dOJGiZr5xBWI5sfG+2K3QVMNAGOBrbjWKH2RGLvGpf2TSFySidhz0siDrcQA46cDsjLmGstezQdgeGzA==}
    dependencies:
      '@antv/g-webgpu': 0.5.5
      '@dagrejs/graphlib': 2.1.4
      d3-force: 2.1.1
      ml-matrix: 6.10.4
    dev: false

  /@antv/layout@0.3.25:
    resolution: {integrity: sha512-d29Aw1PXoAavMRZy7iTB9L5rMBeChFEX0BJ9ELP4TI35ySdCu07YbmPo9ju9OH/6sG2/NB3o85Ayxrre3iwX/g==}
    dependencies:
      '@antv/g-webgpu': 0.7.2
      '@antv/graphlib': 1.2.0
      '@antv/util': 3.3.4
      d3-force: 2.1.1
      d3-quadtree: 2.0.0
      dagre-compound: 0.0.11
      ml-matrix: 6.5.0
    transitivePeerDependencies:
      - dagre
    dev: false

  /@antv/layout@0.3.25(dagre@0.8.5):
    resolution: {integrity: sha512-d29Aw1PXoAavMRZy7iTB9L5rMBeChFEX0BJ9ELP4TI35ySdCu07YbmPo9ju9OH/6sG2/NB3o85Ayxrre3iwX/g==}
    dependencies:
      '@antv/g-webgpu': 0.7.2
      '@antv/graphlib': 1.2.0
      '@antv/util': 3.3.4
      d3-force: 2.1.1
      d3-quadtree: 2.0.0
      dagre-compound: 0.0.11(dagre@0.8.5)
      ml-matrix: 6.5.0
    transitivePeerDependencies:
      - dagre
    dev: false

  /@antv/matrix-util@3.0.4:
    resolution: {integrity: sha512-BAPyu6dUliHcQ7fm9hZSGKqkwcjEDVLVAstlHULLvcMZvANHeLXgHEgV7JqcAV/GIhIz8aZChIlzM1ZboiXpYQ==}
    dependencies:
      '@antv/util': 2.0.17
      gl-matrix: 3.4.3
      tslib: 2.8.1
    dev: false

  /@antv/matrix-util@3.1.0-beta.3:
    resolution: {integrity: sha512-W2R6Za3A6CmG51Y/4jZUM/tFgYSq7vTqJL1VD9dKrvwxS4sE0ZcXINtkp55CdyBwJ6Cwm8pfoRpnD4FnHahN0A==}
    dependencies:
      '@antv/util': 2.0.17
      gl-matrix: 3.4.3
      tslib: 2.8.1
    dev: false

  /@antv/path-util@2.0.15:
    resolution: {integrity: sha512-R2VLZ5C8PLPtr3VciNyxtjKqJ0XlANzpFb5sE9GE61UQqSRuSVSzIakMxjEPrpqbgc+s+y8i+fmc89Snu7qbNw==}
    dependencies:
      '@antv/matrix-util': 3.0.4
      '@antv/util': 2.0.17
      tslib: 2.8.1
    dev: false

  /@antv/path-util@3.0.1:
    resolution: {integrity: sha512-tpvAzMpF9Qm6ik2YSMqICNU5tco5POOW7S4XoxZAI/B0L26adU+Md/SmO0BBo2SpuywKvzPH3hPT3xmoyhr04Q==}
    dependencies:
      gl-matrix: 3.4.3
      lodash-es: 4.17.21
      tslib: 2.8.1
    dev: false

  /@antv/scale@0.3.18:
    resolution: {integrity: sha512-GHwE6Lo7S/Q5fgaLPaCsW+CH+3zl4aXpnN1skOiEY0Ue9/u+s2EySv6aDXYkAqs//i0uilMDD/0/4n8caX9U9w==}
    dependencies:
      '@antv/util': 2.0.17
      fecha: 4.2.3
      tslib: 2.8.1
    dev: false

  /@antv/util@2.0.17:
    resolution: {integrity: sha512-o6I9hi5CIUvLGDhth0RxNSFDRwXeywmt6ExR4+RmVAzIi48ps6HUy+svxOCayvrPBN37uE6TAc2KDofRo0nK9Q==}
    dependencies:
      csstype: 3.1.2
      tslib: 2.8.1
    dev: false

  /@antv/util@3.3.4:
    resolution: {integrity: sha512-NGRjPCPje8GIC14Ye7sjebamFIjxoZ+mCIqfXz6wD/M6fA9SgJivzmLB3Ry01Wq8PI36oOVv9BwrAGV1JD8vjA==}
    dependencies:
      fast-deep-equal: 3.1.3
      gl-matrix: 3.4.3
      tslib: 2.8.1
    dev: false

  /@antv/x6-common@2.0.17:
    resolution: {integrity: sha512-37g7vmRkNdYzZPdwjaMSZEGv/MMH0S4r70/Jwoab1mioycmuIBN73iyziX8m56BvJSDucZ3J/6DU07otWqzS6A==}
    dependencies:
      lodash-es: 4.17.21
      utility-types: 3.10.0
    dev: false

  /@antv/x6-geometry@2.0.5:
    resolution: {integrity: sha512-MId6riEQkxphBpVeTcL4ZNXL4lScyvDEPLyIafvWMcWNTGK0jgkK7N20XSzqt8ltJb0mGUso5s56mrk8ysHu2A==}
    dev: false

  /@antv/x6-plugin-clipboard@2.1.6(@antv/x6@2.18.1):
    resolution: {integrity: sha512-roZPLnZx6PK8MBvee0QMo90fz/TXeF0WNe4EGin2NBq5M1I5XTWrYvA6N2XVIiWAAI67gjQeEE8TpkL7f8QdqA==}
    peerDependencies:
      '@antv/x6': ^2.x
    dependencies:
      '@antv/x6': 2.18.1
    dev: false

  /@antv/x6-plugin-dnd@2.1.1(@antv/x6@2.18.1):
    resolution: {integrity: sha512-v0szzik1RkadPDn4Qi5mOSaB2AeI78D40/YuCYbPVzplG+HydGsHwO3MLTgJPQ+R5n0eM0W5F850p1VfTOHR7g==}
    peerDependencies:
      '@antv/x6': ^2.x
    dependencies:
      '@antv/x6': 2.18.1
    dev: false

  /@antv/x6-plugin-history@2.2.4(@antv/x6@2.18.1):
    resolution: {integrity: sha512-9gHHvEW4Fla+1hxUV49zNgJyIMoV9CjVM52MrFgAJcvyRn1Kvxz4MfxiKlG+DEZUs+/zvfjl9pS6gJOd8laRkg==}
    peerDependencies:
      '@antv/x6': ^2.x
    dependencies:
      '@antv/x6': 2.18.1
    dev: false

  /@antv/x6-plugin-keyboard@2.2.3(@antv/x6@2.18.1):
    resolution: {integrity: sha512-pnCIC+mDyKKfkcDyLePfGxKVIqXBcldTgannITkHC1kc0IafRS1GMvzpvuDGrM5haRYd6Nwz8kjkJyHkJE4GPA==}
    peerDependencies:
      '@antv/x6': ^2.x
    dependencies:
      '@antv/x6': 2.18.1
      mousetrap: 1.6.5
    dev: false

  /@antv/x6-plugin-selection@2.2.2(@antv/x6@2.18.1):
    resolution: {integrity: sha512-s2gtR9Onlhr7HOHqyqg0d+4sG76JCcQEbvrZZ64XmSChlvieIPlC3YtH4dg1KMNhYIuBmBmpSum6S0eVTEiPQw==}
    peerDependencies:
      '@antv/x6': ^2.x
    dependencies:
      '@antv/x6': 2.18.1
    dev: false

  /@antv/x6-plugin-snapline@2.1.7(@antv/x6@2.18.1):
    resolution: {integrity: sha512-AsysoCb9vES0U2USNhEpYuO/W8I0aYfkhlbee5Kt4NYiMfQfZKQyqW/YjDVaS2pm38C1NKu1LdPVk/BBr4CasA==}
    peerDependencies:
      '@antv/x6': ^2.x
    dependencies:
      '@antv/x6': 2.18.1
    dev: false

  /@antv/x6-plugin-stencil@2.1.5(@antv/x6@2.18.1):
    resolution: {integrity: sha512-q7wx7XRMFkUKPv3WsHkvZda6O1GW+6q6H/+c1lcrwlQoEKOFv1Djc4Hu2J4SGhV2z98P2JLfVJiT5m7YoOoCHw==}
    peerDependencies:
      '@antv/x6': ^2.x
    dependencies:
      '@antv/x6': 2.18.1
      '@antv/x6-plugin-dnd': 2.1.1(@antv/x6@2.18.1)
    dev: false

  /@antv/x6-plugin-transform@2.1.8(@antv/x6@2.18.1):
    resolution: {integrity: sha512-GvJuiJ4BKp0H7+qx3R1I+Vzbw5gXp9+oByXo/WyVxE3urOC7LC5sqnaDfIjyYMN6ROLPYPZraLSeSyYBgMgcDw==}
    peerDependencies:
      '@antv/x6': ^2.x
    dependencies:
      '@antv/x6': 2.18.1
    dev: false

  /@antv/x6-react-components@1.1.20(antd@4.24.9)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-HpQqjPCUo+jfcbfW2sr9oxuXMCxWnXxWvE8jXKJzvrlMNZ3kgfxNqMCRxwGi2QTCxLB3g/KYi5/n8kze8ui1/Q==}
    peerDependencies:
      antd: '>=4.4.2 || >=5.0.0-beta.0'
      react: '>=16.8.6 || >=17.0.0'
      react-dom: '>=16.8.6 || >=17.0.0'
    dependencies:
      antd: 4.24.9(react-dom@16.14.0)(react@16.14.0)
      clamp: 1.0.1
      classnames: 2.5.1
      rc-dropdown: 3.6.2(react-dom@16.14.0)(react@16.14.0)
      rc-util: 4.21.1
      react: 16.14.0
      react-color: 2.17.1(react@16.14.0)
      react-dom: 16.14.0(react@16.14.0)
      react-resize-detector: 7.1.2(react-dom@16.14.0)(react@16.14.0)
      ua-parser-js: 0.7.35
    dev: false

  /@antv/x6-react-shape@1.6.4(@antv/x6@1.34.14)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-xjvEV0FuH/AC+MXfgliEroPzMzdPmx3hqiWbIaXq36BasghXUNutlyCKiDmT6DX0bL1IxJsKJGESV0QQBcMlow==}
    peerDependencies:
      '@antv/x6': ^1.x
      react: '>=16.8.6 || >=17.0.0'
      react-dom: '>=16.8.6 || >=17.0.0'
    dependencies:
      '@antv/x6': 1.34.14
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /@antv/x6-react-shape@2.0.8(@antv/x6@2.18.1)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-mOO3BAN5xxKAdfJUU5fy1Rb/sv+3o85EgKWM+rs5LCOy+EIqG2pzXB/IuqB3Q5m1MCweJ3BuDRrzBqnVv7yNjg==}
    peerDependencies:
      '@antv/x6': ^2.x
      react: '>=16.8.6 || >=17.0.0 || >=18.0.0'
      react-dom: '>=16.8.6 || >=17.0.0 || >= 18.0.0'
    dependencies:
      '@antv/x6': 2.18.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /@antv/x6@1.34.14:
    resolution: {integrity: sha512-S3FPEVOHczmfneMSTWD3J+hvu6R2XxuEhAfD+Hhs3h1xkWFOmSAoDOIuPF0gzkQcPr0WQNEGnQ5hgfATCxiPpQ==}
    dependencies:
      csstype: 3.1.2
      jquery: 3.7.1
      jquery-mousewheel: 3.1.13
      lodash-es: 4.17.21
      mousetrap: 1.6.5
      utility-types: 3.10.0
    dev: false

  /@antv/x6@2.18.1:
    resolution: {integrity: sha512-FkWdbLOpN9J7dfJ+kiBxzowSx2N6syBily13NMVdMs+wqC6Eo5sLXWCZjQHateTFWgFw7ZGi2y9o3Pmdov1sXw==}
    dependencies:
      '@antv/x6-common': 2.0.17
      '@antv/x6-geometry': 2.0.5
      utility-types: 3.10.0
    dev: false

  /@antv/xflow-core@1.0.52(@ant-design/icons@4.8.1)(@antv/x6-react-shape@2.0.8)(@antv/x6@2.18.1)(antd@4.24.9)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-dRcEdRKWrZUPtQnd2TepDtcYuxC3nZeGieH2lwIOjmj06sQDvDO4TGhQHDSKXjY0FELa+MWmT59BjkJ9jbpCxw==}
    peerDependencies:
      '@ant-design/icons': ^4.6.0
      '@antv/x6': ^1.30.1
      '@antv/x6-react-components': ^1.1.15
      '@antv/x6-react-shape': ^1.2.5
      antd: ^4.6.3
      lodash: ^4.17.20
      react: ^16.8.0  || ^17.0.0
      react-dom: ^16.8.0  || ^17.0.0
    dependencies:
      '@ant-design/icons': 4.8.1(react-dom@16.14.0)(react@16.14.0)
      '@antv/x6': 2.18.1
      '@antv/x6-react-shape': 2.0.8(@antv/x6@2.18.1)(react-dom@16.14.0)(react@16.14.0)
      '@antv/xflow-hook': 1.0.52
      antd: 4.24.9(react-dom@16.14.0)(react@16.14.0)
      classnames: 2.5.1
      immer: 9.0.21
      lodash: 4.17.21
      mana-common: 0.3.2
      mana-syringe: 0.2.2
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      reflect-metadata: 0.1.13
      rxjs: 6.6.7
    dev: false

  /@antv/xflow-core@1.0.55(@ant-design/icons@4.8.1)(@antv/x6-react-components@1.1.20)(@antv/x6-react-shape@1.6.4)(@antv/x6@1.34.14)(antd@4.24.9)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-FhjTstp/ZdO6mj8ERARJ6M9PWgikXvOpWwMTLCjc+i8vS4O3t4XL/fIu16MqJNuNdqfETcjJluHd06c32MTB/w==}
    peerDependencies:
      '@ant-design/icons': ^4.6.0
      '@antv/x6': ^1.30.1
      '@antv/x6-react-components': ^1.1.15
      '@antv/x6-react-shape': ^1.2.5
      antd: ^4.6.3
      lodash: ^4.17.20
      react: ^16.8.0  || ^17.0.0
      react-dom: ^16.8.0  || ^17.0.0
    dependencies:
      '@ant-design/icons': 4.8.1(react-dom@16.14.0)(react@16.14.0)
      '@antv/x6': 1.34.14
      '@antv/x6-react-components': 1.1.20(antd@4.24.9)(react-dom@16.14.0)(react@16.14.0)
      '@antv/x6-react-shape': 1.6.4(@antv/x6@1.34.14)(react-dom@16.14.0)(react@16.14.0)
      '@antv/xflow-hook': 1.0.55
      antd: 4.24.9(react-dom@16.14.0)(react@16.14.0)
      classnames: 2.5.1
      immer: 9.0.21
      lodash: 4.17.21
      mana-common: 0.3.2
      mana-syringe: 0.2.2
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      reflect-metadata: 0.1.13
      rxjs: 6.6.7
    dev: false

  /@antv/xflow-extension@1.0.52(@ant-design/icons@4.8.1)(@antv/x6-react-shape@2.0.8)(@antv/x6@2.18.1)(antd@4.24.9)(classnames@2.5.1)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-Aw73Ju+CcdMFC+BA+J4MaWalNwoT+GyLvU1lBnjhClu4l/XqQciw3z11+e/NAfdXW4ii2jXLm9PySa/6+xaFgg==}
    peerDependencies:
      '@ant-design/icons': ^4.6.0
      '@antv/x6': ^1.30.1
      '@antv/x6-react-components': ^1.1.15
      '@antv/x6-react-shape': ^1.2.5
      antd: ^4.6.3
      classnames: ^2.2.6
      react: ^16.8.0  || ^17.0.0
      react-dom: ^16.8.0  || ^17.0.0
    dependencies:
      '@ant-design/icons': 4.8.1(react-dom@16.14.0)(react@16.14.0)
      '@antv/x6': 2.18.1
      '@antv/x6-react-shape': 2.0.8(@antv/x6@2.18.1)(react-dom@16.14.0)(react@16.14.0)
      '@antv/xflow-core': 1.0.52(@ant-design/icons@4.8.1)(@antv/x6-react-shape@2.0.8)(@antv/x6@2.18.1)(antd@4.24.9)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0)
      '@antv/xflow-hook': 1.0.52
      antd: 4.24.9(react-dom@16.14.0)(react@16.14.0)
      classnames: 2.5.1
      mana-syringe: 0.2.2
      moment: 2.29.4
      rc-field-form: 1.27.4(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-color: 2.17.1(react@16.14.0)
      react-dom: 16.14.0(react@16.14.0)
      reflect-metadata: 0.1.13
    transitivePeerDependencies:
      - lodash
    dev: false

  /@antv/xflow-extension@1.0.55(@ant-design/icons@4.8.1)(@antv/x6-react-components@1.1.20)(@antv/x6-react-shape@1.6.4)(@antv/x6@1.34.14)(antd@4.24.9)(classnames@2.5.1)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-iHRfO/ae4/9xvC5xVFmA9eIFg813DQa06hTgTdxLlt0Aojnfa5VnsGrIrPQoDDQovCXG8SavMDlnLZUpa491GQ==}
    peerDependencies:
      '@ant-design/icons': ^4.6.0
      '@antv/x6': ^1.30.1
      '@antv/x6-react-components': ^1.1.15
      '@antv/x6-react-shape': ^1.2.5
      antd: ^4.6.3
      classnames: ^2.2.6
      react: ^16.8.0  || ^17.0.0
      react-dom: ^16.8.0  || ^17.0.0
    dependencies:
      '@ant-design/icons': 4.8.1(react-dom@16.14.0)(react@16.14.0)
      '@antv/x6': 1.34.14
      '@antv/x6-react-components': 1.1.20(antd@4.24.9)(react-dom@16.14.0)(react@16.14.0)
      '@antv/x6-react-shape': 1.6.4(@antv/x6@1.34.14)(react-dom@16.14.0)(react@16.14.0)
      '@antv/xflow-core': 1.0.55(@ant-design/icons@4.8.1)(@antv/x6-react-components@1.1.20)(@antv/x6-react-shape@1.6.4)(@antv/x6@1.34.14)(antd@4.24.9)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0)
      '@antv/xflow-hook': 1.0.55
      antd: 4.24.9(react-dom@16.14.0)(react@16.14.0)
      classnames: 2.5.1
      mana-syringe: 0.2.2
      moment: 2.29.4
      rc-field-form: 1.27.4(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-color: 2.17.1(react@16.14.0)
      react-dom: 16.14.0(react@16.14.0)
      reflect-metadata: 0.1.13
    transitivePeerDependencies:
      - lodash
    dev: false

  /@antv/xflow-hook@1.0.52:
    resolution: {integrity: sha512-qyheVO5bwhDjKGSldf3pkneowf8ZCydIIrX4n8LRTFpvVhVF9cPoXG9e6+1LhIFvSEaLAJcYBH4gNqAjRrbUYw==}
    dependencies:
      toposort: 2.0.2
    dev: false

  /@antv/xflow-hook@1.0.55:
    resolution: {integrity: sha512-NVBrezDue4wd32seaIx1OT5WONpZUhwEp+p86QRhPzO3mmb1qhVxbEqznXhtisxZeHitHFS33J7NqeH1sDFUtQ==}
    dependencies:
      toposort: 2.0.2
    dev: false

  /@antv/xflow@1.0.55(@ant-design/icons@4.8.1)(antd@4.24.9)(classnames@2.5.1)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-1LRFZsDZDVuRwEYD0xFt7SGCbHducgXQ4GgPOzmaCMA5FMbQfZVpXhsOG4o/a9X+gKfYdpOELCLqpz7iykyArQ==}
    peerDependencies:
      '@ant-design/icons': ^4.6.0
      antd: ^4.6.3
      lodash: ^4.17.20
      react: ^16.8.0  || ^17.0.0
      react-dom: ^16.8.0  || ^17.0.0
    dependencies:
      '@ant-design/icons': 4.8.1(react-dom@16.14.0)(react@16.14.0)
      '@antv/layout': 0.1.31
      '@antv/x6': 1.34.14
      '@antv/x6-react-components': 1.1.20(antd@4.24.9)(react-dom@16.14.0)(react@16.14.0)
      '@antv/x6-react-shape': 1.6.4(@antv/x6@1.34.14)(react-dom@16.14.0)(react@16.14.0)
      '@antv/xflow-core': 1.0.55(@ant-design/icons@4.8.1)(@antv/x6-react-components@1.1.20)(@antv/x6-react-shape@1.6.4)(@antv/x6@1.34.14)(antd@4.24.9)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0)
      '@antv/xflow-extension': 1.0.55(@ant-design/icons@4.8.1)(@antv/x6-react-components@1.1.20)(@antv/x6-react-shape@1.6.4)(@antv/x6@1.34.14)(antd@4.24.9)(classnames@2.5.1)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0)
      '@antv/xflow-hook': 1.0.55
      antd: 4.24.9(react-dom@16.14.0)(react@16.14.0)
      lodash: 4.17.21
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    transitivePeerDependencies:
      - classnames
    dev: false

  /@babel/code-frame@7.22.10:
    resolution: {integrity: sha512-/KKIMG4UEL35WmI9OlvMhurwtytjvXoFcGNrOvyG9zIzA8YmPjVtIZUf7b05+TPO7G7/GEmLHDaoCgACHl9hhA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.22.10
      chalk: 2.4.2
    dev: true

  /@babel/code-frame@7.22.13:
    resolution: {integrity: sha512-XktuhWlJ5g+3TJXc5upd9Ks1HutSArik6jf2eAjYFyIOf4ej3RN+184cZbzDvbPnuTJIUhPKKJE3cIsYTiAT3w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': 7.22.20
      chalk: 2.4.2
    dev: true

  /@babel/code-frame@7.26.2:
    resolution: {integrity: sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      js-tokens: 4.0.0
      picocolors: 1.1.1
    dev: false

  /@babel/generator@7.22.10:
    resolution: {integrity: sha512-79KIf7YiWjjdZ81JnLujDRApWtl7BxTqWD88+FFdQEIOG8LJ0etDOM7CXuIgGJa55sGOwZVwuEsaLEm0PJ5/+A==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.19
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.19
      jsesc: 2.5.2
    dev: true

  /@babel/generator@7.26.9:
    resolution: {integrity: sha512-kEWdzjOAUMW4hAyrzJ0ZaTOu9OmpyDIQicIh0zg0EEcEkYXZb2TjtBhnHi2ViX7PKwZqF4xwqfAm299/QMP3lg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/parser': 7.26.9
      '@babel/types': 7.26.9
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0
    dev: false

  /@babel/helper-annotate-as-pure@7.25.9:
    resolution: {integrity: sha512-gv7320KBUFJz1RnylIg5WWYPRXKZ884AGkYpgpWW02TH66Dl+HaC1t1CKd0z3R4b6hdYEcmrNZHUmfCP+1u3/g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.26.9
    dev: false

  /@babel/helper-create-class-features-plugin@7.26.9:
    resolution: {integrity: sha512-ubbUqCofvxPRurw5L8WTsCLSkQiVpov4Qx0WMA+jUN+nXBK8ADPlJO1grkFw5CWKC5+sZSOfuGMdX1aI1iT9Sg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/helper-replace-supers': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/traverse': 7.26.9
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-environment-visitor@7.22.20:
    resolution: {integrity: sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-function-name@7.22.5:
    resolution: {integrity: sha512-wtHSq6jMRE3uF2otvfuD3DIvVhOsSNshQl0Qrd7qC9oQJzHvOL4qQXlQn2916+CXGywIjpGuIkoyZRRxHPiNQQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': 7.22.15
      '@babel/types': 7.22.19
    dev: true

  /@babel/helper-hoist-variables@7.22.5:
    resolution: {integrity: sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.19
    dev: true

  /@babel/helper-member-expression-to-functions@7.25.9:
    resolution: {integrity: sha512-wbfdZ9w5vk0C0oyHqAJbc62+vet5prjj01jjJ8sKn3j9h3MQQlflEdXYvuqRWjHnM12coDEqiC1IRCi0U/EKwQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.26.9
      '@babel/types': 7.26.9
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-optimise-call-expression@7.25.9:
    resolution: {integrity: sha512-FIpuNaz5ow8VyrYcnXQTDRGvV6tTjkNtCK/RYNDXGSLlUD6cBuQTSw43CShGxjvfBTfcUA/r6UhUCbtYqkhcuQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.26.9
    dev: false

  /@babel/helper-plugin-utils@7.26.5:
    resolution: {integrity: sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-replace-supers@7.26.5:
    resolution: {integrity: sha512-bJ6iIVdYX1YooY2X7w1q6VITt+LnUILtNk7zT78ykuwStx8BauCzxvFqFaHjOpW1bVnSUM1PN1f0p5P21wHxvg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/traverse': 7.26.9
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-skip-transparent-expression-wrappers@7.25.9:
    resolution: {integrity: sha512-K4Du3BFa3gvyhzgPcntrkDgZzQaq6uozzcpGbOO1OEJaI+EJdqWIMTLgFgQf6lrfiDFo5FU+BxKepI9RmZqahA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/traverse': 7.26.9
      '@babel/types': 7.26.9
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/helper-split-export-declaration@7.22.6:
    resolution: {integrity: sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': 7.22.19
    dev: true

  /@babel/helper-string-parser@7.22.5:
    resolution: {integrity: sha512-mM4COjgZox8U+JcXQwPijIZLElkgEpO5rsERVDJTc2qfCDfERyob6k5WegS14SX18IIjv+XD+GrqNumY5JRCDw==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-string-parser@7.25.9:
    resolution: {integrity: sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/helper-validator-identifier@7.22.20:
    resolution: {integrity: sha512-Y4OZ+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-validator-identifier@7.22.5:
    resolution: {integrity: sha512-aJXu+6lErq8ltp+JhkJUfk1MTGyuA4v7f3pA+BJ5HLfNC6nAQ0Cpi9uOquUj8Hehg0aUiHzWQbOVJGao6ztBAQ==}
    engines: {node: '>=6.9.0'}
    dev: true

  /@babel/helper-validator-identifier@7.25.9:
    resolution: {integrity: sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==}
    engines: {node: '>=6.9.0'}
    dev: false

  /@babel/highlight@7.22.10:
    resolution: {integrity: sha512-78aUtVcT7MUscr0K5mIEnkwxPE0MaxkR5RxRwuHaQ+JuU5AmTPhY+do2mdzVTnIJJpyBglql2pehuBIWHug+WQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.22.20
      chalk: 2.4.2
      js-tokens: 4.0.0
    dev: true

  /@babel/highlight@7.22.20:
    resolution: {integrity: sha512-dkdMCN3py0+ksCgYmGG8jKeGA/8Tk+gJwSYYlFGxG5lmhfKNoAy004YpLxpS1W2J8m/EK2Ew+yOs9pVRwO89mg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': 7.22.20
      chalk: 2.4.2
      js-tokens: 4.0.0
    dev: true

  /@babel/parser@7.22.11:
    resolution: {integrity: sha512-R5zb8eJIBPJriQtbH/htEQy4k7E2dHWlD2Y2VT07JCzwYZHBxV5ZYtM0UhXSNMT74LyxuM+b1jdL7pSesXbC/g==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.22.19
    dev: true

  /@babel/parser@7.22.16:
    resolution: {integrity: sha512-+gPfKv8UWeKKeJTUxe59+OobVcrYHETCsORl61EmSkmgymguYk/X5bp7GuUIXaFsc6y++v8ZxPsLSSuujqDphA==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.22.19
    dev: true

  /@babel/parser@7.26.9:
    resolution: {integrity: sha512-81NWa1njQblgZbQHxWHpxxCzNsa3ZwvFqpUg7P+NNUU6f3UU2jBEg4OlF/J6rl8+PQGh1q6/zWScd001YwcA5A==}
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': 7.26.9
    dev: false

  /@babel/plugin-proposal-decorators@7.25.9:
    resolution: {integrity: sha512-smkNLL/O1ezy9Nhy4CNosc4Va+1wo5w4gzSZeLe6y6dM4mmHfYOCPolXQPHQxonZCF+ZyebxN9vqOolkYrSn5g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-create-class-features-plugin': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/plugin-syntax-decorators': 7.25.9
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/plugin-syntax-decorators@7.25.9:
    resolution: {integrity: sha512-ryzI0McXUPJnRCvMo4lumIKZUzhYUO/ScI+Mz4YVaTLt04DHNSjEUjKVvbzQjZFLuod/cYEc07mJWhzl6v4DPg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/helper-plugin-utils': 7.26.5
    dev: false

  /@babel/runtime-corejs3@7.22.11:
    resolution: {integrity: sha512-NhfzUbdWbiE6fCFypbWCPu6AR8xre31EOPF7wwAIJEvGQ2avov04eymayWinCuyXmV1b0+jzoXP/HYzzUYdvwg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      core-js-pure: 3.32.1
      regenerator-runtime: 0.14.0
    dev: true

  /@babel/runtime@7.22.11:
    resolution: {integrity: sha512-ee7jVNlWN09+KftVOu9n7S8gQzD/Z6hN/I8VBRXW4P1+Xe7kJGXMwu8vds4aGIMHZnNbdpSWCfZZtinytpcAvA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.14.0

  /@babel/runtime@7.25.6:
    resolution: {integrity: sha512-VBj9MYyDb9tuLq7yzqjgzt6Q+IBQLrGZfdjOekyEirZPHxXWoTSGUTMrpsfi58Up73d13NfYLv8HT9vmznjzhQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: 0.14.0

  /@babel/template@7.22.15:
    resolution: {integrity: sha512-QPErUVm4uyJa60rkI73qneDacvdvzxshT3kksGqlGWYdOTIUOwJ7RDUL8sGqslY1uXWSL6xMFKEXDS3ox2uF0w==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.22.13
      '@babel/parser': 7.22.16
      '@babel/types': 7.22.19
    dev: true

  /@babel/template@7.26.9:
    resolution: {integrity: sha512-qyRplbeIpNZhmzOysF/wFMuP9sctmh2cFzRAZOn1YapxBsE1i9bJIY586R/WBLfLcmcBlM8ROBiQURnnNy+zfA==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/parser': 7.26.9
      '@babel/types': 7.26.9
    dev: false

  /@babel/traverse@7.22.11:
    resolution: {integrity: sha512-mzAenteTfomcB7mfPtyi+4oe5BZ6MXxWcn4CX+h4IRJ+OOGXBrWU6jDQavkQI9Vuc5P+donFabBfFCcmWka9lQ==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.22.10
      '@babel/generator': 7.22.10
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.22.5
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/parser': 7.22.11
      '@babel/types': 7.22.19
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@babel/traverse@7.26.9:
    resolution: {integrity: sha512-ZYW7L+pL8ahU5fXmNbPF+iZFHCv5scFak7MZ9bwaRPLUhHh7QQEMjZUg0HevihoqCM5iSYHN61EyCoZvqC+bxg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.26.9
      '@babel/parser': 7.26.9
      '@babel/template': 7.26.9
      '@babel/types': 7.26.9
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /@babel/types@7.22.11:
    resolution: {integrity: sha512-siazHiGuZRz9aB9NpHy9GOs9xiQPKnMzgdr493iI1M67vRXpnEq8ZOOKzezC5q7zwuQ6sDhdSp4SD9ixKSqKZg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.22.5
      '@babel/helper-validator-identifier': 7.22.5
      to-fast-properties: 2.0.0
    dev: true

  /@babel/types@7.22.19:
    resolution: {integrity: sha512-P7LAw/LbojPzkgp5oznjE6tQEIWbp4PkkfrZDINTro9zgBRtI324/EYsiSI7lhPbpIQ+DCeR2NNmMWANGGfZsg==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.22.5
      '@babel/helper-validator-identifier': 7.22.20
      to-fast-properties: 2.0.0
    dev: true

  /@babel/types@7.26.9:
    resolution: {integrity: sha512-Y3IR1cRnOxOCDvMmNiym7XpXQ93iGDDPHx+Zj+NM+rg0fBaShfQLkg+hKPaZCEvg5N/LeCo4+Rj/i3FuJsIQaw==}
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9
    dev: false

  /@bufbuild/protobuf@2.2.2:
    resolution: {integrity: sha512-UNtPCbrwrenpmrXuRwn9jYpPoweNXj8X5sMvYgsqYyaH8jQ6LfUJSk3dJLnBK+6sfYPrF4iAIo5sd5HQ+tg75A==}
    dev: true

  /@ctrl/tinycolor@3.6.1:
    resolution: {integrity: sha512-SITSV6aIXsuVNV3f3O0f2n/cgyEDWoSqtZMYiAmcsYHydcKrOz3gUxB/iXd/Qf08+IZX4KpgNbvUdMBmWz+kcA==}
    engines: {node: '>=10'}
    dev: false

  /@dagrejs/graphlib@2.1.4:
    resolution: {integrity: sha512-QCg9sL4uhjn468FDEsb/S9hS2xUZSrv/+dApb1Ze5VKO96pTXKNJZ6MGhIpgWkc1TVhbVGH9/7rq/Mf8/jWicw==}
    dependencies:
      lodash: 4.17.21
    dev: false

  /@docusaurus/react-loadable@6.0.0(react@16.14.0):
    resolution: {integrity: sha512-YMMxTUQV/QFSnbgrP3tjDzLHRg7vsbMn8e9HAa8o/1iXoiomo48b7sk/kkmWEuWNDPJVlKSJRB6Y2fHqdJk+SQ==}
    peerDependencies:
      react: '*'
    dependencies:
      '@types/react': 16.14.62
      react: 16.14.0
    dev: false

  /@emoji-mart/data@1.2.1:
    resolution: {integrity: sha512-no2pQMWiBy6gpBEiqGeU77/bFejDqUTRY7KX+0+iur13op3bqUsXdnwoZs6Xb1zbv0gAj5VvS1PWoUUckSr5Dw==}
    dev: false

  /@emoji-mart/react@1.1.1(emoji-mart@5.6.0)(react@16.14.0):
    resolution: {integrity: sha512-NMlFNeWgv1//uPsvLxvGQoIerPuVdXwK/EUek8OOkJ6wVOWPUizRBJU0hDqWZCOROVpfBgCemaC3m6jDOXi03g==}
    peerDependencies:
      emoji-mart: ^5.2
      react: ^16.8 || ^17 || ^18
    dependencies:
      emoji-mart: 5.6.0
      react: 16.14.0
    dev: false

  /@eslint-community/eslint-utils@4.4.0(eslint@6.6.0):
    resolution: {integrity: sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0
    dependencies:
      eslint: 6.6.0
      eslint-visitor-keys: 3.4.3
    dev: true

  /@eslint-community/regexpp@4.8.0:
    resolution: {integrity: sha512-JylOEEzDiOryeUnFbQz+oViCXS0KsvR1mvHkoMiu5+UiBvy+RYX7tzlIIIEstF/gVa2tj9AQXk3dgnxv6KxhFg==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}
    dev: true

  /@icons/material@0.2.4(react@16.14.0):
    resolution: {integrity: sha512-QPcGmICAPbGLGb6F/yNf/KzKqvFx8z5qx3D1yFqVAjoFmXK35EgyW+cJ57Te3CNsmzblwtzakLGFqHPqrfb4Tw==}
    peerDependencies:
      react: '*'
    dependencies:
      react: 16.14.0
    dev: false

  /@isaacs/cliui@8.0.2:
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}
    dependencies:
      string-width: 5.1.2
      string-width-cjs: /string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: /strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: /wrap-ansi@7.0.0
    dev: true

  /@jest/expect-utils@29.6.4:
    resolution: {integrity: sha512-FEhkJhqtvBwgSpiTrocquJCdXPsyvNKcl/n7A3u7X4pVoF4bswm11c9d4AV+kfq2Gpv/mM8x7E7DsRvH+djkrg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      jest-get-type: 29.6.3
    dev: true

  /@jest/schemas@29.6.3:
    resolution: {integrity: sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@sinclair/typebox': 0.27.8
    dev: true

  /@jest/types@29.6.3:
    resolution: {integrity: sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/schemas': 29.6.3
      '@types/istanbul-lib-coverage': 2.0.4
      '@types/istanbul-reports': 3.0.1
      '@types/node': 20.17.19
      '@types/yargs': 17.0.24
      chalk: 4.1.2
    dev: true

  /@jridgewell/gen-mapping@0.3.3:
    resolution: {integrity: sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.19
    dev: true

  /@jridgewell/gen-mapping@0.3.8:
    resolution: {integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==}
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.25
    dev: false

  /@jridgewell/resolve-uri@3.1.1:
    resolution: {integrity: sha512-dSYZh7HhCDtCKm4QakX0xFpsRDqjjtZf/kjI/v3T3Nwt5r8/qz/M19F9ySyOqU94SXBmeG9ttTul+YnR4LOxFA==}
    engines: {node: '>=6.0.0'}

  /@jridgewell/set-array@1.1.2:
    resolution: {integrity: sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==}
    engines: {node: '>=6.0.0'}
    dev: true

  /@jridgewell/set-array@1.2.1:
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}
    dev: false

  /@jridgewell/sourcemap-codec@1.4.15:
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==}

  /@jridgewell/trace-mapping@0.3.19:
    resolution: {integrity: sha512-kf37QtfW+Hwx/buWGMPcR60iF9ziHa6r/CZJIHbmcm4+0qrXiVdxegAH0F6yddEVQ7zdkjcGCgCzUu+BcbhQxw==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.1
      '@jridgewell/sourcemap-codec': 1.4.15
    dev: true

  /@jridgewell/trace-mapping@0.3.25:
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}
    dependencies:
      '@jridgewell/resolve-uri': 3.1.1
      '@jridgewell/sourcemap-codec': 1.4.15
    dev: false

  /@mapbox/geojson-rewind@0.5.2:
    resolution: {integrity: sha512-tJaT+RbYGJYStt7wI3cq4Nl4SXxG8W7JDG5DMJu97V25RnbNg3QtQtf+KD+VLjNpWKYsRvXDNmNrBgEETr1ifA==}
    hasBin: true
    dependencies:
      get-stream: 6.0.1
      minimist: 1.2.8
    dev: false

  /@mapbox/geojson-types@1.0.2:
    resolution: {integrity: sha512-e9EBqHHv3EORHrSfbR9DqecPNn+AmuAoQxV6aL8Xu30bJMJR1o8PZLZzpk1Wq7/NfCbuhmakHTPYRhoqLsXRnw==}
    dev: false

  /@mapbox/jsonlint-lines-primitives@2.0.2:
    resolution: {integrity: sha512-rY0o9A5ECsTQRVhv7tL/OyDpGAoUB4tTvLiW1DSzQGq4bvTPhNw1VpSNjDJc5GFZ2XuyOtSWSVN05qOtcD71qQ==}
    engines: {node: '>= 0.6'}
    dev: false

  /@mapbox/mapbox-gl-supported@1.5.0(mapbox-gl@1.13.3):
    resolution: {integrity: sha512-/PT1P6DNf7vjEEiPkVIRJkvibbqWtqnyGaBz3nfRdcxclNSnSdaLU5tfAgcD7I8Yt5i+L19s406YLl1koLnLbg==}
    peerDependencies:
      mapbox-gl: '>=0.32.1 <2.0.0'
    dependencies:
      mapbox-gl: 1.13.3
    dev: false

  /@mapbox/martini@0.2.0:
    resolution: {integrity: sha512-7hFhtkb0KTLEls+TRw/rWayq5EeHtTaErgm/NskVoXmtgAQu/9D299aeyj6mzAR/6XUnYRp2lU+4IcrYRFjVsQ==}
    dev: false

  /@mapbox/point-geometry@0.1.0:
    resolution: {integrity: sha512-6j56HdLTwWGO0fJPlrZtdU/B13q8Uwmo18Ck2GnGgN9PCFyKTZ3UbXeEdRFh18i9XQ92eH2VdtpJHpBD3aripQ==}
    dev: false

  /@mapbox/tiny-sdf@1.2.5:
    resolution: {integrity: sha512-cD8A/zJlm6fdJOk6DqPUV8mcpyJkRz2x2R+/fYcWDYG3oWbG7/L7Yl/WqQ1VZCjnL9OTIMAn6c+BC5Eru4sQEw==}
    dev: false

  /@mapbox/tiny-sdf@2.0.6:
    resolution: {integrity: sha512-qMqa27TLw+ZQz5Jk+RcwZGH7BQf5G/TrutJhspsca/3SHwmgKQ1iq+d3Jxz5oysPVYTGP6aXxCo5Lk9Er6YBAA==}
    dev: false

  /@mapbox/unitbezier@0.0.0:
    resolution: {integrity: sha512-HPnRdYO0WjFjRTSwO3frz1wKaU649OBFPX3Zo/2WZvuRi6zMiRGui8SnPQiQABgqCf8YikDe5t3HViTVw1WUzA==}
    dev: false

  /@mapbox/unitbezier@0.0.1:
    resolution: {integrity: sha512-nMkuDXFv60aBr9soUG5q+GvZYL+2KZHVvsqFCzqnkGEf46U2fvmytHaEVc1/YZbiLn8X+eR3QzX1+dwDO1lxlw==}
    dev: false

  /@mapbox/vector-tile@1.3.1:
    resolution: {integrity: sha512-MCEddb8u44/xfQ3oD+Srl/tNcQoqTw3goGk2oLsrFxOTc3dUp+kAnby3PvAeeBYSMSjSPD1nd1AJA6W49WnoUw==}
    dependencies:
      '@mapbox/point-geometry': 0.1.0
    dev: false

  /@mapbox/whoots-js@3.1.0:
    resolution: {integrity: sha512-Es6WcD0nO5l+2BOQS4uLfNPYQaNDfbot3X1XUoloz+x0mPDS3eeORZJl06HXjwBG1fOGwCRnzK88LMdxKRrd6Q==}
    engines: {node: '>=6.0.0'}
    dev: false

  /@maplibre/maplibre-gl-style-spec@19.3.3:
    resolution: {integrity: sha512-cOZZOVhDSulgK0meTsTkmNXb1ahVvmTmWmfx9gRBwc6hq98wS9JP35ESIoNq3xqEan+UN+gn8187Z6E4NKhLsw==}
    hasBin: true
    dependencies:
      '@mapbox/jsonlint-lines-primitives': 2.0.2
      '@mapbox/unitbezier': 0.0.1
      json-stringify-pretty-compact: 3.0.0
      minimist: 1.2.8
      rw: 1.3.3
      sort-object: 3.0.3
    dev: false

  /@module-federation/error-codes@0.8.4:
    resolution: {integrity: sha512-55LYmrDdKb4jt+qr8qE8U3al62ZANp3FhfVaNPOaAmdTh0jHdD8M3yf5HKFlr5xVkVO4eV/F/J2NCfpbh+pEXQ==}
    dev: true

  /@module-federation/runtime-tools@0.8.4:
    resolution: {integrity: sha512-fjVOsItJ1u5YY6E9FnS56UDwZgqEQUrWFnouRiPtK123LUuqUI9FH4redZoKWlE1PB0ir1Z3tnqy8eFYzPO38Q==}
    dependencies:
      '@module-federation/runtime': 0.8.4
      '@module-federation/webpack-bundler-runtime': 0.8.4
    dev: true

  /@module-federation/runtime@0.8.4:
    resolution: {integrity: sha512-yZeZ7z2Rx4gv/0E97oLTF3V6N25vglmwXGgoeju/W2YjsFvWzVtCDI7zRRb0mJhU6+jmSM8jP1DeQGbea/AiZQ==}
    dependencies:
      '@module-federation/error-codes': 0.8.4
      '@module-federation/sdk': 0.8.4
    dev: true

  /@module-federation/sdk@0.8.4:
    resolution: {integrity: sha512-waABomIjg/5m1rPDBWYG4KUhS5r7OUUY7S+avpaVIY/tkPWB3ibRDKy2dNLLAMaLKq0u+B1qIdEp4NIWkqhqpg==}
    dependencies:
      isomorphic-rslog: 0.0.6
    dev: true

  /@module-federation/webpack-bundler-runtime@0.8.4:
    resolution: {integrity: sha512-HggROJhvHPUX7uqBD/XlajGygMNM1DG0+4OAkk8MBQe4a18QzrRNzZt6XQbRTSG4OaEoyRWhQHvYD3Yps405tQ==}
    dependencies:
      '@module-federation/runtime': 0.8.4
      '@module-federation/sdk': 0.8.4
    dev: true

  /@nodelib/fs.scandir@2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0
    dev: true

  /@nodelib/fs.stat@2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}
    dev: true

  /@nodelib/fs.walk@1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.15.0
    dev: true

  /@parcel/watcher-android-arm64@2.5.0:
    resolution: {integrity: sha512-qlX4eS28bUcQCdribHkg/herLe+0A9RyYC+mm2PXpncit8z5b3nSqGVzMNR3CmtAOgRutiZ02eIJJgP/b1iEFQ==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    optional: true

  /@parcel/watcher-darwin-arm64@2.5.0:
    resolution: {integrity: sha512-hyZ3TANnzGfLpRA2s/4U1kbw2ZI4qGxaRJbBH2DCSREFfubMswheh8TeiC1sGZ3z2jUf3s37P0BBlrD3sjVTUw==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    optional: true

  /@parcel/watcher-darwin-x64@2.5.0:
    resolution: {integrity: sha512-9rhlwd78saKf18fT869/poydQK8YqlU26TMiNg7AIu7eBp9adqbJZqmdFOsbZ5cnLp5XvRo9wcFmNHgHdWaGYA==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    optional: true

  /@parcel/watcher-freebsd-x64@2.5.0:
    resolution: {integrity: sha512-syvfhZzyM8kErg3VF0xpV8dixJ+RzbUaaGaeb7uDuz0D3FK97/mZ5AJQ3XNnDsXX7KkFNtyQyFrXZzQIcN49Tw==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [freebsd]
    requiresBuild: true
    optional: true

  /@parcel/watcher-linux-arm-glibc@2.5.0:
    resolution: {integrity: sha512-0VQY1K35DQET3dVYWpOaPFecqOT9dbuCfzjxoQyif1Wc574t3kOSkKevULddcR9znz1TcklCE7Ht6NIxjvTqLA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    optional: true

  /@parcel/watcher-linux-arm-musl@2.5.0:
    resolution: {integrity: sha512-6uHywSIzz8+vi2lAzFeltnYbdHsDm3iIB57d4g5oaB9vKwjb6N6dRIgZMujw4nm5r6v9/BQH0noq6DzHrqr2pA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    optional: true

  /@parcel/watcher-linux-arm64-glibc@2.5.0:
    resolution: {integrity: sha512-BfNjXwZKxBy4WibDb/LDCriWSKLz+jJRL3cM/DllnHH5QUyoiUNEp3GmL80ZqxeumoADfCCP19+qiYiC8gUBjA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    optional: true

  /@parcel/watcher-linux-arm64-musl@2.5.0:
    resolution: {integrity: sha512-S1qARKOphxfiBEkwLUbHjCY9BWPdWnW9j7f7Hb2jPplu8UZ3nes7zpPOW9bkLbHRvWM0WDTsjdOTUgW0xLBN1Q==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    optional: true

  /@parcel/watcher-linux-x64-glibc@2.5.0:
    resolution: {integrity: sha512-d9AOkusyXARkFD66S6zlGXyzx5RvY+chTP9Jp0ypSTC9d4lzyRs9ovGf/80VCxjKddcUvnsGwCHWuF2EoPgWjw==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    optional: true

  /@parcel/watcher-linux-x64-musl@2.5.0:
    resolution: {integrity: sha512-iqOC+GoTDoFyk/VYSFHwjHhYrk8bljW6zOhPuhi5t9ulqiYq1togGJB5e3PwYVFFfeVgc6pbz3JdQyDoBszVaA==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    optional: true

  /@parcel/watcher-win32-arm64@2.5.0:
    resolution: {integrity: sha512-twtft1d+JRNkM5YbmexfcH/N4znDtjgysFaV9zvZmmJezQsKpkfLYJ+JFV3uygugK6AtIM2oADPkB2AdhBrNig==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    optional: true

  /@parcel/watcher-win32-ia32@2.5.0:
    resolution: {integrity: sha512-+rgpsNRKwo8A53elqbbHXdOMtY/tAtTzManTWShB5Kk54N8Q9mzNWV7tV+IbGueCbcj826MfWGU3mprWtuf1TA==}
    engines: {node: '>= 10.0.0'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    optional: true

  /@parcel/watcher-win32-x64@2.5.0:
    resolution: {integrity: sha512-lPrxve92zEHdgeff3aiu4gDOIt4u7sJYha6wbdEZDCDUhtjTsOMiaJzG5lMY4GkWH8p0fMmO2Ppq5G5XXG+DQw==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    optional: true

  /@parcel/watcher@2.5.0:
    resolution: {integrity: sha512-i0GV1yJnm2n3Yq1qw6QrUrd/LI9bE8WEBOTtOkpCXHHdyN3TAGgqAK/DAT05z4fq2x04cARXt2pDmjWjL92iTQ==}
    engines: {node: '>= 10.0.0'}
    requiresBuild: true
    dependencies:
      detect-libc: 1.0.3
      is-glob: 4.0.3
      micromatch: 4.0.5
      node-addon-api: 7.1.1
    optionalDependencies:
      '@parcel/watcher-android-arm64': 2.5.0
      '@parcel/watcher-darwin-arm64': 2.5.0
      '@parcel/watcher-darwin-x64': 2.5.0
      '@parcel/watcher-freebsd-x64': 2.5.0
      '@parcel/watcher-linux-arm-glibc': 2.5.0
      '@parcel/watcher-linux-arm-musl': 2.5.0
      '@parcel/watcher-linux-arm64-glibc': 2.5.0
      '@parcel/watcher-linux-arm64-musl': 2.5.0
      '@parcel/watcher-linux-x64-glibc': 2.5.0
      '@parcel/watcher-linux-x64-musl': 2.5.0
      '@parcel/watcher-win32-arm64': 2.5.0
      '@parcel/watcher-win32-ia32': 2.5.0
      '@parcel/watcher-win32-x64': 2.5.0
    optional: true

  /@pkgjs/parseargs@0.11.0:
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}
    requiresBuild: true
    dev: true
    optional: true

  /@probe.gl/env@3.6.0:
    resolution: {integrity: sha512-4tTZYUg/8BICC3Yyb9rOeoKeijKbZHRXBEKObrfPmX4sQmYB15ZOUpoVBhAyJkOYVAM8EkPci6Uw5dLCwx2BEQ==}
    dependencies:
      '@babel/runtime': 7.25.6
    dev: false

  /@probe.gl/log@3.6.0:
    resolution: {integrity: sha512-hjpyenpEvOdowgZ1qMeCJxfRD4JkKdlXz0RC14m42Un62NtOT+GpWyKA4LssT0+xyLULCByRAtG2fzZorpIAcA==}
    dependencies:
      '@babel/runtime': 7.25.6
      '@probe.gl/env': 3.6.0
    dev: false

  /@probe.gl/stats@3.6.0:
    resolution: {integrity: sha512-JdALQXB44OP4kUBN/UrQgzbJe4qokbVF4Y8lkIA8iVCFnjVowWIgkD/z/0QO65yELT54tTrtepw1jScjKB+rhQ==}
    dependencies:
      '@babel/runtime': 7.25.6
    dev: false

  /@rc-component/portal@1.1.2(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-6f813C0IsasTZms08kfA8kPAGxbbkYToa8ALaiDIGGECU4i9hj8Plgbx0sNJDrey3EtHO30hmdaxtT0138xZcg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /@rc-component/trigger@2.2.5(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-F1EJ4KjFpGAHAjuKvOyZB/6IZDkVx0bHl0M4fQM5wXcmm7lgTgVSSnR3bXwdmS6jOJGHOqfDxIJW3WUvwMIXhQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      '@rc-component/portal': 1.1.2(react-dom@16.14.0)(react@16.14.0)
      classnames: 2.5.1
      rc-motion: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-resize-observer: 1.4.0(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /@react-dnd/asap@4.0.1:
    resolution: {integrity: sha512-kLy0PJDDwvwwTXxqTFNAAllPHD73AycE9ypWeln/IguoGBEbvFcPDbCV03G52bEcC5E+YgupBE0VzHGdC8SIXg==}
    dev: false

  /@react-dnd/invariant@2.0.0:
    resolution: {integrity: sha512-xL4RCQBCBDJ+GRwKTFhGUW8GXa4yoDfJrPbLblc3U09ciS+9ZJXJ3Qrcs/x2IODOdIE5kQxvMmE2UKyqUictUw==}
    dev: false

  /@react-dnd/shallowequal@2.0.0:
    resolution: {integrity: sha512-Pc/AFTdwZwEKJxFJvlxrSmGe/di+aAOBn60sremrpLo6VI/6cmiUYNNwlI5KNYttg7uypzA3ILPMPgxB2GYZEg==}
    dev: false

  /@rsbuild/core@1.2.19:
    resolution: {integrity: sha512-k76is4HygmbYYMLG2V1d1yQeurHHC+ZEtGs/nwE11y6HmwSndoFhmjOeQbQ2Ul0b2B8HErksqSMtlCxd37YPPQ==}
    engines: {node: '>=16.7.0'}
    hasBin: true
    dependencies:
      '@rspack/core': 1.2.8(@swc/helpers@0.5.15)
      '@rspack/lite-tapable': 1.0.1
      '@swc/helpers': 0.5.15
      core-js: 3.41.0
      jiti: 2.4.2
    transitivePeerDependencies:
      - '@rspack/tracing'
    dev: true

  /@rsbuild/plugin-react@1.1.1(@rsbuild/core@1.2.19):
    resolution: {integrity: sha512-gkATKrOQauXMMtrYA5jbTQkhmYTE0VXoknPLtVpiXtwDbBUwgX23LFf1XJ51YOwqYpP7g5SfPEMgD2FENtCq0A==}
    peerDependencies:
      '@rsbuild/core': 1.x
    dependencies:
      '@rsbuild/core': 1.2.19
      '@rspack/plugin-react-refresh': 1.0.1(react-refresh@0.16.0)
      react-refresh: 0.16.0
    dev: true

  /@rsbuild/plugin-sass@1.2.2(@rsbuild/core@1.2.19):
    resolution: {integrity: sha512-vznLfxxPXDyFSPYW7JWTYf/6SJMx5DEgKParNd5lXo7FRa1IKsQOrJdf6F3Rm+T7jKoAvnCVXjM2IkxBW2yJSA==}
    peerDependencies:
      '@rsbuild/core': 1.x
    dependencies:
      '@rsbuild/core': 1.2.19
      deepmerge: 4.3.1
      loader-utils: 2.0.4
      postcss: 8.5.3
      reduce-configs: 1.1.0
      sass-embedded: 1.85.1
    dev: true

  /@rspack/binding-darwin-arm64@1.2.8:
    resolution: {integrity: sha512-bDlrlroY3iMlzna/3i1gD6eRmhJW2zRyC3Ov6aR1micshVQ9RteigYZWkjZuQfyC5Z8dCcLUQJVojz+pqp0JXg==}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-darwin-x64@1.2.8:
    resolution: {integrity: sha512-0/qOVbMuzZ+WbtDa4TbH46R4vph/W6MHcXbrXDO+vpdTMFDVJ64DnZXT7aqvGcY+7vTCIGm0GT+6ooR4KaIX8A==}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-linux-arm64-gnu@1.2.8:
    resolution: {integrity: sha512-En/SMl45s19iUVb1/ZDFQvFDxIjnlfk7yqV3drMWWAL5HSgksNejaTIFTO52aoohIBbmwuk5wSGcbU0G0IFiPg==}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-linux-arm64-musl@1.2.8:
    resolution: {integrity: sha512-N1oZsXfJ9VLLcK7p1PS65cxLYQCZ7iqHW2OP6Ew2+hlz/d1hzngxgzrtZMCXFOHXDvTzVu5ff6jGS2v7+zv2tA==}
    cpu: [arm64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-linux-x64-gnu@1.2.8:
    resolution: {integrity: sha512-BdPaepoLKuaVwip4QK/nGqNi1xpbCWSxiycPbKRrGqKgt/QGihxxFgiqr4EpWQVIJNIMy4nCsg4arO0+H1KWGQ==}
    cpu: [x64]
    os: [linux]
    libc: [glibc]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-linux-x64-musl@1.2.8:
    resolution: {integrity: sha512-GFv0Bod268OcXIcjeLoPlK0oz8rClEIxIRFkz+ejhbvfCwRJ+Fd+EKaaKQTBfZQujPqc0h2GctIF25nN5pFTmA==}
    cpu: [x64]
    os: [linux]
    libc: [musl]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-win32-arm64-msvc@1.2.8:
    resolution: {integrity: sha512-aEU+uJdbvJJGrzzAsjbjrPeNbG/bcG8JoXK2kSsUB+/sWHTIkHX0AQ3oX3aV/lcLKgZWrUxLAfLoCXEnIHMEyQ==}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-win32-ia32-msvc@1.2.8:
    resolution: {integrity: sha512-GHYzNOSoiLyG9elLTmMqADJMQzjll+co4irp5AgZ+KHG9EVq0qEHxDqDIJxZnUA15U8JDvCgo6YAo3T0BFEL0Q==}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding-win32-x64-msvc@1.2.8:
    resolution: {integrity: sha512-EigKLhKLH1kfv1e/ZgXuSKlIjkbyneJtiLbNDz7EeEVFGV1XMM6bsCea1sb2WOxsPYiOX4Q5JmR1j1KGrZS/LA==}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /@rspack/binding@1.2.8:
    resolution: {integrity: sha512-T3FMB3N9P1AbSAryfkSRJkPtmeSYs/Gj9zUZoPz1ckPEIcWZmpUOQbJylldjbw5waxtCL1haHNbi0pcSvxiaJw==}
    optionalDependencies:
      '@rspack/binding-darwin-arm64': 1.2.8
      '@rspack/binding-darwin-x64': 1.2.8
      '@rspack/binding-linux-arm64-gnu': 1.2.8
      '@rspack/binding-linux-arm64-musl': 1.2.8
      '@rspack/binding-linux-x64-gnu': 1.2.8
      '@rspack/binding-linux-x64-musl': 1.2.8
      '@rspack/binding-win32-arm64-msvc': 1.2.8
      '@rspack/binding-win32-ia32-msvc': 1.2.8
      '@rspack/binding-win32-x64-msvc': 1.2.8
    dev: true

  /@rspack/core@1.2.8:
    resolution: {integrity: sha512-ppj3uQQtkhgrYDLrUqb33YbpNEZCpAudpfVuOHGsvUrAnu1PijbfJJymoA5ZvUhM+HNMvPI5D1ie97TXyb0UVg==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@rspack/tracing': ^1.x
      '@swc/helpers': '>=0.5.1'
    peerDependenciesMeta:
      '@rspack/tracing':
        optional: true
      '@swc/helpers':
        optional: true
    dependencies:
      '@module-federation/runtime-tools': 0.8.4
      '@rspack/binding': 1.2.8
      '@rspack/lite-tapable': 1.0.1
      caniuse-lite: 1.0.30001706
    dev: true

  /@rspack/core@1.2.8(@swc/helpers@0.5.15):
    resolution: {integrity: sha512-ppj3uQQtkhgrYDLrUqb33YbpNEZCpAudpfVuOHGsvUrAnu1PijbfJJymoA5ZvUhM+HNMvPI5D1ie97TXyb0UVg==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@rspack/tracing': ^1.x
      '@swc/helpers': '>=0.5.1'
    peerDependenciesMeta:
      '@rspack/tracing':
        optional: true
      '@swc/helpers':
        optional: true
    dependencies:
      '@module-federation/runtime-tools': 0.8.4
      '@rspack/binding': 1.2.8
      '@rspack/lite-tapable': 1.0.1
      '@swc/helpers': 0.5.15
      caniuse-lite: 1.0.30001706
    dev: true

  /@rspack/lite-tapable@1.0.1:
    resolution: {integrity: sha512-VynGOEsVw2s8TAlLf/uESfrgfrq2+rcXB1muPJYBWbsm1Oa6r5qVQhjA5ggM6z/coYPrsVMgovl3Ff7Q7OCp1w==}
    engines: {node: '>=16.0.0'}
    dev: true

  /@rspack/plugin-react-refresh@1.0.1(react-refresh@0.16.0):
    resolution: {integrity: sha512-KSBc3bsr3mrAPViv7w9MpE9KEWm6q87EyRXyHlRfJ9PpQ56NbX9KZ7AXo7jPeECb0q5sfpM2PSEf+syBiMgLSw==}
    peerDependencies:
      react-refresh: '>=0.10.0 <1.0.0'
    peerDependenciesMeta:
      react-refresh:
        optional: true
    dependencies:
      error-stack-parser: 2.1.4
      html-entities: 2.5.2
      react-refresh: 0.16.0
    dev: true

  /@rtsao/scc@1.1.0:
    resolution: {integrity: sha512-zt6OdqaDoOnJ1ZYsCYGt9YmWzDXl4vQdKTyJev62gFhRGKdx7mcT54V9KIjg+d2wi9EXsPvAPKe7i7WjfVWB8g==}
    dev: true

  /@sinclair/typebox@0.27.8:
    resolution: {integrity: sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==}
    dev: true

  /@swc/helpers@0.5.15:
    resolution: {integrity: sha512-JQ5TuMi45Owi4/BIMAJBoSQoOJu12oOk/gADqlcUL9JEdHB8vyjUSsxqeNXnmXHjYKMi2WcYtezGEEhqUI/E2g==}
    dependencies:
      tslib: 2.8.1
    dev: true

  /@turf/along@5.1.5:
    resolution: {integrity: sha512-N7BN1xvj6VWMe3UpjQDdVI0j0oY/EZ0bWgOgBXc4DlJ411uEsKCh6iBv0b2MSxQ3YUXEez3oc5FcgO9eVSs7iQ==}
    dependencies:
      '@turf/bearing': 5.1.5
      '@turf/destination': 5.1.5
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/area@5.1.5:
    resolution: {integrity: sha512-lz16gqtvoz+j1jD9y3zj0Z5JnGNd3YfS0h+DQY1EcZymvi75Frm9i5YbEyth0RfxYZeOVufY7YIS3LXbJlI57g==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/bbox-clip@5.1.5:
    resolution: {integrity: sha512-KP64aoTvjcXxWHeM/Hs25vOQUBJgyJi7DlRVEoZofFJiR1kPnmDQrK7Xj+60lAk5cxuqzFnaPPxUk9Q+3v4p1Q==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      lineclip: 1.1.5
    dev: false

  /@turf/bbox-polygon@5.1.5:
    resolution: {integrity: sha512-PKVPF5LABFWZJud8KzzfesLGm5ihiwLbVa54HJjYySe6yqU/cr5q/qcN9TWptynOFhNktG1dr0KXVG0I2FZmfw==}
    dependencies:
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/bbox-polygon@6.5.0:
    resolution: {integrity: sha512-+/r0NyL1lOG3zKZmmf6L8ommU07HliP4dgYToMoTxqzsWzyLjaj/OzgQ8rBmv703WJX+aS6yCmLuIhYqyufyuw==}
    dependencies:
      '@turf/helpers': 6.5.0
    dev: false

  /@turf/bbox@5.1.5:
    resolution: {integrity: sha512-sYQU4fqsOYYJoD8UndC1n2hy8hV/lGIAmMLKWuzwmPUWqWOuSKWUcoRWDi9mGB0GvQQe/ow2IxZr8UaVaGz3sQ==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/bbox@6.5.0:
    resolution: {integrity: sha512-RBbLaao5hXTYyyg577iuMtDB8ehxMlUqHEJiMs8jT1GHkFhr6sYre3lmLsPeYEi/ZKj5TP5tt7fkzNdJ4GIVyw==}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0
    dev: false

  /@turf/bearing@5.1.5:
    resolution: {integrity: sha512-PrvZuJjnXGseB8hUatIjsrK3tgD3wttyRnVYXTbSfXYJZzaOfHDMplgO4lxXQp7diraZhGhCdSlbMvRRXItbUQ==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/bearing@6.5.0:
    resolution: {integrity: sha512-dxINYhIEMzgDOztyMZc20I7ssYVNEpSv04VbMo5YPQsqa80KO3TFvbuCahMsCAW5z8Tncc8dwBlEFrmRjJG33A==}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
    dev: false

  /@turf/bezier-spline@5.1.5:
    resolution: {integrity: sha512-Y9NoComaGgFFFe9TWWE/cEMg2+EnBfU1R3112ec2wlx21ygDmFGXs4boOS71WM4ySwm/dbS3wxnbVxs4j68sKw==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/boolean-clockwise@5.1.5:
    resolution: {integrity: sha512-FqbmEEOJ4rU4/2t7FKx0HUWmjFEVqR+NJrFP7ymGSjja2SQ7Q91nnBihGuT+yuHHl6ElMjQ3ttsB/eTmyCycxA==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/boolean-contains@5.1.5:
    resolution: {integrity: sha512-x2HeEieeE9vBQrTdCuj4swnAXlpKbj9ChxMdDTV479c0m2gVmfea83ocmkj3w+9cvAaS63L8WqFyNVSmkwqljQ==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/boolean-point-in-polygon': 5.1.5
      '@turf/boolean-point-on-line': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/boolean-crosses@5.1.5:
    resolution: {integrity: sha512-odljvS7INr9k/8yXeyXQVry7GqEaChOmXawP0+SoTfGO3hgptiik59TLU/Yjn/SLFjE2Ul54Ga1jKFSL7vvH0Q==}
    dependencies:
      '@turf/boolean-point-in-polygon': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/line-intersect': 5.1.5
      '@turf/polygon-to-line': 5.1.5
    dev: false

  /@turf/boolean-disjoint@5.1.6:
    resolution: {integrity: sha512-KHvUS6SBNYHBCLIJEJrg04pF5Oy+Fqn8V5G9U+9pti5vI9tyX7Ln2g7RSB7iJ1Cxsz8QAi6OukhXjEF2/8ZpGg==}
    dependencies:
      '@turf/boolean-point-in-polygon': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/line-intersect': 5.1.5
      '@turf/meta': 5.1.6
      '@turf/polygon-to-line': 5.1.5
    dev: false

  /@turf/boolean-equal@5.1.5:
    resolution: {integrity: sha512-QEMbhDPV+J8PlRkMlVg6m5oSLaYUpOx2VUhDDekQ73FlpnhFBKRIlidhvHtS6CYnEw8d+/zA3h8Z18B4W4mq9Q==}
    dependencies:
      '@turf/clean-coords': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      geojson-equality: 0.1.6
    dev: false

  /@turf/boolean-overlap@5.1.5:
    resolution: {integrity: sha512-lizojgU559KME0G705YAgWVa0B3/tsWNobMzOEWDx/1rABWTojCY4uxw2rFxpOsP++s8JJHrGWXRLh1PbdAvRQ==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/line-intersect': 5.1.5
      '@turf/line-overlap': 5.1.5
      '@turf/meta': 5.1.6
      geojson-equality: 0.1.6
    dev: false

  /@turf/boolean-parallel@5.1.5:
    resolution: {integrity: sha512-eeuGgDhnas3nJ22A/DD8aiH0kg9dSzbQChIMAqYRPGg3pWNK41aGAbeh5z0GO5N/EVFX1+ga5a0vsPmiRgQB5g==}
    dependencies:
      '@turf/clean-coords': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/line-segment': 5.1.5
      '@turf/rhumb-bearing': 5.1.5
    dev: false

  /@turf/boolean-point-in-polygon@5.1.5:
    resolution: {integrity: sha512-y+gbAhLmsAZH9uYhv+C68pu06mxsGIm3o7l0hzVkc/PXYdbkr+vKe7n7PfSN3xpVA3qoDLKLpCGOqeW8/ThaJA==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/boolean-point-on-line@5.1.5:
    resolution: {integrity: sha512-Zf4d28mckV2tYfLWf2iqxQ8eeLZqi2HGimM26mptf1OCEIwc1wfkKgLRRJXMu94Crvd/pJxjRAjoYGcGliP6Vg==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/boolean-within@5.1.5:
    resolution: {integrity: sha512-CNAtrvm4HiUwV/vhpGhvJzfhV9CN7VhPC5y4tTfQicK82fYY6ifPz0iaNpUOmshU6+TAot/fsVQVgDJ4t7HXcA==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/boolean-point-in-polygon': 5.1.5
      '@turf/boolean-point-on-line': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/buffer@5.1.5:
    resolution: {integrity: sha512-U3LU0HF/JNFUNabpB5ArpNG6yPla7yR5XPrZvzZRH48vvbr/N0rkSRI0tJFRWTz7ntugVm9X0OD9Y382NTJRhA==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/center': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
      '@turf/projection': 5.1.5
      d3-geo: 1.7.1
      turf-jsts: 1.2.3
    dev: false

  /@turf/center-mean@5.1.5:
    resolution: {integrity: sha512-XdkBXzFUuyCqu5EPlBwgkv8FLA8pIGBnt7xy5cxxhxKOYLMrKqwMPPHPA84TjeQpNti0gH0CVuOk2r1f/Pp8iQ==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/center-median@5.1.5:
    resolution: {integrity: sha512-M+O6bSNsIDKZ4utk/YzSOIg6W0isjLVWud+TCLWyrDCWTSERlSJlhOaVE1y7cObhG8nYBHvmszqZyoAY6nufQw==}
    dependencies:
      '@turf/center-mean': 5.1.5
      '@turf/centroid': 5.1.5
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/center-of-mass@5.1.5:
    resolution: {integrity: sha512-UvI7q6GgW3afCVIDOyTRuLT54v9Xwv65Xudxh4FIT6w7HNU4KUBtTGnx0NuhODZcgvZgWVWVakhmIcHQTMjYYA==}
    dependencies:
      '@turf/centroid': 5.1.5
      '@turf/convex': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
    dev: false

  /@turf/center@5.1.5:
    resolution: {integrity: sha512-Dy1TvAv2oHKFddZcWqlVsanxurfcZV1Mmb1E+7H7GRKI+fXZTfRjwCdbiZCbO/tPwxt8jWQHWdLHn8E9lecc3A==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/centroid@5.1.5:
    resolution: {integrity: sha512-0m9ZAZJB4YXLDxF2fWGqlE/g9Y68cebeWaRNOMN+e6Bti1fz0JKQuaEqJV+J8xOmODPHSMbZZ1SqSDVRgVHP2Q==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/circle@5.1.5:
    resolution: {integrity: sha512-CNaEtvp38Q+TSFJHdzdl5iYNjBFZRluRTFikIuEcennSeMJD60nP0dMubP58TR/QQn541eNDUyED90V4KuOjyQ==}
    dependencies:
      '@turf/destination': 5.1.5
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/circle@6.5.0:
    resolution: {integrity: sha512-oU1+Kq9DgRnoSbWFHKnnUdTmtcRUMmHoV9DjTXu9vOLNV5OWtAAh1VZ+mzsioGGzoDNT/V5igbFOkMfBQc0B6A==}
    dependencies:
      '@turf/destination': 6.5.0
      '@turf/helpers': 6.5.0
    dev: false

  /@turf/clean-coords@5.1.5:
    resolution: {integrity: sha512-xd/iSM0McVUxbu81KCKDqirCsYkKk3EAwpDjYI8vIQ+eKf/MLSdteRcm3PB7wo2y6JcYp4dMGv2cr9IP7V+dXQ==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/clone@5.1.5:
    resolution: {integrity: sha512-//pITsQ8xUdcQ9pVb4JqXiSqG4dos5Q9N4sYFoWghX21tfOV2dhc5TGqYOhnHrQS7RiKQL1vQ48kIK34gQ5oRg==}
    dependencies:
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/clone@6.5.0:
    resolution: {integrity: sha512-mzVtTFj/QycXOn6ig+annKrM6ZlimreKYz6f/GSERytOpgzodbQyOgkfwru100O1KQhhjSudKK4DsQ0oyi9cTw==}
    dependencies:
      '@turf/helpers': 6.5.0
    dev: false

  /@turf/clusters-dbscan@5.1.5:
    resolution: {integrity: sha512-X3qLLHJkwMuv+xdWQ08NtOc6BgeqCKKSAltyyAZ7iImE65f0C+sW024DfHSbTMsZVXBFst2Q6RQY8RVUf3QBeQ==}
    dependencies:
      '@turf/clone': 5.1.5
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
      density-clustering: 1.3.0
    dev: false

  /@turf/clusters-kmeans@5.1.5:
    resolution: {integrity: sha512-W6raiv9+fRgmJxCvKrpSacbLXzh7beZUk0A1pjF82Fv3CFTrXAJbgAyIbdlmgXezYSXhOT5NMUugnbkUy2oBZw==}
    dependencies:
      '@turf/clone': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
      skmeans: 0.9.7
    dev: false

  /@turf/clusters@5.1.5:
    resolution: {integrity: sha512-+rQe+g66xfbIXz58tveXQCDdE9hzqRJtDVSw5xth92TvCcL4J60ZKN8mHNUSn1ZZvpUHtVPe4dYcbtk5bW8fXQ==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/collect@5.1.5:
    resolution: {integrity: sha512-voFWu6EGPcNuIbAp43yvGf2Ip4/q8TTeWhOSJ2yDEHgOfbAwrNUwUJCclEjcUVsnc7ypKNrFn3/8bmR9tI0NQg==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/boolean-point-in-polygon': 5.1.5
      '@turf/helpers': 5.1.5
      rbush: 2.0.2
    dev: false

  /@turf/combine@5.1.5:
    resolution: {integrity: sha512-/RqmfCvduHquINVyNmzKOcZtZjfaEHMhghgmj8MYnzepN3ro+E2QXoaQGGrQ7nChAvGgWPAvN8EveVSc1MvzPg==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/concave@5.1.5:
    resolution: {integrity: sha512-NvR5vmAunmgjEPjNzmvjLRvPcj7C6WuqCf+vu/aqyc4h2c1B/x399bDsSM64iFT+PYesFuoS1ZhJHWivXG8Y5g==}
    dependencies:
      '@turf/clone': 5.1.5
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
      '@turf/tin': 5.1.5
      topojson-client: 3.1.0
      topojson-server: 3.0.1
    dev: false

  /@turf/convex@5.1.5:
    resolution: {integrity: sha512-ZEk4kIAoYR/mjO3C8rMe2StgmwhdwmbxVvNxg3udeahe2m0ZzbfkRC4HiJAaBgfR4TLJUAEewynESReTPwASBQ==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
      concaveman: 1.2.1
    dev: false

  /@turf/destination@5.1.5:
    resolution: {integrity: sha512-EWwZnd4wxUO9d8UWzJt88jQlFf6W/6SE1930MMzzIR9o+RfqhrS/BL1eUDrg5I5drsymf6PZsK0j/V0q6jqkFQ==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/destination@6.5.0:
    resolution: {integrity: sha512-4cnWQlNC8d1tItOz9B4pmJdWpXqS0vEvv65bI/Pj/genJnsL7evI0/Xw42RvEGROS481MPiU80xzvwxEvhQiMQ==}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
    dev: false

  /@turf/difference@5.1.5:
    resolution: {integrity: sha512-hIjiUHS8WiDfnmADQrhh6QcXWc3zNtjIpPQ5g/2NZ3k1mjnOdmGBVObkSJG4WEUNqyj3PKlsZ8W9xnSu+lLF1Q==}
    dependencies:
      '@turf/area': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
      turf-jsts: 1.2.3
    dev: false

  /@turf/dissolve@5.1.5:
    resolution: {integrity: sha512-YcQgyp7pvhyZHCmbqqItVH6vHs43R9N0jzP/LnAG03oMiY4wves/BO1du6VDDbnJSXeRKf1afmY9tRGKYrm9ag==}
    dependencies:
      '@turf/boolean-overlap': 5.1.5
      '@turf/clone': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/line-intersect': 5.1.5
      '@turf/meta': 5.1.6
      '@turf/union': 5.1.5
      geojson-rbush: 2.1.0
      get-closest: 0.0.4
    dev: false

  /@turf/distance@5.1.5:
    resolution: {integrity: sha512-sYCAgYZ2MjNKMtx17EijHlK9qHwpA0MuuQWbR4P30LTCl52UlG/reBfV899wKyF3HuDL9ux78IbILwOfeQ4zgA==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/distance@6.5.0:
    resolution: {integrity: sha512-xzykSLfoURec5qvQJcfifw/1mJa+5UwByZZ5TZ8iaqjGYN0vomhV9aiSLeYdUGtYRESZ+DYC/OzY+4RclZYgMg==}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
    dev: false

  /@turf/ellipse@5.1.5:
    resolution: {integrity: sha512-oVTzEyDOi3d9isgB7Ah+YiOoUKB1eHMtMDXVl1oT+vC/T+6KR2aq+HjjbF11A0cjuh3VhjSWUZaS+2TYY0pu0w==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/rhumb-destination': 5.1.5
      '@turf/transform-rotate': 5.1.5
    dev: false

  /@turf/envelope@5.1.5:
    resolution: {integrity: sha512-Mxl5A2euAxq3RZVN65/MVyaO91kzGU8MJXfegPdep6SN4bONDadEp0olwW5qSRf2U3cJ8Jppl089X6AeifD3IA==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/bbox-polygon': 5.1.5
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/explode@5.1.5:
    resolution: {integrity: sha512-v/hC9DB9RKRW9/ZjnKoQelIp08JNa5wew0889465s//tfgY8+JEGkSGMag2L2NnVARWmzI/vlLgMK36qwkyDIA==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/flatten@5.1.5:
    resolution: {integrity: sha512-aagHz5tjHmOtb8eMb5fd10+HJwdlhkhsPql1vRXQNnpv0Q9xL/4SsbvXZ6lPqkRAjiZuy087mvaz+ERml76/jg==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/flip@5.1.5:
    resolution: {integrity: sha512-7+IYM3QQAkV4co3wjEmM726/OkXqUCCHWWyIqrI9hiK+LR628qkoqP1hk6rQ4vZJrAYuvSlK+FZnr24OtgY0cw==}
    dependencies:
      '@turf/clone': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/great-circle@5.1.5:
    resolution: {integrity: sha512-k6FWwlt+YCQoD5VS1NybQjriNL7apYHO+tm2HbIFQ85blPUX4IyLppHIFevfD/k+K2bJqhFCze8JNVMBwdrzVw==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/helpers@5.1.5:
    resolution: {integrity: sha512-/lF+JR+qNDHZ8bF9d+Cp58nxtZWJ3sqFe6n3u3Vpj+/0cqkjk4nXKYBSY0azm+GIYB5mWKxUXvuP/m0ZnKj1bw==}
    dev: false

  /@turf/helpers@6.5.0:
    resolution: {integrity: sha512-VbI1dV5bLFzohYYdgqwikdMVpe7pJ9X3E+dlr425wa2/sMJqYDhTO++ec38/pcPvPE6oD9WEEeU3Xu3gza+VPw==}
    dev: false

  /@turf/hex-grid@5.1.5:
    resolution: {integrity: sha512-rwDL+DlUyxDNL1aVHIKKCmrt1131ZULF3irExYIO/um6/SwRzsBw+522/RcxD/mg/Shtrpozb6bz8aJJ/3RXHA==}
    dependencies:
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/intersect': 5.1.6
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/interpolate@5.1.5:
    resolution: {integrity: sha512-LfmvtIUWc3NVkqPkX6j3CAIjF7y1LAZqfDd+2Ii+0fN7XOOGMWcb1uiTTAb8zDQjhTsygcUYgaz6mMYDCWYKPg==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/centroid': 5.1.5
      '@turf/clone': 5.1.5
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/hex-grid': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
      '@turf/point-grid': 5.1.5
      '@turf/square-grid': 5.1.5
      '@turf/triangle-grid': 5.1.5
    dev: false

  /@turf/intersect@5.1.6:
    resolution: {integrity: sha512-KXyNv/GXdoGAOy03qZF53rgtXC2tNhF/4jLwTKiVRrBQH6kcEpipGStdJ+QkYIlarQPa8f7I9UlVAB19et4MfQ==}
    dependencies:
      '@turf/clean-coords': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/truncate': 5.1.5
      turf-jsts: 1.2.3
    dev: false

  /@turf/invariant@5.1.5:
    resolution: {integrity: sha512-4elbC8GVQ8XxrnWLWpFFXTK3qnzIYzIVtSkJrY9eefA8WNZzwcwT3WGFY3xte4BB48o5oEjihjoJharWRis78w==}
    dependencies:
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/invariant@5.2.0:
    resolution: {integrity: sha512-28RCBGvCYsajVkw2EydpzLdcYyhSA77LovuOvgCJplJWaNVyJYH6BOR3HR9w50MEkPqb/Vc/jdo6I6ermlRtQA==}
    dependencies:
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/invariant@6.5.0:
    resolution: {integrity: sha512-Wv8PRNCtPD31UVbdJE/KVAWKe7l6US+lJItRR/HOEW3eh+U/JwRCSUl/KZ7bmjM/C+zLNoreM2TU6OoLACs4eg==}
    dependencies:
      '@turf/helpers': 6.5.0
    dev: false

  /@turf/isobands@5.1.5:
    resolution: {integrity: sha512-0n3NPfDYQyqjOch00I4hVCCqjKn9Sm+a8qlWOKbkuhmGa9dCDzsu2bZL0ahT+LjwlS4c8/owQXqe6KE2GWqT1Q==}
    dependencies:
      '@turf/area': 5.1.5
      '@turf/bbox': 5.1.5
      '@turf/boolean-point-in-polygon': 5.1.5
      '@turf/explode': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
    dev: false

  /@turf/isolines@5.1.5:
    resolution: {integrity: sha512-Ehn5pJmiq4hAn2+2jPB2rLt3iF8DDp8zciw9z2pAt5IGVRU/K+x3z4aYG5ra5vbFB/E4G3aHr/X4QPIb9LCJtA==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
    dev: false

  /@turf/kinks@5.1.5:
    resolution: {integrity: sha512-G38sC8/+MYqQpVocT3XahhV42cqEAVJAZwUND9YOfKJZfjUn7FKmWhPURs5py95me48UuI0C0jLLAMzBkUc2nQ==}
    dependencies:
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/length@5.1.5:
    resolution: {integrity: sha512-0ryx68h512wCoNfwyksLdabxEfwkGNTPg61/QiY+QfGFUOUNhHbP+QimViFpwF5hyX7qmroaSHVclLUqyLGRbg==}
    dependencies:
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/line-arc@5.1.5:
    resolution: {integrity: sha512-Kz5RX/qRIHVrGNqF3BRlD3ACuuCr0G5lpaVyPjNvN+vA7Q4bEDyWIYeqm3DdTn7X2MXitpTNgr2uvX4WoUy4yA==}
    dependencies:
      '@turf/circle': 5.1.5
      '@turf/destination': 5.1.5
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/line-chunk@5.1.5:
    resolution: {integrity: sha512-mKvTUMahnb3EsYUMI8tQmygsliQkgQ1FZAY915zoTrm+WV246loa+84+h7i5d8W2O8gGJWuY7jQTpM7toTeL5w==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/length': 5.1.5
      '@turf/line-slice-along': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/line-intersect@5.1.5:
    resolution: {integrity: sha512-9DajJbHhJauLI2qVMnqZ7SeFsinFroVICOSUheODk7j5teuwNABuZ2Z6WmKATzEsPkEJ1iVykqB+F9vGMVKB6g==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/line-segment': 5.1.5
      '@turf/meta': 5.1.6
      geojson-rbush: 2.1.0
    dev: false

  /@turf/line-offset@5.1.5:
    resolution: {integrity: sha512-VccGDgFfBSiCTqrHdQgxD7Rs9lnJmDOJ5gqQRculKPsCNUyRFMYIZud7l2dTs83g66evfOwkZCrTxtSoBY3Jxg==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
    dev: false

  /@turf/line-overlap@5.1.5:
    resolution: {integrity: sha512-hMz3XARXEbfGwLF9WXyErqQjzhZYMKvGQwlPGOoth+2o9Uga9mfWfevduJvozJAE1MKxtFttMjIXMzcShW3O8A==}
    dependencies:
      '@turf/boolean-point-on-line': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/line-segment': 5.1.5
      '@turf/meta': 5.1.6
      '@turf/nearest-point-on-line': 5.1.5
      geojson-rbush: 2.1.0
    dev: false

  /@turf/line-segment@5.1.5:
    resolution: {integrity: sha512-wIrRtWuLuLXhnSkqdVG1SDayTU0/CmZf+a+BBhEf0vFIsAedJnrY3a2cbCEvtfuk6ZsAbhOi7/kYiaR/F+rEzg==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
    dev: false

  /@turf/line-slice-along@5.1.5:
    resolution: {integrity: sha512-yKvSDtULztLtlPIMowm9l8pS6XLAEpCPmrARZA0sIWFX8XrcSzISBaXZbiMMzg3nxQJMXfGIgWDk10B7+J8Tqw==}
    dependencies:
      '@turf/bearing': 5.1.5
      '@turf/destination': 5.1.5
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/line-slice@5.1.5:
    resolution: {integrity: sha512-Fo+CuD+fj6T702BofHO+rgiXUgzCk0iO2JqMPtttMtgzfKkVTUOQoauMNS1LNNaG/7n/TfKGh5gRCEDRNaNwYA==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/nearest-point-on-line': 5.1.5
    dev: false

  /@turf/line-split@5.1.5:
    resolution: {integrity: sha512-gtUUBwZL3hcSu5MpqHTl68hgAJBNHcr1APDj8E5o6iX5xFX+wvl4ohQXyMs5HOATCI8Iy83wLuggcY6maNw7LQ==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/line-intersect': 5.1.5
      '@turf/line-segment': 5.1.5
      '@turf/meta': 5.1.6
      '@turf/nearest-point-on-line': 5.1.5
      '@turf/square': 5.1.5
      '@turf/truncate': 5.1.5
      geojson-rbush: 2.1.0
    dev: false

  /@turf/line-to-polygon@5.1.5:
    resolution: {integrity: sha512-hGiDAPd6j986kZZLDgEAkVD7O6DmIqHQliBedspoKperPJOUJJzdzSnF6OAWSsxY+j8fWtQnIo5TTqdO/KfamA==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/mask@5.1.5:
    resolution: {integrity: sha512-2eOuxA3ammZAGsjlsy/H7IpeJxjl3hrgkcKM6kTKRJGft4QyKwCxqQP7RN5j0zIYvAurgs9JOLe/dpd5sE5HXQ==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
      '@turf/union': 5.1.5
      rbush: 2.0.2
    dev: false

  /@turf/meta@5.1.6:
    resolution: {integrity: sha512-lv+6LCgoc3LVitQZ4TScN/8a/fcctq8bIoxBTMJVq4aU8xoHeY1851Dq8MCU37EzbH33utkx8/jENaQP+aeElg==}
    dependencies:
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/meta@6.5.0:
    resolution: {integrity: sha512-RrArvtsV0vdsCBegoBtOalgdSOfkBrTJ07VkpiCnq/491W67hnMWmDu7e6Ztw0C3WldRYTXkg3SumfdzZxLBHA==}
    dependencies:
      '@turf/helpers': 6.5.0
    dev: false

  /@turf/midpoint@5.1.5:
    resolution: {integrity: sha512-0pDQAKHyK/zxlvUx3XNxwvqftf4sV32QxnHfqSs4AXaODUGUbPhzAD7aXgDScBeUOVLwpAzFRQfitUvUMTGC6A==}
    dependencies:
      '@turf/bearing': 5.1.5
      '@turf/destination': 5.1.5
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/nearest-point-on-line@5.1.5:
    resolution: {integrity: sha512-qT7BLTwToo8cq0oNoz921oLlRPJamyRg/rZgll+kNBadyDPmJI4W66riHcpM9RQcAJ6TPvDveIIBeGJH7iG88w==}
    dependencies:
      '@turf/bearing': 5.1.5
      '@turf/destination': 5.1.5
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/line-intersect': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/nearest-point-to-line@5.1.6:
    resolution: {integrity: sha512-ZSvDIEiHhifn/vNwLXZI/E8xmEz5yBPqfUR7BVHRZrB1cP7jLhKZvkbidjG//uW8Fr1Ulc+PFOXczLspIcx/lw==}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/point-to-line-distance': 5.1.6
      object-assign: 4.1.1
    dev: false

  /@turf/nearest-point@5.1.5:
    resolution: {integrity: sha512-tZQXI7OE7keNKK4OvYOJ5gervCEuu2pJ6psu59QW9yhe2Di3Gl+HAdLvVa6RZ8s5Fndr3u0JWKsmxve3fCxc9g==}
    dependencies:
      '@turf/clone': 5.1.5
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/planepoint@5.1.5:
    resolution: {integrity: sha512-+Tp+SQ0Db2tqwLbxfXJPysT9IxcOHSMIin2dJb/j3Qn5+g0LRus6rczZl6dWNAIjqBPMawj/V/dZhMu6Q9O9wA==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/point-grid@5.1.5:
    resolution: {integrity: sha512-4ibozguP9YJ297Q7i9e8/ypGSycvt1re2jrPXTxeuZ4/L/NE5B1nOBLG+tw121nMjD+S+v2RWOtqD+FZ3Ga+ew==}
    dependencies:
      '@turf/boolean-within': 5.1.5
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/point-on-feature@5.1.5:
    resolution: {integrity: sha512-NTcpe5xZjybRh0aTL+7td1cm0s49GGbAt5u8Cdec4W9ix2PsehRcLUbmQIQsODN2kiVyUSpnhECIpsyN5MjX7A==}
    dependencies:
      '@turf/boolean-point-in-polygon': 5.1.5
      '@turf/center': 5.1.5
      '@turf/explode': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/nearest-point': 5.1.5
    dev: false

  /@turf/point-to-line-distance@5.1.6:
    resolution: {integrity: sha512-PE3hiTeeDEi4ZLPtI8XAzFYW9nHo1EVsZGm/4ZVV8jo39d3X1oLVHxY3e1PkCmWwRapXy4QLqvnTQ7nU4wspNw==}
    dependencies:
      '@turf/bearing': 6.5.0
      '@turf/distance': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      '@turf/meta': 6.5.0
      '@turf/projection': 6.5.0
      '@turf/rhumb-bearing': 6.5.0
      '@turf/rhumb-distance': 6.5.0
    dev: false

  /@turf/points-within-polygon@5.1.5:
    resolution: {integrity: sha512-nexe2AHVOY8wEBvs+CYSOp10NyOCkyZ1gkhIfsx0mzU8LPYBxD9ctjlKveheKh4AAldLcFupd/gSCBTKF1JS7A==}
    dependencies:
      '@turf/boolean-point-in-polygon': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/polygon-tangents@5.1.5:
    resolution: {integrity: sha512-uoZfKvFhl6rf0+CDWucru9fZ4mJB5Nsg37TS/7emrzjoVxXyOdxc/s1HFCjcKflMue7MjU/gT6AitJyrvdztDg==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/polygon-to-line@5.1.5:
    resolution: {integrity: sha512-kVo0owPqyccy5+qZGvaxGvMsYkgueKE2OOgX2UV/HyrXF3uI3TomK1txjApqeFsLvwuSANxesvVbYLrYiIwvGw==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/polygon-to-line@6.5.0:
    resolution: {integrity: sha512-5p4n/ij97EIttAq+ewSnKt0ruvuM+LIDzuczSzuHTpq4oS7Oq8yqg5TQ4nzMVuK41r/tALCk7nAoBuw3Su4Gcw==}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
    dev: false

  /@turf/polygonize@5.1.5:
    resolution: {integrity: sha512-qzhtuzoOhldqZHm+ZPsWAs9nDpnkcDfsr+I0twmBF+wjAmo0HKiy9++sRQ4kEePpdwbMpF07D/NdZqYdmOJkGQ==}
    dependencies:
      '@turf/boolean-point-in-polygon': 5.1.5
      '@turf/envelope': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
    dev: false

  /@turf/projection@5.1.5:
    resolution: {integrity: sha512-TWKJDFeEKQhI4Ce1+2PuOSDggn4cnMibqyUoCpIW+4KxUC1R88SE3/SYomqzwxMn00O09glHSycPkGD5JzHd8A==}
    dependencies:
      '@turf/clone': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/projection@6.5.0:
    resolution: {integrity: sha512-/Pgh9mDvQWWu8HRxqpM+tKz8OzgauV+DiOcr3FCjD6ubDnrrmMJlsf6fFJmggw93mtVPrZRL6yyi9aYCQBOIvg==}
    dependencies:
      '@turf/clone': 6.5.0
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0
    dev: false

  /@turf/random@5.1.5:
    resolution: {integrity: sha512-oitpBwEb6YXqoUkIAOVMK+vrTPxUi2rqITmtTa/FBHr6J8TDwMWq6bufE3Gmgjxsss50O2ITJunOksxrouWGDQ==}
    dependencies:
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/rewind@5.1.5:
    resolution: {integrity: sha512-Gdem7JXNu+G4hMllQHXRFRihJl3+pNl7qY+l4qhQFxq+hiU1cQoVFnyoleIqWKIrdK/i2YubaSwc3SCM7N5mMw==}
    dependencies:
      '@turf/boolean-clockwise': 5.1.5
      '@turf/clone': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
    dev: false

  /@turf/rhumb-bearing@5.1.5:
    resolution: {integrity: sha512-zXTl2khjwf7mx2D1uPo5vgpGgP4sM2VrKDbJNKyulPu4TO4ELt8x7FsKyCBlRTzzQf284t/xnNcZOfUbkkd70g==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/rhumb-bearing@6.5.0:
    resolution: {integrity: sha512-jMyqiMRK4hzREjQmnLXmkJ+VTNTx1ii8vuqRwJPcTlKbNWfjDz/5JqJlb5NaFDcdMpftWovkW5GevfnuzHnOYA==}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
    dev: false

  /@turf/rhumb-destination@5.1.5:
    resolution: {integrity: sha512-FdDUCSRfRAfsRmUaWjc76Wk32QYFJ6ckmSt6Ls6nEczO6eg/RgH1atF8CIYwR5ifl0Sk1rQzKiOSbpCyvVwQtw==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/rhumb-distance@5.1.5:
    resolution: {integrity: sha512-AGA/ky5/BJJZtzQqafy2GvJfcUXSzCCrPFp8sDRPSKBoUN4gMBHN15ijDWYYLFoWFFj0urcauVx7chQlHZ/Qfw==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/rhumb-distance@6.5.0:
    resolution: {integrity: sha512-oKp8KFE8E4huC2Z1a1KNcFwjVOqa99isxNOwfo4g3SUABQ6NezjKDDrnvC4yI5YZ3/huDjULLBvhed45xdCrzg==}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
    dev: false

  /@turf/sample@5.1.5:
    resolution: {integrity: sha512-EJE8yx+5x7rXejTzwBdOKpvT4tOCS0jwYJfycyTVDuLUSh2rETeYdjy7EeJbofnxm9CRPXqWQMPWIBKWxNTjow==}
    dependencies:
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/sector@5.1.5:
    resolution: {integrity: sha512-dnWVifL3xWTqPPs8mfbbV9muDimNJtxRk4ogrkOLEDQ9ZZ1ALQMtQdYrg7kI3iC+L+LscV37tl+E8bayWyX8YA==}
    dependencies:
      '@turf/circle': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/line-arc': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/shortest-path@5.1.5:
    resolution: {integrity: sha512-ZGC8kSBj02GKWiI56Z5FNdrZ+fS0xyeOUNrPJWzudAlrv9wKGaRuWoIVRLGBu0j0OuO1HCwggic2c6WV/AhP0A==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/bbox-polygon': 5.1.5
      '@turf/boolean-point-in-polygon': 5.1.5
      '@turf/clean-coords': 5.1.5
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
      '@turf/transform-scale': 5.1.5
    dev: false

  /@turf/simplify@5.1.5:
    resolution: {integrity: sha512-IuBXEYdGSxbDOK3v949ajaPvs6NhjhTCTbKA6mSGuVbwGS7gzAuRiPSG4K/MvCVuQy3PKpkPcUGD+Uvt2Ov2PQ==}
    dependencies:
      '@turf/clean-coords': 5.1.5
      '@turf/clone': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/square-grid@5.1.5:
    resolution: {integrity: sha512-/pusEL4FmOwNWLcZfIXUyqUe0fOdkfaLO4wLhDlg/ZL1jWr/wZjhVlMU0tQ27kVN6dJTvlzNc9e0JWNw6yt2eQ==}
    dependencies:
      '@turf/boolean-contains': 5.1.5
      '@turf/boolean-overlap': 5.1.5
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/intersect': 5.1.6
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/square@5.1.5:
    resolution: {integrity: sha512-GgP2le9ksoW6vsVef5wFkjmWQiLPTJvcjGXqmoGWT4oMwDpvTJVQ91RBLs8qQbI4KACCQevz94N69klk3ah30Q==}
    dependencies:
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/standard-deviational-ellipse@5.1.5:
    resolution: {integrity: sha512-GOaxGKeeJAXV1H3Zz2fjQ5XeSbMKz1OkFRlTDBUipiAawe/9qTCF55L87I2ZPnO80B5BaaIT+AN2n0lMcAklzA==}
    dependencies:
      '@turf/center-mean': 5.1.5
      '@turf/ellipse': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
      '@turf/points-within-polygon': 5.1.5
    dev: false

  /@turf/tag@5.1.5:
    resolution: {integrity: sha512-XI3QFpva6tEsRnzFe1tJGdAAWlzjnXZPfJ9EKShTxEW8ZgPzm92b2odjiSAt2KuQusK82ltNfdw5Frlna5xGYQ==}
    dependencies:
      '@turf/boolean-point-in-polygon': 5.1.5
      '@turf/clone': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/tesselate@5.1.5:
    resolution: {integrity: sha512-Rs/jAij26bcU4OzvFXkWDase1G3kSwyuuKZPFU0t7OmJu7eQJOR12WOZLGcVxd5oBlklo4xPE4EBQUqpQUsQgg==}
    dependencies:
      '@turf/helpers': 5.1.5
      earcut: 2.2.4
    dev: false

  /@turf/tin@5.1.5:
    resolution: {integrity: sha512-lDyCTYKoThBIKmkBxBMupqEpFbvTDAYuZIs8qrWnmux2vntSb8OFGi7ZbGPC6apS2hdVwZZae3YB88Tp+Fg+xw==}
    dependencies:
      '@turf/helpers': 5.1.5
    dev: false

  /@turf/transform-rotate@5.1.5:
    resolution: {integrity: sha512-3QKckeHKPXu5O5vEuT+nkszGDI6aknDD06ePb00+6H2oA7MZj7nj+fVQIJLs41MRb76IyKr4n5NvuKZU6idESA==}
    dependencies:
      '@turf/centroid': 5.1.5
      '@turf/clone': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
      '@turf/rhumb-bearing': 5.1.5
      '@turf/rhumb-destination': 5.1.5
      '@turf/rhumb-distance': 5.1.5
    dev: false

  /@turf/transform-scale@5.1.5:
    resolution: {integrity: sha512-t1fCZX29ONA7DJiqCKA4YZy0+hCzhppWNOZhglBUv9vKHsWCFYZDUKfFInciaypUInsZyvm8eKxxixBVPdPGsw==}
    dependencies:
      '@turf/bbox': 5.1.5
      '@turf/center': 5.1.5
      '@turf/centroid': 5.1.5
      '@turf/clone': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
      '@turf/rhumb-bearing': 5.1.5
      '@turf/rhumb-destination': 5.1.5
      '@turf/rhumb-distance': 5.1.5
    dev: false

  /@turf/transform-translate@5.1.5:
    resolution: {integrity: sha512-GdLFp7I7198oRQt311B8EjiqHupndeMSQ3Zclzki5L/niUrb1ptOIpo+mxSidSy03m+1Q5ylWlENroI1WBcQ3Q==}
    dependencies:
      '@turf/clone': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.1.6
      '@turf/rhumb-destination': 5.1.5
    dev: false

  /@turf/triangle-grid@5.1.5:
    resolution: {integrity: sha512-jmCRcynI80xsVqd+0rv0YxP6mvZn4BAaJv8dwthg2T3WfHB9OD+rNUMohMuUY8HmI0zRT3s/Ypdy2Cdri9u/tw==}
    dependencies:
      '@turf/distance': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/intersect': 5.1.6
      '@turf/invariant': 5.2.0
    dev: false

  /@turf/truncate@5.1.5:
    resolution: {integrity: sha512-WjWGsRE6o1vUqULGb/O7O1eK6B4Eu6R/RBZWnF0rH0Os6WVel6tHktkeJdlKwz9WElIEO12wDIu6uKd54t7DDQ==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
    dev: false

  /@turf/turf@5.1.6:
    resolution: {integrity: sha512-NIjkt5jAbOrom+56ELw9ERZF6qsdf1xAIHyC9/PkDMIOQAxe7FVe2HaqbQ+x88F0q5FaSX4dtpIEf08md6h5/A==}
    dependencies:
      '@turf/along': 5.1.5
      '@turf/area': 5.1.5
      '@turf/bbox': 5.1.5
      '@turf/bbox-clip': 5.1.5
      '@turf/bbox-polygon': 5.1.5
      '@turf/bearing': 5.1.5
      '@turf/bezier-spline': 5.1.5
      '@turf/boolean-clockwise': 5.1.5
      '@turf/boolean-contains': 5.1.5
      '@turf/boolean-crosses': 5.1.5
      '@turf/boolean-disjoint': 5.1.6
      '@turf/boolean-equal': 5.1.5
      '@turf/boolean-overlap': 5.1.5
      '@turf/boolean-parallel': 5.1.5
      '@turf/boolean-point-in-polygon': 5.1.5
      '@turf/boolean-point-on-line': 5.1.5
      '@turf/boolean-within': 5.1.5
      '@turf/buffer': 5.1.5
      '@turf/center': 5.1.5
      '@turf/center-mean': 5.1.5
      '@turf/center-median': 5.1.5
      '@turf/center-of-mass': 5.1.5
      '@turf/centroid': 5.1.5
      '@turf/circle': 5.1.5
      '@turf/clean-coords': 5.1.5
      '@turf/clone': 5.1.5
      '@turf/clusters': 5.1.5
      '@turf/clusters-dbscan': 5.1.5
      '@turf/clusters-kmeans': 5.1.5
      '@turf/collect': 5.1.5
      '@turf/combine': 5.1.5
      '@turf/concave': 5.1.5
      '@turf/convex': 5.1.5
      '@turf/destination': 5.1.5
      '@turf/difference': 5.1.5
      '@turf/dissolve': 5.1.5
      '@turf/distance': 5.1.5
      '@turf/ellipse': 5.1.5
      '@turf/envelope': 5.1.5
      '@turf/explode': 5.1.5
      '@turf/flatten': 5.1.5
      '@turf/flip': 5.1.5
      '@turf/great-circle': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/hex-grid': 5.1.5
      '@turf/interpolate': 5.1.5
      '@turf/intersect': 5.1.6
      '@turf/invariant': 5.1.5
      '@turf/isobands': 5.1.5
      '@turf/isolines': 5.1.5
      '@turf/kinks': 5.1.5
      '@turf/length': 5.1.5
      '@turf/line-arc': 5.1.5
      '@turf/line-chunk': 5.1.5
      '@turf/line-intersect': 5.1.5
      '@turf/line-offset': 5.1.5
      '@turf/line-overlap': 5.1.5
      '@turf/line-segment': 5.1.5
      '@turf/line-slice': 5.1.5
      '@turf/line-slice-along': 5.1.5
      '@turf/line-split': 5.1.5
      '@turf/line-to-polygon': 5.1.5
      '@turf/mask': 5.1.5
      '@turf/meta': 5.1.6
      '@turf/midpoint': 5.1.5
      '@turf/nearest-point': 5.1.5
      '@turf/nearest-point-on-line': 5.1.5
      '@turf/nearest-point-to-line': 5.1.6
      '@turf/planepoint': 5.1.5
      '@turf/point-grid': 5.1.5
      '@turf/point-on-feature': 5.1.5
      '@turf/point-to-line-distance': 5.1.6
      '@turf/points-within-polygon': 5.1.5
      '@turf/polygon-tangents': 5.1.5
      '@turf/polygon-to-line': 5.1.5
      '@turf/polygonize': 5.1.5
      '@turf/projection': 5.1.5
      '@turf/random': 5.1.5
      '@turf/rewind': 5.1.5
      '@turf/rhumb-bearing': 5.1.5
      '@turf/rhumb-destination': 5.1.5
      '@turf/rhumb-distance': 5.1.5
      '@turf/sample': 5.1.5
      '@turf/sector': 5.1.5
      '@turf/shortest-path': 5.1.5
      '@turf/simplify': 5.1.5
      '@turf/square': 5.1.5
      '@turf/square-grid': 5.1.5
      '@turf/standard-deviational-ellipse': 5.1.5
      '@turf/tag': 5.1.5
      '@turf/tesselate': 5.1.5
      '@turf/tin': 5.1.5
      '@turf/transform-rotate': 5.1.5
      '@turf/transform-scale': 5.1.5
      '@turf/transform-translate': 5.1.5
      '@turf/triangle-grid': 5.1.5
      '@turf/truncate': 5.1.5
      '@turf/union': 5.1.5
      '@turf/unkink-polygon': 5.1.5
      '@turf/voronoi': 5.1.5
    dev: false

  /@turf/union@5.1.5:
    resolution: {integrity: sha512-wBy1ixxC68PpsTeEDebk/EfnbI1Za5dCyY7xFY9NMzrtVEOy0l0lQ5syOsaqY4Ire+dbsDM66p2GGxmefoyIEA==}
    dependencies:
      '@turf/helpers': 5.1.5
      turf-jsts: 1.2.3
    dev: false

  /@turf/union@6.5.0:
    resolution: {integrity: sha512-igYWCwP/f0RFHIlC2c0SKDuM/ObBaqSljI3IdV/x71805QbIvY/BYGcJdyNcgEA6cylIGl/0VSlIbpJHZ9ldhw==}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/invariant': 6.5.0
      polygon-clipping: 0.15.3
    dev: false

  /@turf/unkink-polygon@5.1.5:
    resolution: {integrity: sha512-lzSrgsfSuyxIc4pkE2qyM2dsHxR992e6oItoZAT8G58A2Ef4qc5gRocmXPWZakGx41fQobegSo7wlo4I49wyHg==}
    dependencies:
      '@turf/area': 5.1.5
      '@turf/boolean-point-in-polygon': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/meta': 5.1.6
      rbush: 2.0.2
    dev: false

  /@turf/voronoi@5.1.5:
    resolution: {integrity: sha512-Ad0HZAyYjOpMIZfDGV+Q+30M9PQHIirTyn32kWyTjEI1O6uhL5NOYjzSha4Sr77xOls3hGzKOj+JET7eDtOvsg==}
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      d3-voronoi: 1.1.2
    dev: false

  /@types/acorn@4.0.6:
    resolution: {integrity: sha512-veQTnWP+1D/xbxVrPC3zHnCZRjSrKfhbMUlEA43iMZLu7EsnTtkJklIuwrCPbOi8YkvDQAiW05VQQFvvz9oieQ==}
    dependencies:
      '@types/estree': 0.0.41
    dev: false

  /@types/amap-js-api@1.4.16:
    resolution: {integrity: sha512-gfcEswuU4GNRdGAh74FhxfLlU6qxmVIJYPf3HV0uKucCWQ9iSr8TonDhqBvHVmmQEY60K+UAeIh0YLwKo/jpFQ==}
    dev: false

  /@types/d3-timer@2.0.1:
    resolution: {integrity: sha512-TF8aoF5cHcLO7W7403blM7L1T+6NF3XMyN3fxyUolq2uOcFeicG/khQg/dGxiCJWoAcmYulYN7LYSRKO54IXaA==}
    dev: false

  /@types/estree@0.0.41:
    resolution: {integrity: sha512-rIAmXyJlqw4KEBO7+u9gxZZSQHaCNnIzYrnNmYVpgfJhxTqO0brCX0SYpqUTkVI5mwwUwzmtspLBGBKroMeynA==}
    dev: false

  /@types/geojson@7946.0.16:
    resolution: {integrity: sha512-6C8nqWur3j98U6+lXDfTUWIfgvZU+EumvpHKcYjujKH7woYyLj2sUmff0tRhrqM7BohUw7Pz3ZB1jj2gW9Fvmg==}
    dev: false

  /@types/history@4.7.11:
    resolution: {integrity: sha512-qjDJRrmvBMiTx+jyLxvLfJU7UznFuokDv4f3WRuriHKERccVpFU+8XMQUAbDzoiJCsmexxRExQeMwwCdamSKDA==}
    dev: true

  /@types/hoist-non-react-statics@3.3.1:
    resolution: {integrity: sha512-iMIqiko6ooLrTh1joXodJK5X9xeEALT1kM5G3ZLhD3hszxBdIEd5C75U834D9mLcINgD4OyZf5uQXjkuYydWvA==}
    dependencies:
      '@types/react': 16.14.62
      hoist-non-react-statics: 3.3.2

  /@types/istanbul-lib-coverage@2.0.4:
    resolution: {integrity: sha512-z/QT1XN4K4KYuslS23k62yDIDLwLFkzxOuMplDtObz0+y7VqJCaO2o+SPwHCvLFZh7xazvvoor2tA/hPz9ee7g==}
    dev: true

  /@types/istanbul-lib-report@3.0.0:
    resolution: {integrity: sha512-plGgXAPfVKFoYfa9NpYDAkseG+g6Jr294RqeqcqDixSbU34MZVJRi/P+7Y8GDpzkEwLaGZZOpKIEmeVZNtKsrg==}
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.4
    dev: true

  /@types/istanbul-reports@3.0.1:
    resolution: {integrity: sha512-c3mAZEuK0lvBp8tmuL74XRKn1+y2dcwOUpH7x4WrF6gk1GIgiluDRgMYQtw2OFcBvAJWlt6ASU3tSqxp0Uu0Aw==}
    dependencies:
      '@types/istanbul-lib-report': 3.0.0
    dev: true

  /@types/jest@29.5.14:
    resolution: {integrity: sha512-ZN+4sdnLUbo8EVvVc2ao0GFW6oVrQRPn4K2lglySj7APvSrgzxHiNNK99us4WDMi57xxA2yggblIAMNhXOotLQ==}
    dependencies:
      expect: 29.6.4
      pretty-format: 29.6.3
    dev: true

  /@types/js-cookie@2.2.7:
    resolution: {integrity: sha512-aLkWa0C0vO5b4Sr798E26QgOkss68Un0bLjs7u9qxzPT5CG+8DuNTffWES58YzJs3hrVAOs1wonycqEBqNJubA==}
    dev: false

  /@types/json-schema@7.0.12:
    resolution: {integrity: sha512-Hr5Jfhc9eYOQNPYO5WLDq/n4jqijdHNlDXjuAQkkt+mWdQR+XJToOHrsD4cPaMXpn6KO7y2+wM8AZEs8VpBLVA==}
    dev: true

  /@types/json5@0.0.29:
    resolution: {integrity: sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==}
    dev: true

  /@types/lodash@4.17.15:
    resolution: {integrity: sha512-w/P33JFeySuhN6JLkysYUK2gEmy9kHHFN7E8ro0tkfmlDOgxBDzWEZ/J8cWA+fHqFevpswDTFZnDx+R9lbL6xw==}
    dev: true

  /@types/mapbox-gl@1.13.6:
    resolution: {integrity: sha512-CONmQCVgzLOseiDa0s8EXwgc3z9FQcc8U/KsIBiaNBY1sIxPen14k9z3eMeYgpx1vo7k0cq9xxxk3/xSZkW0TQ==}
    dependencies:
      '@types/geojson': 7946.0.16
    dev: false

  /@types/mapbox__point-geometry@0.1.4:
    resolution: {integrity: sha512-mUWlSxAmYLfwnRBmgYV86tgYmMIICX4kza8YnE/eIlywGe2XoOxlpVnXWwir92xRLjwyarqwpu2EJKD2pk0IUA==}
    dev: false

  /@types/mapbox__vector-tile@1.3.4:
    resolution: {integrity: sha512-bpd8dRn9pr6xKvuEBQup8pwQfD4VUyqO/2deGjfpe6AwC8YRlyEipvefyRJUSiCJTZuCb8Pl1ciVV5ekqJ96Bg==}
    dependencies:
      '@types/geojson': 7946.0.16
      '@types/mapbox__point-geometry': 0.1.4
      '@types/pbf': 3.0.5
    dev: false

  /@types/node@20.17.19:
    resolution: {integrity: sha512-LEwC7o1ifqg/6r2gn9Dns0f1rhK+fPFDoMiceTJ6kWmVk6bgXBI/9IOWfVan4WiAavK9pIVWdX0/e3J+eEUh5A==}
    dependencies:
      undici-types: 6.19.8
    dev: true

  /@types/parse-json@4.0.0:
    resolution: {integrity: sha512-//oorEZjL6sbPcKUaCdIGlIUeH26mgzimjBB77G6XRgnDl/L5wOnpyBGRe/Mmf5CVW3PwEBE1NjiMZ/ssFh4wA==}
    dev: true

  /@types/pbf@3.0.5:
    resolution: {integrity: sha512-j3pOPiEcWZ34R6a6mN07mUkM4o4Lwf6hPNt8eilOeZhTFbxFXmKhvXl9Y28jotFPaI1bpPDJsbCprUoNke6OrA==}
    dev: false

  /@types/prop-types@15.7.5:
    resolution: {integrity: sha512-JCB8C6SnDoQf0cNycqd/35A7MjcnK+ZTqE7judS6o7utxUCg6imJg3QK2qzHKszlTjcj2cn+NwMB2i96ubpj7w==}

  /@types/react-dom@18.3.5(@types/react@16.14.62):
    resolution: {integrity: sha512-P4t6saawp+b/dFrUr2cvkVsfvPguwsxtH6dNIYRllMsefqFzkZk5UIjzyDOv5g1dXIPdG4Sp1yCR4Z6RCUsG/Q==}
    peerDependencies:
      '@types/react': ^18.0.0
    dependencies:
      '@types/react': 16.14.62
    dev: true

  /@types/react-redux@7.1.34:
    resolution: {integrity: sha512-GdFaVjEbYv4Fthm2ZLvj1VSCedV7TqE5y1kNwnjSdBOTXuRSgowux6J8TAct15T3CKBr63UMk+2CO7ilRhyrAQ==}
    dependencies:
      '@types/hoist-non-react-statics': 3.3.1
      '@types/react': 16.14.62
      hoist-non-react-statics: 3.3.2
      redux: 4.2.1

  /@types/react-router-dom@5.3.3:
    resolution: {integrity: sha512-kpqnYK4wcdm5UaWI3fLcELopqLrHgLqNsdpHauzlQktfkHL3npOSwtj1Uz9oKBAzs7lFtVkV8j83voAz2D8fhw==}
    dependencies:
      '@types/history': 4.7.11
      '@types/react': 16.14.62
      '@types/react-router': 5.1.20
    dev: true

  /@types/react-router@5.1.20:
    resolution: {integrity: sha512-jGjmu/ZqS7FjSH6owMcD5qpq19+1RS9DeVRqfl1FeBMxTDQAGwlMWOcs52NDoXaNKyG3d1cYQFMs9rCrb88o9Q==}
    dependencies:
      '@types/history': 4.7.11
      '@types/react': 16.14.62
    dev: true

  /@types/react@16.14.62:
    resolution: {integrity: sha512-BWf7hqninZav6nerxXj+NeZT/mTpDeG6Lk2zREHAy63CrnXoOGPGtNqTFYFN/sqpSaREDP5otVV88axIXmKfGA==}
    dependencies:
      '@types/prop-types': 15.7.5
      '@types/scheduler': 0.16.8
      csstype: 3.1.2

  /@types/scheduler@0.16.8:
    resolution: {integrity: sha512-WZLiwShhwLRmeV6zH+GkbOFT6Z6VklCItrDioxUnv+u4Ll+8vKeFySoFyK/0ctcRpOmwAicELfmys1sDc/Rw+A==}

  /@types/semver@7.5.0:
    resolution: {integrity: sha512-G8hZ6XJiHnuhQKR7ZmysCeJWE08o8T0AXtk5darsCaTVsYZhhgUrq53jizaR2FvsoeCwJhlmwTjkXBY5Pn/ZHw==}
    dev: true

  /@types/sortablejs@1.15.8:
    resolution: {integrity: sha512-b79830lW+RZfwaztgs1aVPgbasJ8e7AXtZYHTELNXZPsERt4ymJdjV4OccDbHQAvHrCcFpbF78jkm0R6h/pZVg==}
    dev: true

  /@types/stack-utils@2.0.1:
    resolution: {integrity: sha512-Hl219/BT5fLAaz6NDkSuhzasy49dwQS/DSdu4MdggFB8zcXv7vflBI3xp7FEmkmdDkBUI2bPUNeMttp2knYdxw==}
    dev: true

  /@types/supercluster@7.1.3:
    resolution: {integrity: sha512-Z0pOY34GDFl3Q6hUFYf3HkTwKEE02e7QgtJppBt+beEAxnyOpJua+voGFvxINBHa06GwLFFym7gRPY2SiKIfIA==}
    dependencies:
      '@types/geojson': 7946.0.16
    dev: false

  /@types/yargs-parser@21.0.0:
    resolution: {integrity: sha512-iO9ZQHkZxHn4mSakYV0vFHAVDyEOIJQrV2uZ06HxEPcx+mt8swXoZHIbaaJ2crJYFfErySgktuTZ3BeLz+XmFA==}
    dev: true

  /@types/yargs@17.0.24:
    resolution: {integrity: sha512-6i0aC7jV6QzQB8ne1joVZ0eSFIstHsCrobmOtghM11yGlH0j43FKL2UhWdELkyps0zuf7qVTUVCCR+tgSlyLLw==}
    dependencies:
      '@types/yargs-parser': 21.0.0
    dev: true

  /@typescript-eslint/eslint-plugin@6.21.0(@typescript-eslint/parser@6.21.0)(eslint@6.6.0)(typescript@5.0.4):
    resolution: {integrity: sha512-oy9+hTPCUFpngkEZUSzbf9MxI65wbKFoQYsgPdILTfbUldp5ovUuphZVe4i30emU9M/kP+T64Di0mxl7dSw3MA==}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^6.0.0 || ^6.0.0-alpha
      eslint: ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@eslint-community/regexpp': 4.8.0
      '@typescript-eslint/parser': 6.21.0(eslint@6.6.0)(typescript@5.0.4)
      '@typescript-eslint/scope-manager': 6.21.0
      '@typescript-eslint/type-utils': 6.21.0(eslint@6.6.0)(typescript@5.0.4)
      '@typescript-eslint/utils': 6.21.0(eslint@6.6.0)(typescript@5.0.4)
      '@typescript-eslint/visitor-keys': 6.21.0
      debug: 4.3.4
      eslint: 6.6.0
      graphemer: 1.4.0
      ignore: 5.2.4
      natural-compare: 1.4.0
      semver: 7.5.4
      ts-api-utils: 1.0.2(typescript@5.0.4)
      typescript: 5.0.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/parser@6.21.0(eslint@6.6.0)(typescript@5.0.4):
    resolution: {integrity: sha512-tbsV1jPne5CkFQCgPBcDOt30ItF7aJoZL997JSF7MhGQqOeT3svWRYxiqlfA5RUdlHN6Fi+EI9bxqbdyAUZjYQ==}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/scope-manager': 6.21.0
      '@typescript-eslint/types': 6.21.0
      '@typescript-eslint/typescript-estree': 6.21.0(typescript@5.0.4)
      '@typescript-eslint/visitor-keys': 6.21.0
      debug: 4.3.4
      eslint: 6.6.0
      typescript: 5.0.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/scope-manager@6.21.0:
    resolution: {integrity: sha512-OwLUIWZJry80O99zvqXVEioyniJMa+d2GrqpUTqi5/v5D5rOrppJVBPa0yKCblcigC0/aYAzxxqQ1B+DS2RYsg==}
    engines: {node: ^16.0.0 || >=18.0.0}
    dependencies:
      '@typescript-eslint/types': 6.21.0
      '@typescript-eslint/visitor-keys': 6.21.0
    dev: true

  /@typescript-eslint/type-utils@6.21.0(eslint@6.6.0)(typescript@5.0.4):
    resolution: {integrity: sha512-rZQI7wHfao8qMX3Rd3xqeYSMCL3SoiSQLBATSiVKARdFGCYSRvmViieZjqc58jKgs8Y8i9YvVVhRbHSTA4VBag==}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/typescript-estree': 6.21.0(typescript@5.0.4)
      '@typescript-eslint/utils': 6.21.0(eslint@6.6.0)(typescript@5.0.4)
      debug: 4.3.4
      eslint: 6.6.0
      ts-api-utils: 1.0.2(typescript@5.0.4)
      typescript: 5.0.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/types@6.21.0:
    resolution: {integrity: sha512-1kFmZ1rOm5epu9NZEZm1kckCDGj5UJEf7P1kliH4LKu/RkwpsfqqGmY2OOcUs18lSlQBKLDYBOGxRVtrMN5lpg==}
    engines: {node: ^16.0.0 || >=18.0.0}
    dev: true

  /@typescript-eslint/typescript-estree@6.21.0(typescript@5.0.4):
    resolution: {integrity: sha512-6npJTkZcO+y2/kr+z0hc4HwNfrrP4kNYh57ek7yCNlrBjWQ1Y0OS7jiZTkgumrvkX5HkEKXFZkkdFNkaW2wmUQ==}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      '@typescript-eslint/types': 6.21.0
      '@typescript-eslint/visitor-keys': 6.21.0
      debug: 4.3.4
      globby: 11.1.0
      is-glob: 4.0.3
      minimatch: 9.0.3
      semver: 7.5.4
      ts-api-utils: 1.0.2(typescript@5.0.4)
      typescript: 5.0.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /@typescript-eslint/utils@6.21.0(eslint@6.6.0)(typescript@5.0.4):
    resolution: {integrity: sha512-NfWVaC8HP9T8cbKQxHcsJBY5YE1O33+jpMwN45qzWWaPDZgLIbo12toGMWnmhvCpd3sIxkpDw3Wv1B3dYrbDQQ==}
    engines: {node: ^16.0.0 || >=18.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
    dependencies:
      '@eslint-community/eslint-utils': 4.4.0(eslint@6.6.0)
      '@types/json-schema': 7.0.12
      '@types/semver': 7.5.0
      '@typescript-eslint/scope-manager': 6.21.0
      '@typescript-eslint/types': 6.21.0
      '@typescript-eslint/typescript-estree': 6.21.0(typescript@5.0.4)
      eslint: 6.6.0
      semver: 7.5.4
    transitivePeerDependencies:
      - supports-color
      - typescript
    dev: true

  /@typescript-eslint/visitor-keys@6.21.0:
    resolution: {integrity: sha512-JJtkDduxLi9bivAB+cYOVMtbkqdPOhZ+ZI5LC47MIRrDV4Yn2o+ZnW10Nkmr28xRpSpdJ6Sm42Hjf2+REYXm0A==}
    engines: {node: ^16.0.0 || >=18.0.0}
    dependencies:
      '@typescript-eslint/types': 6.21.0
      eslint-visitor-keys: 3.4.3
    dev: true

  /@umijs/hooks@1.9.3(react@16.14.0):
    resolution: {integrity: sha512-h83Zk0x2oO8HeTZlSLsT4KVh+Me1VoXu+DHmT+cpqR7caBhii0T5c8Weko/pFBkEiB4QKzvWi7Zj+78VSKkI5w==}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      react: ^16.8.6
    dependencies:
      '@umijs/use-request': 1.4.3(react@16.14.0)
      intersection-observer: 0.7.0
      lodash.isequal: 4.5.0
      react: 16.14.0
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
    dev: false

  /@umijs/use-request@1.4.3(react@16.14.0):
    resolution: {integrity: sha512-aH4GCdRnMCaaciygdN0KtCDQdBBh1KyiNUAgYDPX8Y4brmbymEpJViX1FU4isOTbV34WlbkWTiBpR9HIi2ciNQ==}
    peerDependencies:
      react: ^16.8.6
    dependencies:
      lodash.debounce: 4.0.8
      lodash.throttle: 4.1.1
      react: 16.14.0
      umi-request: 1.4.0
    dev: false

  /@webgpu/glslang@0.0.15:
    resolution: {integrity: sha512-niT+Prh3Aff8Uf1MVBVUsaNjFj9rJAKDXuoHIKiQbB+6IUP/3J3JIhBNyZ7lDhytvXxw6ppgnwKZdDJ08UMj4Q==}
    dev: false

  /@webgpu/types@0.0.31:
    resolution: {integrity: sha512-cvvCMSZBT4VsRNtt0lI6XQqvOIIWw6+NRUtnPUMDVDgsI4pCZColz3qzF5QcP9wIYOHEc3jssIBse8UWONKhlQ==}
    dev: false

  /@webgpu/types@0.1.54:
    resolution: {integrity: sha512-81oaalC8LFrXjhsczomEQ0u3jG+TqE6V9QHLA8GNZq/Rnot0KDugu3LhSYSlie8tSdooAN1Hov05asrUUp9qgg==}
    dev: false

  /@xobotyi/scrollbar-width@1.9.5:
    resolution: {integrity: sha512-N8tkAACJx2ww8vFMneJmaAgmjAG1tnVBZJRLRcx061tmsLRZHSEZSLuGWnwPtunsSLvSqXQ2wfp7Mgqg1I+2dQ==}
    dev: false

  /acorn-jsx@5.3.2(acorn@7.4.1):
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: 7.4.1
    dev: true

  /acorn@7.4.1:
    resolution: {integrity: sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  /add-dom-event-listener@1.1.0:
    resolution: {integrity: sha512-WCxx1ixHT0GQU9hb0KI/mhgRQhnU+U3GvwY6ZvVjYq8rsihIGoaIOUbY0yMPBxLH5MDtr0kz3fisWGNcbWW7Jw==}
    dependencies:
      object-assign: 4.1.1
    dev: false

  /aggregate-error@3.1.0:
    resolution: {integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==}
    engines: {node: '>=8'}
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0
    dev: true

  /ahooks@3.8.4(react@16.14.0):
    resolution: {integrity: sha512-39wDEw2ZHvypaT14EpMMk4AzosHWt0z9bulY0BeDsvc9PqJEV+Kjh/4TZfftSsotBMq52iYIOFPd3PR56e0ZJg==}
    engines: {node: '>=8.0.0'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
    dependencies:
      '@babel/runtime': 7.25.6
      dayjs: 1.11.13
      intersection-observer: 0.12.2
      js-cookie: 3.0.5
      lodash: 4.17.21
      react: 16.14.0
      react-fast-compare: 3.2.2
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
      tslib: 2.8.1
    dev: false

  /ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  /align-text@0.1.4:
    resolution: {integrity: sha512-GrTZLRpmp6wIC2ztrWW9MjjTgSKccffgFagbNDOX95/dcjEcYZibYTeaOntySQLcdw1ztBoFkviiUvTMbb9MYg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      kind-of: 3.2.2
      longest: 1.0.1
      repeat-string: 1.6.1
    dev: false

  /amdefine@1.0.1:
    resolution: {integrity: sha512-S2Hw0TtNkMJhIabBwIojKL9YHO5T0n5eNqWJ7Lrlel/zDbftQpxpapi8tZs3X1HWa+u+QeydGmzzNU0m09+Rcg==}
    engines: {node: '>=0.4.2'}
    dev: false

  /animate.css@3.7.2:
    resolution: {integrity: sha512-0bE8zYo7C0KvgOYrSVfrzkbYk6IOTVPNqkiHg2cbyF4Pq/PXzilz4BRWA3hwEUBoMp5VBgrC29lQIZyhRWdBTw==}
    dev: false

  /ansi-colors@4.1.3:
    resolution: {integrity: sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==}
    engines: {node: '>=6'}
    dev: true

  /ansi-escapes@4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}
    dependencies:
      type-fest: 0.21.3
    dev: true

  /ansi-regex@2.1.1:
    resolution: {integrity: sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /ansi-regex@4.1.1:
    resolution: {integrity: sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==}
    engines: {node: '>=6'}
    dev: true

  /ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}
    dev: true

  /ansi-regex@6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==}
    engines: {node: '>=12'}
    dev: true

  /ansi-styles@2.2.1:
    resolution: {integrity: sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}
    dependencies:
      color-convert: 1.9.3
    dev: true

  /ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}
    dependencies:
      color-convert: 2.0.1
    dev: true

  /ansi-styles@5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==}
    engines: {node: '>=10'}
    dev: true

  /ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}
    dev: true

  /ant-path-matcher@0.0.5:
    resolution: {integrity: sha512-ZLhI8wKxFDcp2ASqyxoXlG4EU8skG6TCBGuWuGwpnLhs7RDjamX8LXXrBimQSvp0GKLJyGL8a5rw68uDvijkGg==}
    dependencies:
      string-builder: 0.1.8
      xregexp: 4.4.1
      xregexp-quotemeta: 1.0.7
    dev: true

  /antd-dayjs-webpack-plugin@1.0.6(dayjs@1.11.13):
    resolution: {integrity: sha512-UlK3BfA0iE2c5+Zz/Bd2iPAkT6cICtrKG4/swSik5MZweBHtgmu1aUQCHvICdiv39EAShdZy/edfP6mlkS/xXg==}
    peerDependencies:
      dayjs: '*'
    dependencies:
      dayjs: 1.11.13
    dev: true

  /antd@4.24.9(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-VPgjzGk9o8UZESlaIGLI8Vei7Dudlrd7bopWm+Adw7uevRejtBMAatnQUNo8hACjuGYIW7AYLU6wYe1dorBX0Q==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@ant-design/colors': 6.0.0
      '@ant-design/icons': 4.8.1(react-dom@16.14.0)(react@16.14.0)
      '@ant-design/react-slick': 0.29.2(react@16.14.0)
      '@babel/runtime': 7.22.11
      '@ctrl/tinycolor': 3.6.1
      classnames: 2.5.1
      copy-to-clipboard: 3.3.3
      lodash: 4.17.21
      moment: 2.29.4
      rc-cascader: 3.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-checkbox: 3.0.1(react-dom@16.14.0)(react@16.14.0)
      rc-collapse: 3.4.2(react-dom@16.14.0)(react@16.14.0)
      rc-dialog: 9.0.2(react-dom@16.14.0)(react@16.14.0)
      rc-drawer: 6.1.6(react-dom@16.14.0)(react@16.14.0)
      rc-dropdown: 4.0.1(react-dom@16.14.0)(react@16.14.0)
      rc-field-form: 1.27.4(react-dom@16.14.0)(react@16.14.0)
      rc-image: 5.13.0(react-dom@16.14.0)(react@16.14.0)
      rc-input: 0.1.4(react-dom@16.14.0)(react@16.14.0)
      rc-input-number: 7.3.11(react-dom@16.14.0)(react@16.14.0)
      rc-mentions: 1.13.1(react-dom@16.14.0)(react@16.14.0)
      rc-menu: 9.8.4(react-dom@16.14.0)(react@16.14.0)
      rc-motion: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-notification: 4.6.1(react-dom@16.14.0)(react@16.14.0)
      rc-pagination: 3.2.0(react-dom@16.14.0)(react@16.14.0)
      rc-picker: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-progress: 3.4.2(react-dom@16.14.0)(react@16.14.0)
      rc-rate: 2.9.2(react-dom@16.14.0)(react@16.14.0)
      rc-resize-observer: 1.2.1(react-dom@16.14.0)(react@16.14.0)
      rc-segmented: 2.1.2(react-dom@16.14.0)(react@16.14.0)
      rc-select: 14.1.18(react-dom@16.14.0)(react@16.14.0)
      rc-slider: 10.0.1(react-dom@16.14.0)(react@16.14.0)
      rc-steps: 5.0.0(react-dom@16.14.0)(react@16.14.0)
      rc-switch: 3.2.2(react-dom@16.14.0)(react@16.14.0)
      rc-table: 7.26.0(react-dom@16.14.0)(react@16.14.0)
      rc-tabs: 12.5.10(react-dom@16.14.0)(react@16.14.0)
      rc-textarea: 0.4.7(react-dom@16.14.0)(react@16.14.0)
      rc-tooltip: 5.2.2(react-dom@16.14.0)(react@16.14.0)
      rc-tree: 5.7.10(react-dom@16.14.0)(react@16.14.0)
      rc-tree-select: 5.5.5(react-dom@16.14.0)(react@16.14.0)
      rc-trigger: 5.3.4(react-dom@16.14.0)(react@16.14.0)
      rc-upload: 4.3.4(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.37.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      scroll-into-view-if-needed: 2.2.31
    dev: false

  /any-promise@1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}
    dev: true

  /anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1
    dev: true

  /arg@5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}
    dev: true

  /argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}
    dependencies:
      sprintf-js: 1.0.3
    dev: true

  /argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}
    dev: true

  /aria-query@3.0.0:
    resolution: {integrity: sha512-majUxHgLehQTeSA+hClx+DY09OVUqG3GtezWkF1krgLGNdlDu9l9V8DaqNMWbq4Eddc8wsyDA0hpDUtnYxQEXw==}
    dependencies:
      ast-types-flow: 0.0.7
      commander: 2.20.3
    dev: true

  /arr-union@3.1.0:
    resolution: {integrity: sha512-sKpyeERZ02v1FeCZT8lrfJq5u6goHCtpTAzPwJYe7c8SPFOboNjNg1vz2L4VTn9T4PQxEx13TbXLmYUcS6Ug7Q==}
    engines: {node: '>=0.10.0'}
    dev: false

  /array-buffer-byte-length@1.0.0:
    resolution: {integrity: sha512-LPuwb2P+NrQw3XhxGc36+XSvuBPopovXYTR9Ew++Du9Yb/bx5AzBfrIsBoj0EZUifjQU+sHL21sseZ3jerWO/A==}
    dependencies:
      call-bind: 1.0.8
      is-array-buffer: 3.0.2

  /array-buffer-byte-length@1.0.2:
    resolution: {integrity: sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      is-array-buffer: 3.0.5
    dev: true

  /array-includes@3.1.6:
    resolution: {integrity: sha512-sgTbLvL6cNnw24FnbaDyjmvddQ2ML8arZsgaJhoABMoplz/4QRhtrYS+alr1BUM1Bwp6dhx8vVCBSLG+StwOFw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
      get-intrinsic: 1.2.1
      is-string: 1.0.7
    dev: true

  /array-includes@3.1.8:
    resolution: {integrity: sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.0.0
      get-intrinsic: 1.2.6
      is-string: 1.0.7
    dev: true

  /array-source@0.0.4:
    resolution: {integrity: sha512-frNdc+zBn80vipY+GdcJkLEbMWj3xmzArYApmUGxoiV8uAu/ygcs9icPdsGdA26h0MkHUMW6EN2piIvVx+M5Mw==}
    dev: false

  /array-tree-filter@2.1.0:
    resolution: {integrity: sha512-4ROwICNlNw/Hqa9v+rk5h22KjmzB1JGTMVKP2AKJBOCgb0yL0ASf0+YvCcLNNwquOHNX48jkeZIJ3a+oOQqKcw==}
    dev: false

  /array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}
    dev: true

  /array.prototype.findlastindex@1.2.5:
    resolution: {integrity: sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-shim-unscopables: 1.1.0
    dev: true

  /array.prototype.flat@1.3.3:
    resolution: {integrity: sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-shim-unscopables: 1.1.0
    dev: true

  /array.prototype.flatmap@1.3.3:
    resolution: {integrity: sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-shim-unscopables: 1.1.0
    dev: true

  /arraybuffer.prototype.slice@1.0.1:
    resolution: {integrity: sha512-09x0ZWFEjj4WD8PDbykUwo3t9arLn8NIzmmYEJFpYekOAQjpkGSyrQhNoRTcwwcFRu+ycWF78QZ63oWTqSjBcw==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.0
      call-bind: 1.0.8
      define-properties: 1.2.1
      get-intrinsic: 1.2.1
      is-array-buffer: 3.0.2
      is-shared-array-buffer: 1.0.2

  /arraybuffer.prototype.slice@1.0.4:
    resolution: {integrity: sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      is-array-buffer: 3.0.5
    dev: true

  /as-number@1.0.0:
    resolution: {integrity: sha512-HkI/zLo2AbSRO4fqVkmyf3hms0bJDs3iboHqTrNuwTiCRvdYXM7HFhfhB6Dk51anV2LM/IMB83mtK9mHw4FlAg==}
    dev: false

  /asap@2.0.6:
    resolution: {integrity: sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==}
    dev: false

  /asn1.js@4.10.1:
    resolution: {integrity: sha512-p32cOF5q0Zqs9uBiONKYLm6BClCoBCM5O9JfeUSlnQLBTxYdTK+pW+nXflm8UkKd2UYlEbYz5qEi0JuZR9ckSw==}
    dependencies:
      bn.js: 4.12.1
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: true

  /assert@2.1.0:
    resolution: {integrity: sha512-eLHpSK/Y4nhMJ07gDaAzoX/XAKS8PSaojml3M0DM4JpV1LAi5JOJ/p6H/XWrl8L+DzVEvVCW1z3vWAaB9oTsQw==}
    dependencies:
      call-bind: 1.0.2
      is-nan: 1.3.2
      object-is: 1.1.5
      object.assign: 4.1.4
      util: 0.12.5
    dev: true

  /assign-symbols@1.0.0:
    resolution: {integrity: sha512-Q+JC7Whu8HhmTdBph/Tq59IoRtoy6KAm5zzPv00WdujX82lbAL8K7WVjne7vdCsAmbF4AYaDOPyO3k0kl8qIrw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /ast-types-flow@0.0.7:
    resolution: {integrity: sha512-eBvWn1lvIApYMhzQMsu9ciLfkBY499mFZlNqG+/9WR7PVlroQw0vG30cOQQbaKz3sCEc44TAOu2ykzqXSNnwag==}
    dev: true

  /astral-regex@1.0.0:
    resolution: {integrity: sha512-+Ryf6g3BKoRc7jfp7ad8tM4TtMiaWvbF/1/sQcZPkkS7ag3D5nMBCe2UfOTONtAkaG0tO0ij3C5Lwmf1EiyjHg==}
    engines: {node: '>=4'}
    dev: true

  /astral-regex@2.0.0:
    resolution: {integrity: sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==}
    engines: {node: '>=8'}
    dev: true

  /async-function@1.0.0:
    resolution: {integrity: sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==}
    engines: {node: '>= 0.4'}
    dev: true

  /async-validator@1.11.5:
    resolution: {integrity: sha512-XNtCsMAeAH1pdLMEg1z8/Bb3a8cdCbui9QbJATRFHHHW5kT6+NPI3zSVQUXgikTFITzsg+kYY5NTWhM2Orwt9w==}
    dev: false

  /async-validator@4.2.5:
    resolution: {integrity: sha512-7HhHjtERjqlNbZtqNqy2rckN/SpOOlmDliet+lP7k+eKZEjPk3DgyeU9lIXLdeLz0uBbbVp+9Qdow9wJWgwwfg==}
    dev: false

  /async@3.2.4:
    resolution: {integrity: sha512-iAB+JbDEGXhyIUavoDl9WP/Jj106Kz9DEn1DPgYw5ruDn0e3Wgi3sKFm55sASdGBNOQB8F59d9qQ7deqrHA8wQ==}

  /available-typed-arrays@1.0.5:
    resolution: {integrity: sha512-DMD0KiN46eipeziST1LPP/STfDU0sufISXmjSgvVsoU2tqxctQeASejWcfNtxYKqETM1UxQ8sp2OrSBWpHY6sw==}
    engines: {node: '>= 0.4'}

  /available-typed-arrays@1.0.7:
    resolution: {integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      possible-typed-array-names: 1.1.0
    dev: true

  /axios@0.19.2:
    resolution: {integrity: sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA==}
    deprecated: Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410
    dependencies:
      follow-redirects: 1.5.10
    transitivePeerDependencies:
      - supports-color
    dev: false

  /axobject-query@2.2.0:
    resolution: {integrity: sha512-Td525n+iPOOyUQIeBfcASuG6uJsDOITl7Mds5gFyerkWiX7qhUTdYUBlSgNMyVqtSJqwpt1kXGLdUt6SykLMRA==}
    dev: true

  /babel-eslint@10.0.3(eslint@6.6.0):
    resolution: {integrity: sha512-z3U7eMY6r/3f3/JB9mTsLjyxrv0Yb1zb8PCWCLpguxfCzBIZUwy23R1t/XKewP+8mEN2Ck8Dtr4q20z6ce6SoA==}
    engines: {node: '>=6'}
    deprecated: babel-eslint is now @babel/eslint-parser. This package will no longer receive updates.
    peerDependencies:
      eslint: '>= 4.12.1'
    dependencies:
      '@babel/code-frame': 7.22.10
      '@babel/parser': 7.22.11
      '@babel/traverse': 7.22.11
      '@babel/types': 7.22.11
      eslint: 6.6.0
      eslint-visitor-keys: 1.3.0
      resolve: 1.22.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /babel-plugin-transform-async-to-promises@0.8.18:
    resolution: {integrity: sha512-WpOrF76nUHijnNn10eBGOHZmXQC8JYRME9rOLxStOga7Av2VO53ehVFvVNImMksVtQuL2/7ZNxEgxnx7oo/3Hw==}
    dev: false

  /babel-runtime@6.26.0:
    resolution: {integrity: sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==}
    dependencies:
      core-js: 2.6.12
      regenerator-runtime: 0.11.1
    dev: false

  /balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  /base64-arraybuffer@1.0.2:
    resolution: {integrity: sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ==}
    engines: {node: '>= 0.6.0'}
    dev: false

  /base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  /batch-processor@1.0.0:
    resolution: {integrity: sha512-xoLQD8gmmR32MeuBHgH0Tzd5PuSZx71ZsbhVxOCRbgktZEPe4SQy7s9Z50uPp0F/f7iw2XmkHN2xkgbMfckMDA==}
    dev: false

  /big.js@5.2.2:
    resolution: {integrity: sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==}
    dev: true

  /binary-extensions@2.2.0:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==}
    engines: {node: '>=8'}
    dev: true

  /bn.js@4.12.1:
    resolution: {integrity: sha512-k8TVBiPkPJT9uHLdOKfFpqcfprwBFOAAXXozRubr7R7PfIuKvQlzcI4M0pALeqXN09vdaMbUdUj+pass+uULAg==}
    dev: true

  /bn.js@5.2.1:
    resolution: {integrity: sha512-eXRvHzWyYPBuB4NBy0cmYQjGitUrtqwbvlzP3G6VFnNRbsZQIxQ10PbKKHt8gZ/HW/D/747aDl+QkDqg3KQLMQ==}
    dev: true

  /brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  /brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}
    dependencies:
      balanced-match: 1.0.2
    dev: true

  /braces@3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.0.1

  /braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}
    dependencies:
      fill-range: 7.1.1
    dev: true

  /brorand@1.1.0:
    resolution: {integrity: sha512-cKV8tMCEpQs4hK/ik71d6LrPOnpkpGBR0wzxqr68g2m/LB2GxVYQroAjMJZRVM1Y4BCjCKc3vAamxSzOY2RP+w==}
    dev: true

  /browser-resolve@2.0.0:
    resolution: {integrity: sha512-7sWsQlYL2rGLy2IWm8WL8DCTJvYLc/qlOnsakDac87SOoCd16WLsaAMdCiAqsTNHIe+SXfaqyxyo6THoWqs8WQ==}
    dependencies:
      resolve: 1.22.4
    dev: true

  /browserify-aes@1.2.0:
    resolution: {integrity: sha512-+7CHXqGuspUn/Sl5aO7Ea0xWGAtETPXNSAjHo48JfLdPWcMng33Xe4znFvQweqc/uzk5zSOI3H52CYnjCfb5hA==}
    dependencies:
      buffer-xor: 1.0.3
      cipher-base: 1.0.6
      create-hash: 1.2.0
      evp_bytestokey: 1.0.3
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: true

  /browserify-cipher@1.0.1:
    resolution: {integrity: sha512-sPhkz0ARKbf4rRQt2hTpAHqn47X3llLkUGn+xEJzLjwY8LRs2p0v7ljvI5EyoRO/mexrNunNECisZs+gw2zz1w==}
    dependencies:
      browserify-aes: 1.2.0
      browserify-des: 1.0.2
      evp_bytestokey: 1.0.3
    dev: true

  /browserify-des@1.0.2:
    resolution: {integrity: sha512-BioO1xf3hFwz4kc6iBhI3ieDFompMhrMlnDFC4/0/vd5MokpuAc3R+LYbwTA9A5Yc9pq9UYPqffKpW2ObuwX5A==}
    dependencies:
      cipher-base: 1.0.6
      des.js: 1.1.0
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: true

  /browserify-rsa@4.1.1:
    resolution: {integrity: sha512-YBjSAiTqM04ZVei6sXighu679a3SqWORA3qZTEqZImnlkDIFtKc6pNutpjyZ8RJTjQtuYfeetkxM11GwoYXMIQ==}
    engines: {node: '>= 0.10'}
    dependencies:
      bn.js: 5.2.1
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: true

  /browserify-sign@4.2.3:
    resolution: {integrity: sha512-JWCZW6SKhfhjJxO8Tyiiy+XYB7cqd2S5/+WeYHsKdNKFlCBhKbblba1A/HN/90YwtxKc8tCErjffZl++UNmGiw==}
    engines: {node: '>= 0.12'}
    dependencies:
      bn.js: 5.2.1
      browserify-rsa: 4.1.1
      create-hash: 1.2.0
      create-hmac: 1.1.7
      elliptic: 6.6.1
      hash-base: 3.0.5
      inherits: 2.0.4
      parse-asn1: 5.1.7
      readable-stream: 2.3.8
      safe-buffer: 5.2.1
    dev: true

  /browserify-zlib@0.2.0:
    resolution: {integrity: sha512-Z942RysHXmJrhqk88FmKBVq/v5tqmSkDz7p54G/MGyjMnCFFnC79XWNbg+Vta8W6Wb2qtSZTSxIGkJrRpCFEiA==}
    dependencies:
      pako: 1.0.11
    dev: true

  /buffer-builder@0.2.0:
    resolution: {integrity: sha512-7VPMEPuYznPSoR21NE1zvd2Xna6c/CloiZCfcMXR1Jny6PjX0N4Nsa38zcBFo/FMK+BlA+FLKbJCQ0i2yxp+Xg==}
    dev: true

  /buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}
    dev: false

  /buffer-xor@1.0.3:
    resolution: {integrity: sha512-571s0T7nZWK6vB67HI5dyUF7wXiNcfaPPPTl6zYCNApANjIvYJTg7hlud/+cJpdAhS7dVzqMLmfhfHR3rAcOjQ==}
    dev: true

  /buffer@5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
    dev: true

  /buffer@6.0.3:
    resolution: {integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==}
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
    dev: false

  /builtin-status-codes@3.0.0:
    resolution: {integrity: sha512-HpGFw18DgFWlncDfjTa2rcQ4W88O1mC8e8yZ2AvQY5KDaktSTwo+KRf6nHK6FRI5FyRyb/5T6+TSxfP7QyGsmQ==}
    dev: true

  /bytewise-core@1.2.3:
    resolution: {integrity: sha512-nZD//kc78OOxeYtRlVk8/zXqTB4gf/nlguL1ggWA8FuchMyOxcyHR4QPQZMUmA7czC+YnaBrPUCubqAWe50DaA==}
    dependencies:
      typewise-core: 1.2.0
    dev: false

  /bytewise@1.1.0:
    resolution: {integrity: sha512-rHuuseJ9iQ0na6UDhnrRVDh8YnWVlU6xM3VH6q/+yHDeUH2zIhUzP+2/h3LIrhLDBtTqzWpE3p3tP/boefskKQ==}
    dependencies:
      bytewise-core: 1.2.3
      typewise: 1.0.3
    dev: false

  /call-bind-apply-helpers@1.0.1:
    resolution: {integrity: sha512-BhYE+WDaywFg2TBWYNXAE+8B1ATnThNBqXHP5nQu0jWJdVvY2hvkpyB3qOmtmDePiS5/BDQ8wASEWGMWRG148g==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  /call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  /call-bind@1.0.2:
    resolution: {integrity: sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==}
    dependencies:
      function-bind: 1.1.1
      get-intrinsic: 1.2.1

  /call-bind@1.0.8:
    resolution: {integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.1
      es-define-property: 1.0.1
      get-intrinsic: 1.2.6
      set-function-length: 1.2.2

  /call-bound@1.0.3:
    resolution: {integrity: sha512-YTd+6wGlNlPxSuri7Y6X8tY2dmm12UMH66RpKMhiX6rsk5wXXnYgbUcOt8kiS31/AjfoTOvCsE+w8nZQLQnzHA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.1
      get-intrinsic: 1.2.6

  /callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}
    dev: true

  /camelcase-css@2.0.1:
    resolution: {integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==}
    engines: {node: '>= 6'}
    dev: true

  /camelcase@1.2.1:
    resolution: {integrity: sha512-wzLkDa4K/mzI1OSITC+DUyjgIl/ETNHE9QvYgy6J6Jvqyyz4C0Xfd+lQhb19sX2jMpZV4IssUn0VDVmglV+s4g==}
    engines: {node: '>=0.10.0'}
    dev: false

  /caniuse-lite@1.0.30001706:
    resolution: {integrity: sha512-3ZczoTApMAZwPKYWmwVbQMFpXBDds3/0VciVoUwPUbldlYyVLmRVuRs/PcUZtHpbLRpzzDvrvnFuREsGt6lUug==}
    dev: true

  /center-align@0.1.3:
    resolution: {integrity: sha512-Baz3aNe2gd2LP2qk5U+sDk/m4oSuwSDcBfayTCTBoWpfIGO5XFxPmjILQII4NGiZjD6DoDI6kf7gKaxkf7s3VQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      align-text: 0.1.4
      lazy-cache: 1.0.4
    dev: false

  /chalk@1.1.3:
    resolution: {integrity: sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0
    dev: false

  /chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0
    dev: true

  /chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0
    dev: true

  /chardet@0.7.0:
    resolution: {integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==}
    dev: true

  /chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==}
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3
    dev: true

  /chokidar@4.0.1:
    resolution: {integrity: sha512-n8enUVCED/KVRQlab1hr3MVpcVMvxtZjmEa956u+4YijlmQED223XMSYj2tLuKvr4jcCTzNNMpQDUer72MMmzA==}
    engines: {node: '>= 14.16.0'}
    dependencies:
      readdirp: 4.0.2

  /ci-info@2.0.0:
    resolution: {integrity: sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==}
    dev: true

  /ci-info@3.8.0:
    resolution: {integrity: sha512-eXTggHWSooYhq49F2opQhuHWgzucfF2YgODK4e1566GQs5BIfP30B0oenwBJHfWxAs2fyPB1s7Mg949zLf61Yw==}
    engines: {node: '>=8'}
    dev: true

  /cipher-base@1.0.6:
    resolution: {integrity: sha512-3Ek9H3X6pj5TgenXYtNWdaBon1tgYCaebd+XPg0keyjEbEfkD4KkmAxkQ/i1vYvxdcT5nscLBfq9VJRmCBcFSw==}
    engines: {node: '>= 0.10'}
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: true

  /clamp@1.0.1:
    resolution: {integrity: sha512-kgMuFyE78OC6Dyu3Dy7vcx4uy97EIbVxJB/B0eJ3bUNAkwdNcxYzgKltnyADiYwsR7SEqkkUPsEUT//OVS6XMA==}
    dev: false

  /classnames@2.2.6:
    resolution: {integrity: sha512-JR/iSQOSt+LQIWwrwEzJ9uk0xfN3mTVYMwt1Ir5mUcSN6pU+V4zQFFaJsclJbPuAUQH+yfWef6tm7l1quW3C8Q==}
    dev: false

  /classnames@2.5.1:
    resolution: {integrity: sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow==}
    dev: false

  /clean-stack@2.2.0:
    resolution: {integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==}
    engines: {node: '>=6'}
    dev: true

  /cli-cursor@3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: 3.1.0
    dev: true

  /cli-truncate@2.1.0:
    resolution: {integrity: sha512-n8fOixwDD6b/ObinzTrp1ZKFzbgvKZvuz/TvejnLn1aQfC6r52XEx85FmuC+3HI+JM7coBRXUvNqEU2PHVrHpg==}
    engines: {node: '>=8'}
    dependencies:
      slice-ansi: 3.0.0
      string-width: 4.2.3
    dev: true

  /cli-width@3.0.0:
    resolution: {integrity: sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==}
    engines: {node: '>= 10'}
    dev: true

  /cliui@2.1.0:
    resolution: {integrity: sha512-GIOYRizG+TGoc7Wgc1LiOTLare95R3mzKgoln+Q/lE4ceiYH19gUpl0l0Ffq4lJDEf3FxujMe6IBfOCs7pfqNA==}
    dependencies:
      center-align: 0.1.3
      right-align: 0.1.3
      wordwrap: 0.0.2
    dev: false

  /clsx@1.2.1:
    resolution: {integrity: sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==}
    engines: {node: '>=6'}
    dev: false

  /clsx@2.1.1:
    resolution: {integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==}
    engines: {node: '>=6'}
    dev: false

  /codemirror@5.65.18:
    resolution: {integrity: sha512-Gaz4gHnkbHMGgahNt3CA5HBk5lLQBqmD/pBgeB4kQU6OedZmqMBjlRF0LSrp2tJ4wlLNPm2FfaUd1pDy0mdlpA==}
    dev: false

  /color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}
    dependencies:
      color-name: 1.1.3

  /color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: 1.1.4
    dev: true

  /color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  /color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  /color-string@1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2
    dev: false

  /color@3.2.1:
    resolution: {integrity: sha512-aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA==}
    dependencies:
      color-convert: 1.9.3
      color-string: 1.9.1
    dev: false

  /colorette@2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==}
    dev: true

  /colorjs.io@0.5.2:
    resolution: {integrity: sha512-twmVoizEW7ylZSN32OgKdXRmo1qg+wT5/6C3xu5b9QsWzSFAhHLn2xd8ro0diCsKfCj1RdaTP/nrcW+vAoQPIw==}
    dev: true

  /commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  /commander@4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}
    dev: true

  /commander@6.2.1:
    resolution: {integrity: sha512-U7VdrJFnJgo4xjrHpTzu0yrHPGImdsmD95ZlgYSEajAn2JKzDhDTPG9kBTefmObL2w/ngeZnilk+OV9CG3d7UA==}
    engines: {node: '>= 6'}
    dev: true

  /compare-versions@3.6.0:
    resolution: {integrity: sha512-W6Af2Iw1z4CB7q4uU4hv646dW9GQuBM+YpC0UvUCWSD8w90SJjp+ujJuXaEMtAXBtSqGfMPuFOVn4/+FlaqfBA==}
    dev: true

  /component-classes@1.2.6:
    resolution: {integrity: sha512-hPFGULxdwugu1QWW3SvVOCUHLzO34+a2J6Wqy0c5ASQkfi9/8nZcBB0ZohaEbXOQlCflMAEMmEWk7u7BVs4koA==}
    dependencies:
      component-indexof: 0.0.3
    dev: false

  /component-indexof@0.0.3:
    resolution: {integrity: sha512-puDQKvx/64HZXb4hBwIcvQLaLgux8o1CbWl39s41hrIIZDl1lJiD5jc22gj3RBeGK0ovxALDYpIbyjqDUUl0rw==}
    dev: false

  /compute-scroll-into-view@1.0.20:
    resolution: {integrity: sha512-UCB0ioiyj8CRjtrvaceBLqqhZCVP+1B8+NWQhmdsm0VXOJtobBCf1dBQmebCCo34qZmUwZfIH2MZLqNHazrfjg==}
    dev: false

  /concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  /concat-stream@1.4.11:
    resolution: {integrity: sha512-X3JMh8+4je3U1cQpG87+f9lXHDrqcb2MVLg9L7o8b1UZ0DzhRrUpdn65ttzu10PpJPPI3MQNkis+oha6TSA9Mw==}
    engines: {'0': node >= 0.8}
    dependencies:
      inherits: 2.0.4
      readable-stream: 1.1.14
      typedarray: 0.0.7
    dev: false

  /concat-stream@2.0.0:
    resolution: {integrity: sha512-MWufYdFw53ccGjCA+Ol7XJYpAlW6/prSMzuPOTRnJGcGzuhLn4Scrz7qf6o8bROZ514ltazcIFJZevcfbo0x7A==}
    engines: {'0': node >= 6.0}
    dependencies:
      buffer-from: 1.1.2
      inherits: 2.0.4
      readable-stream: 3.6.2
      typedarray: 0.0.6
    dev: false

  /concaveman@1.2.1:
    resolution: {integrity: sha512-PwZYKaM/ckQSa8peP5JpVr7IMJ4Nn/MHIaWUjP4be+KoZ7Botgs8seAZGpmaOM+UZXawcdYRao/px9ycrCihHw==}
    dependencies:
      point-in-polygon: 1.1.0
      rbush: 3.0.1
      robust-predicates: 2.0.4
      tinyqueue: 2.0.3
    dev: false

  /confusing-browser-globals@1.0.11:
    resolution: {integrity: sha512-JsPKdmh8ZkmnHxDk55FZ1TqVLvEQTvoByJZRN9jzI0UjxK/QgAmsphz7PGtqgPieQZ/CQcHWXCR7ATDNhGe+YA==}
    dev: true

  /console-browserify@1.2.0:
    resolution: {integrity: sha512-ZMkYO/LkF17QvCPqM0gxw8yUzigAOZOSWSHg91FH6orS7vcEj5dVZTidN2fQ14yBSdg97RqhSNwLUXInd52OTA==}
    dev: true

  /constants-browserify@1.0.0:
    resolution: {integrity: sha512-xFxOwqIzR/e1k1gLiWEophSCMqXcwVHIH7akf7b/vxcUeGunlj3hvZaaqxwHsTgn+IndtkQJgSztIDWeumWJDQ==}
    dev: true

  /contour_plot@0.0.1:
    resolution: {integrity: sha512-Nil2HI76Xux6sVGORvhSS8v66m+/h5CwFkBJDO+U5vWaMdNC0yXNCsGDPbzPhvqOEU5koebhdEvD372LI+IyLw==}
    dev: false

  /copy-to-clipboard@3.3.3:
    resolution: {integrity: sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==}
    dependencies:
      toggle-selection: 1.0.6
    dev: false

  /core-js-pure@3.32.1:
    resolution: {integrity: sha512-f52QZwkFVDPf7UEQZGHKx6NYxsxmVGJe5DIvbzOdRMJlmT6yv0KDjR8rmy3ngr/t5wU54c7Sp/qIJH0ppbhVpQ==}
    requiresBuild: true
    dev: true

  /core-js@1.2.7:
    resolution: {integrity: sha512-ZiPp9pZlgxpWRu0M+YWbm6+aQ84XEfH1JRXvfOc/fILWI0VKhLC2LX13X1NYq4fULzLMq7Hfh43CSo2/aIaUPA==}
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.
    dev: false

  /core-js@2.6.12:
    resolution: {integrity: sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==}
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.
    requiresBuild: true
    dev: false

  /core-js@3.41.0:
    resolution: {integrity: sha512-SJ4/EHwS36QMJd6h/Rg+GyR4A5xE0FSI3eZ+iBVpfqf1x0eTSg1smWLHrA+2jQThZSh97fmSgFSU8B61nxosxA==}
    requiresBuild: true
    dev: true

  /core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  /cosmiconfig@7.1.0:
    resolution: {integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==}
    engines: {node: '>=10'}
    dependencies:
      '@types/parse-json': 4.0.0
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2
    dev: true

  /cosmiconfig@9.0.0(typescript@5.0.4):
    resolution: {integrity: sha512-itvL5h8RETACmOTFc4UfIyB2RfEHi71Ax6E/PivVxq9NseKbOWpeyHEOIbmAw1rs8Ak0VursQNww7lf7YtUwzg==}
    engines: {node: '>=14'}
    peerDependencies:
      typescript: '>=4.9.5'
    peerDependenciesMeta:
      typescript:
        optional: true
    dependencies:
      env-paths: 2.2.1
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      parse-json: 5.2.0
      typescript: 5.0.4
    dev: true

  /create-ecdh@4.0.4:
    resolution: {integrity: sha512-mf+TCx8wWc9VpuxfP2ht0iSISLZnt0JgWlrOKZiNqyUZWnjIaCIVNQArMHnCZKfEYRg6IM7A+NeJoN8gf/Ws0A==}
    dependencies:
      bn.js: 4.12.1
      elliptic: 6.6.1
    dev: true

  /create-hash@1.2.0:
    resolution: {integrity: sha512-z00bCGNHDG8mHAkP7CtT1qVu+bFQUPjYq/4Iv3C3kWjTFV10zIjfSoeqXo9Asws8gwSHDGj/hl2u4OGIjapeCg==}
    dependencies:
      cipher-base: 1.0.6
      inherits: 2.0.4
      md5.js: 1.3.5
      ripemd160: 2.0.2
      sha.js: 2.4.11
    dev: true

  /create-hmac@1.1.7:
    resolution: {integrity: sha512-MJG9liiZ+ogc4TzUwuvbER1JRdgvUFSB5+VR/g5h82fGaIRWMWddtKBHi7/sVhfjQZ6SehlyhvQYrcYkaUIpLg==}
    dependencies:
      cipher-base: 1.0.6
      create-hash: 1.2.0
      inherits: 2.0.4
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11
    dev: true

  /create-react-class@15.7.0:
    resolution: {integrity: sha512-QZv4sFWG9S5RUvkTYWbflxeZX+JG7Cz0Tn33rQBJ+WFQTqTfUTjMjiv9tnfXazjsO5r0KhPs+AqCjyrQX6h2ng==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
    dev: false

  /create-require@1.1.1:
    resolution: {integrity: sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==}
    dev: true

  /cross-spawn@6.0.5:
    resolution: {integrity: sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==}
    engines: {node: '>=4.8'}
    dependencies:
      nice-try: 1.0.5
      path-key: 2.0.1
      semver: 5.7.2
      shebang-command: 1.2.0
      which: 1.3.1
    dev: true

  /cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: true

  /cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2
    dev: true

  /crypto-browserify@3.12.1:
    resolution: {integrity: sha512-r4ESw/IlusD17lgQi1O20Fa3qNnsckR126TdUuBgAu7GBYSIPvdNyONd3Zrxh0xCwA4+6w/TDArBPsMvhur+KQ==}
    engines: {node: '>= 0.10'}
    dependencies:
      browserify-cipher: 1.0.1
      browserify-sign: 4.2.3
      create-ecdh: 4.0.4
      create-hash: 1.2.0
      create-hmac: 1.1.7
      diffie-hellman: 5.0.3
      hash-base: 3.0.5
      inherits: 2.0.4
      pbkdf2: 3.1.2
      public-encrypt: 4.0.3
      randombytes: 2.1.0
      randomfill: 1.0.4
    dev: true

  /css-animation@1.6.1:
    resolution: {integrity: sha512-/48+/BaEaHRY6kNQ2OIPzKf9A6g8WjZYjhiNDNuIVbsm5tXCGIAsHDjB4Xu1C4vXJtUWZo26O68OQkDpNBaPog==}
    dependencies:
      babel-runtime: 6.26.0
      component-classes: 1.2.6
    dev: false

  /css-in-js-utils@3.1.0:
    resolution: {integrity: sha512-fJAcud6B3rRu+KHYk+Bwf+WFL2MDCJJ1XG9x137tJQ0xYxor7XziQtuGFbWNdqrvF4Tk26O3H73nfVqXt/fW1A==}
    dependencies:
      hyphenate-style-name: 1.0.4
    dev: false

  /css-line-break@2.1.0:
    resolution: {integrity: sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w==}
    dependencies:
      utrie: 1.0.2
    dev: false

  /css-tree@1.1.3:
    resolution: {integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==}
    engines: {node: '>=8.0.0'}
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1
    dev: false

  /csscolorparser@1.0.3:
    resolution: {integrity: sha512-umPSgYwZkdFoUrH5hIq5kf0wPSXiro51nPw0j2K/c83KflkPSTBGMz6NJvMB+07VlL0y7VPo6QJcDjcgKTTm3w==}
    dev: false

  /cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /csstype@3.1.2:
    resolution: {integrity: sha512-I7K1Uu0MBPzaFKg4nI5Q7Vs2t+3gWWW648spaF+Rg7pI9ds18Ugn+lvg4SHczUdKlHI5LWBXyqfS8+DufyBsgQ==}

  /d3-array@1.2.4:
    resolution: {integrity: sha512-KHW6M86R+FUPYGb3R5XiYjXPq7VzwxZ22buHhAEVG5ztoEcZZMLov530mmccaqA1GghZArjQV46fuc8kUqhhHw==}
    dev: false

  /d3-array@2.12.1:
    resolution: {integrity: sha512-B0ErZK/66mHtEsR1TkPEEkwdy+WDesimkM5gpZr5Dsg54BiTA5RXtYW5qTLIAcekaS9xfZrzBLF/OAkB3Qn1YQ==}
    dependencies:
      internmap: 1.0.1
    dev: false

  /d3-collection@1.0.7:
    resolution: {integrity: sha512-ii0/r5f4sjKNTfh84Di+DpztYwqKhEyUlKoPrzUFfeSkWxjW49xU2QzO9qrPrNkpdI0XJkfzvmTu8V2Zylln6A==}
    dev: false

  /d3-color@1.4.1:
    resolution: {integrity: sha512-p2sTHSLCJI2QKunbGb7ocOh7DgTAn8IrLx21QRc/BSnodXM4sv6aLQlnfpvehFMLZEfBc6g9pH9SWQccFYfJ9Q==}
    dev: false

  /d3-color@3.1.0:
    resolution: {integrity: sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==}
    engines: {node: '>=12'}
    dev: false

  /d3-dispatch@2.0.0:
    resolution: {integrity: sha512-S/m2VsXI7gAti2pBoLClFFTMOO1HTtT0j99AuXLoGFKO6deHDdnv6ZGTxSTTUTgO1zVcv82fCOtDjYK4EECmWA==}
    dev: false

  /d3-dsv@1.2.0:
    resolution: {integrity: sha512-9yVlqvZcSOMhCYzniHE7EVUws7Fa1zgw+/EAV2BxJoG3ME19V6BQFBwI855XQDsxyOuG7NibqRMTtiF/Qup46g==}
    hasBin: true
    dependencies:
      commander: 2.20.3
      iconv-lite: 0.4.24
      rw: 1.3.3
    dev: false

  /d3-ease@1.0.7:
    resolution: {integrity: sha512-lx14ZPYkhNx0s/2HX5sLFUI3mbasHjSSpwO/KaaNACweVwxUruKyWVcb293wMv1RqTPZyZ8kSZ2NogUZNcLOFQ==}
    dev: false

  /d3-force@2.1.1:
    resolution: {integrity: sha512-nAuHEzBqMvpFVMf9OX75d00OxvOXdxY+xECIXjW6Gv8BRrXu6gAWbv/9XKrvfJ5i5DCokDW7RYE50LRoK092ew==}
    dependencies:
      d3-dispatch: 2.0.0
      d3-quadtree: 2.0.0
      d3-timer: 2.0.0
    dev: false

  /d3-format@1.4.5:
    resolution: {integrity: sha512-J0piedu6Z8iB6TbIGfZgDzfXxUFN3qQRMofy2oPdXzQibYGqPB/9iMcxr/TGalU+2RsyDO+U4f33id8tbnSRMQ==}
    dev: false

  /d3-geo@1.7.1:
    resolution: {integrity: sha512-O4AempWAr+P5qbk2bC2FuN/sDW4z+dN2wDf9QV3bxQt4M5HfOEeXLgJ/UKQW0+o1Dj8BE+L5kiDbdWUMjsmQpw==}
    dependencies:
      d3-array: 1.2.4
    dev: false

  /d3-hexbin@0.2.2:
    resolution: {integrity: sha512-KS3fUT2ReD4RlGCjvCEm1RgMtp2NFZumdMu4DBzQK8AZv3fXRM6Xm8I4fSU07UXvH4xxg03NwWKWdvxfS/yc4w==}
    dev: false

  /d3-hierarchy@2.0.0:
    resolution: {integrity: sha512-SwIdqM3HxQX2214EG9GTjgmCc/mbSx4mQBn+DuEETubhOw6/U3fmnji4uCVrmzOydMHSO1nZle5gh6HB/wdOzw==}
    dev: false

  /d3-interpolate@1.4.0:
    resolution: {integrity: sha512-V9znK0zc3jOPV4VD2zZn0sDhZU3WAE2bmlxdIwwQPPzPjvyLkd8B3JUVdS1IDUFDkWZ72c9qnv1GK2ZagTZ8EA==}
    dependencies:
      d3-color: 1.4.1
    dev: false

  /d3-interpolate@3.0.1:
    resolution: {integrity: sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==}
    engines: {node: '>=12'}
    dependencies:
      d3-color: 3.1.0
    dev: false

  /d3-quadtree@2.0.0:
    resolution: {integrity: sha512-b0Ed2t1UUalJpc3qXzKi+cPGxeXRr4KU9YSlocN74aTzp6R/Ud43t79yLLqxHRWZfsvWXmbDWPpoENK1K539xw==}
    dev: false

  /d3-regression@1.3.10:
    resolution: {integrity: sha512-PF8GWEL70cHHWpx2jUQXc68r1pyPHIA+St16muk/XRokETzlegj5LriNKg7o4LR0TySug4nHYPJNNRz/W+/Niw==}
    dev: false

  /d3-scale@2.2.2:
    resolution: {integrity: sha512-LbeEvGgIb8UMcAa0EATLNX0lelKWGYDQiPdHj+gLblGVhGLyNbaCn3EvrJf0A3Y/uOOU5aD6MTh5ZFCdEwGiCw==}
    dependencies:
      d3-array: 1.2.4
      d3-collection: 1.0.7
      d3-format: 1.4.5
      d3-interpolate: 1.4.0
      d3-time: 1.1.0
      d3-time-format: 2.3.0
    dev: false

  /d3-time-format@2.3.0:
    resolution: {integrity: sha512-guv6b2H37s2Uq/GefleCDtbe0XZAuy7Wa49VGkPVPMfLL9qObgBST3lEHJBMUp8S7NdLQAGIvr2KXk8Hc98iKQ==}
    dependencies:
      d3-time: 1.1.0
    dev: false

  /d3-time@1.1.0:
    resolution: {integrity: sha512-Xh0isrZ5rPYYdqhAVk8VLnMEidhz5aP7htAADH6MfzgmmicPkTo8LhkLxci61/lCB7n7UmE3bN0leRt+qvkLxA==}
    dev: false

  /d3-timer@1.0.10:
    resolution: {integrity: sha512-B1JDm0XDaQC+uvo4DT79H0XmBskgS3l6Ve+1SBCfxgmtIb1AVrPIoqd+nPSv+loMX8szQ0sVUhGngL7D5QPiXw==}
    dev: false

  /d3-timer@2.0.0:
    resolution: {integrity: sha512-TO4VLh0/420Y/9dO3+f9abDEFYeCUr2WZRlxJvbp4HPTQcSylXNiL6yZa9FIUvV1yRiFufl1bszTCLDqv9PWNA==}
    dev: false

  /d3-voronoi@1.1.2:
    resolution: {integrity: sha512-RhGS1u2vavcO7ay7ZNAPo4xeDh/VYeGof3x5ZLJBQgYhLegxr3s5IykvWmJ94FTU6mcbtp4sloqZ54mP6R4Utw==}
    dev: false

  /dagre-compound@0.0.11:
    resolution: {integrity: sha512-UrSgRP9LtOZCYb9e5doolZXpc7xayyszgyOs7uakTK4n4KsLegLVTRRtq01GpQd/iZjYw5fWMapx9ed+c80MAQ==}
    engines: {node: '>=6.0.0'}
    peerDependencies:
      dagre: ^0.8.5
    dev: false

  /dagre-compound@0.0.11(dagre@0.8.5):
    resolution: {integrity: sha512-UrSgRP9LtOZCYb9e5doolZXpc7xayyszgyOs7uakTK4n4KsLegLVTRRtq01GpQd/iZjYw5fWMapx9ed+c80MAQ==}
    engines: {node: '>=6.0.0'}
    peerDependencies:
      dagre: ^0.8.5
    dependencies:
      dagre: 0.8.5
    dev: false

  /dagre@0.8.5:
    resolution: {integrity: sha512-/aTqmnRta7x7MCCpExk7HQL2O4owCT2h8NT//9I1OQ9vt29Pa0BzSAkR5lwFUcQ7491yVi/3CXU9jQ5o0Mn2Sw==}
    dependencies:
      graphlib: 2.1.8
      lodash: 4.17.21
    dev: false

  /damerau-levenshtein@1.0.8:
    resolution: {integrity: sha512-sdQSFB7+llfUcQHUQO3+B8ERRj0Oa4w9POWMI/puGtuf7gFywGmkaLCElnudfTiKZV+NvHqL0ifzdrI8Ro7ESA==}
    dev: true

  /data-view-buffer@1.0.2:
    resolution: {integrity: sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-data-view: 1.0.2
    dev: true

  /data-view-byte-length@1.0.2:
    resolution: {integrity: sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-data-view: 1.0.2
    dev: true

  /data-view-byte-offset@1.0.1:
    resolution: {integrity: sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-data-view: 1.0.2
    dev: true

  /date-fns@2.30.0:
    resolution: {integrity: sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==}
    engines: {node: '>=0.11'}
    dependencies:
      '@babel/runtime': 7.25.6
    dev: false

  /dayjs@1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==}

  /debug@3.1.0:
    resolution: {integrity: sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.0.0
    dev: false

  /debug@3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.3
    dev: true

  /debug@4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: 2.1.2

  /decamelize@1.2.0:
    resolution: {integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /dedent@0.7.0:
    resolution: {integrity: sha512-Q6fKUPqnAHAyhiUgFU7BUzLiv0kd8saH9al7tnu5Q/okj6dnupxyTgFIBjVzJATdfIAm9NAsvXNzjaKa+bxVyA==}
    dev: true

  /deep-equal@1.1.1:
    resolution: {integrity: sha512-yd9c5AdiqVcR+JjcwUQb9DkhJc8ngNr0MahEBGvDiJw8puWab2yZlh+nkasOnZP+EGTAP6rRp2JzJhJZzvNF8g==}
    dependencies:
      is-arguments: 1.1.1
      is-date-object: 1.1.0
      is-regex: 1.2.1
      object-is: 1.1.5
      object-keys: 1.1.1
      regexp.prototype.flags: 1.5.4
    dev: false

  /deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}
    dev: true

  /deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}
    dev: true

  /define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  /define-properties@1.2.0:
    resolution: {integrity: sha512-xvqAVKGfT1+UAvPwKTVw/njhdQ8ZhXK4lI0bCIuCMrp2up9nPnaDftrLtmpTazqd1o+UY4zgzU+avtMbDP+ldA==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-property-descriptors: 1.0.0
      object-keys: 1.1.1

  /define-properties@1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.0
      object-keys: 1.1.1

  /defined@1.0.1:
    resolution: {integrity: sha512-hsBd2qSVCRE+5PmNdHt1uzyrFu5d3RwmFDKzyNZMFq/EwDNJF7Ee5+D5oEKF0hU6LhtoUF1macFvOe4AskQC1Q==}
    dev: false

  /density-clustering@1.3.0:
    resolution: {integrity: sha512-icpmBubVTwLnsaor9qH/4tG5+7+f61VcqMN3V3pm9sxxSCt2Jcs0zWOgwZW9ARJYaKD3FumIgHiMOcIMRRAzFQ==}
    dev: false

  /des.js@1.1.0:
    resolution: {integrity: sha512-r17GxjhUCjSRy8aiJpr8/UadFIzMzJGexI3Nmz4ADi9LYSFx4gTBp80+NaX/YsXWWLhpZ7v/v/ubEc/bCNfKwg==}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: true

  /detect-browser@5.3.0:
    resolution: {integrity: sha512-53rsFbGdwMwlF7qvCt0ypLM5V5/Mbl0szB7GPN8y9NCcbknYOeVVXdrXEq+90IwAfrrzt6Hd+u2E2ntakICU8w==}
    dev: false

  /detect-libc@1.0.3:
    resolution: {integrity: sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==}
    engines: {node: '>=0.10'}
    hasBin: true
    optional: true

  /didyoumean@1.2.2:
    resolution: {integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==}
    dev: true

  /diff-sequences@29.6.3:
    resolution: {integrity: sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dev: true

  /diffie-hellman@5.0.3:
    resolution: {integrity: sha512-kqag/Nl+f3GwyK25fhUMYj81BUOrZ9IuJsjIcDE5icNM9FJHAVm3VcUDxdLPoQtTuUylWm6ZIknYJwwaPxsUzg==}
    dependencies:
      bn.js: 4.12.1
      miller-rabin: 4.0.1
      randombytes: 2.1.0
    dev: true

  /dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}
    dependencies:
      path-type: 4.0.0
    dev: true

  /dlv@1.1.3:
    resolution: {integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==}
    dev: true

  /dnd-core@11.1.3:
    resolution: {integrity: sha512-QugF55dNW+h+vzxVJ/LSJeTeUw9MCJ2cllhmVThVPEtF16ooBkxj0WBE5RB+AceFxMFo1rO6bJKXtqKl+JNnyA==}
    dependencies:
      '@react-dnd/asap': 4.0.1
      '@react-dnd/invariant': 2.0.0
      redux: 4.2.1
    dev: false

  /doctrine@2.1.0:
    resolution: {integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /doctrine@3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}
    dependencies:
      esutils: 2.0.3
    dev: true

  /dom-align@1.12.4:
    resolution: {integrity: sha512-R8LUSEay/68zE5c8/3BDxiTEvgb4xZTF0RKmAHfiEVN3klfIpXfi2/QCoiWPccVQ0J/ZGdz9OjzL4uJEP/MRAw==}
    dev: false

  /dom-scroll-into-view@1.2.1:
    resolution: {integrity: sha512-LwNVg3GJOprWDO+QhLL1Z9MMgWe/KAFLxVWKzjRTxNSPn8/LLDIfmuG71YHznXCqaqTjvHJDYO1MEAgX6XCNbQ==}
    dev: false

  /domain-browser@4.22.0:
    resolution: {integrity: sha512-IGBwjF7tNk3cwypFNH/7bfzBcgSCbaMOD3GsaY1AU/JRrnHnYgEM0+9kQt52iZxjNsjBtJYtao146V+f8jFZNw==}
    engines: {node: '>=10'}
    dev: true

  /dotignore@0.1.2:
    resolution: {integrity: sha512-UGGGWfSauusaVJC+8fgV+NVvBXkCTmVv7sk6nojDZZvuOUNGUy0Zk4UpHQD6EDjS0jpBwcACvH4eofvyzBcRDw==}
    hasBin: true
    dependencies:
      minimatch: 3.1.2
    dev: false

  /draft-js@0.10.5(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-LE6jSCV9nkPhfVX2ggcRLA4FKs6zWq9ceuO/88BpXdNCS7mjRTgs0NsV6piUCJX9YxMsB9An33wnkMmU2sD2Zg==}
    peerDependencies:
      react: ^0.14.0 || ^15.0.0-rc || ^16.0.0-rc || ^16.0.0
      react-dom: ^0.14.0 || ^15.0.0-rc || ^16.0.0-rc || ^16.0.0
    dependencies:
      fbjs: 0.8.18
      immutable: 3.7.6
      object-assign: 4.1.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  /earcut@2.2.4:
    resolution: {integrity: sha512-/pjZsA1b4RPHbeWZQn66SWS8nZZWLQQ23oE3Eam7aroEFGEvwKAsJfZ9ytiEMycfzXWpca4FA9QIOehf7PocBQ==}
    dev: false

  /eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}
    dev: true

  /element-resize-detector@1.2.4:
    resolution: {integrity: sha512-Fl5Ftk6WwXE0wqCgNoseKWndjzZlDCwuPTcoVZfCP9R3EHQF8qUtr3YUPNETegRBOKqQKPW3n4kiIWngGi8tKg==}
    dependencies:
      batch-processor: 1.0.0
    dev: false

  /element-resize-event@3.0.6:
    resolution: {integrity: sha512-sSeXY9rNDp86bJODW68pxLcy3A5FrPZfIgOrJHzqgYzX513Zq6/ytdBigp7KeJEpZZopBBSiO1cVuiRkZpNxLw==}
    dev: false

  /elliptic@6.6.1:
    resolution: {integrity: sha512-RaddvvMatK2LJHqFJ+YA4WysVN5Ita9E35botqIYspQ4TkRAlCicdzKOjlyv/1Za5RyTNn7di//eEV0uTAfe3g==}
    dependencies:
      bn.js: 4.12.1
      brorand: 1.1.0
      hash.js: 1.1.7
      hmac-drbg: 1.0.1
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1
    dev: true

  /emoji-mart@5.6.0:
    resolution: {integrity: sha512-eJp3QRe79pjwa+duv+n7+5YsNhRcMl812EcFVwrnRvYKoNPoQb5qxU8DG6Bgwji0akHdp6D4Ln6tYLG58MFSow==}
    dev: false

  /emoji-regex@7.0.3:
    resolution: {integrity: sha512-CwBLREIQ7LvYFB0WyRvwhq5N5qPhc6PMjD6bYggFlI5YyDgl+0vxq5VHbMOFqLg7hfWzmu8T5Z1QofhmTIhItA==}
    dev: true

  /emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}
    dev: true

  /emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}
    dev: true

  /emojis-list@3.0.0:
    resolution: {integrity: sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==}
    engines: {node: '>= 4'}
    dev: true

  /encoding@0.1.13:
    resolution: {integrity: sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==}
    dependencies:
      iconv-lite: 0.6.3
    dev: false

  /end-of-stream@1.4.4:
    resolution: {integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==}
    dependencies:
      once: 1.4.0
    dev: true

  /enquire.js@2.1.6:
    resolution: {integrity: sha512-/KujNpO+PT63F7Hlpu4h3pE3TokKRHN26JYmQpPyjkRD/N57R7bPDNojMXdi7uveAKjYB7yQnartCxZnFWr0Xw==}
    dev: false

  /enquirer@2.4.1:
    resolution: {integrity: sha512-rRqJg/6gd538VHvR3PSrdRBb/1Vy2YfzHqzvbhGIQpDRKIa4FgV/54b5Q1xYSxOOwKvjXweS26E0Q+nAMwp2pQ==}
    engines: {node: '>=8.6'}
    dependencies:
      ansi-colors: 4.1.3
      strip-ansi: 6.0.1
    dev: true

  /env-paths@2.2.1:
    resolution: {integrity: sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==}
    engines: {node: '>=6'}
    dev: true

  /error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}
    dependencies:
      is-arrayish: 0.2.1
    dev: true

  /error-stack-parser@2.1.4:
    resolution: {integrity: sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==}
    dependencies:
      stackframe: 1.3.4

  /es-abstract@1.22.1:
    resolution: {integrity: sha512-ioRRcXMO6OFyRpyzV3kE1IIBd4WG5/kltnzdxSCqoP8CMGs/Li+M1uF5o7lOkZVFjDs+NLesthnF66Pg/0q0Lw==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.0
      arraybuffer.prototype.slice: 1.0.1
      available-typed-arrays: 1.0.5
      call-bind: 1.0.2
      es-set-tostringtag: 2.0.1
      es-to-primitive: 1.2.1
      function.prototype.name: 1.1.5
      get-intrinsic: 1.2.1
      get-symbol-description: 1.0.0
      globalthis: 1.0.3
      gopd: 1.0.1
      has: 1.0.3
      has-property-descriptors: 1.0.0
      has-proto: 1.0.1
      has-symbols: 1.0.3
      internal-slot: 1.0.5
      is-array-buffer: 3.0.2
      is-callable: 1.2.7
      is-negative-zero: 2.0.2
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.2
      is-string: 1.0.7
      is-typed-array: 1.1.12
      is-weakref: 1.0.2
      object-inspect: 1.12.3
      object-keys: 1.1.1
      object.assign: 4.1.4
      regexp.prototype.flags: 1.5.0
      safe-array-concat: 1.0.0
      safe-regex-test: 1.0.0
      string.prototype.trim: 1.2.7
      string.prototype.trimend: 1.0.6
      string.prototype.trimstart: 1.0.6
      typed-array-buffer: 1.0.0
      typed-array-byte-length: 1.0.0
      typed-array-byte-offset: 1.0.0
      typed-array-length: 1.0.4
      unbox-primitive: 1.0.2
      which-typed-array: 1.1.11

  /es-abstract@1.23.9:
    resolution: {integrity: sha512-py07lI0wjxAC/DcfK1S6G7iANonniZwTISvdPzk9hzeH0IZIshbuuFxLIU96OyF89Yb9hiqWn8M/bY83KY5vzA==}
    engines: {node: '>= 0.4'}
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.3
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-regex: 1.2.1
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.1
      math-intrinsics: 1.1.0
      object-inspect: 1.13.3
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.18
    dev: true

  /es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  /es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  /es-object-atoms@1.0.0:
    resolution: {integrity: sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0

  /es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0

  /es-set-tostringtag@2.0.1:
    resolution: {integrity: sha512-g3OMbtlwY3QewlqAiMLI47KywjWZoEytKr8pf6iTC8uJq5bIAH52Z9pnQ8pVL6whrCto53JZDuUIsifGeLorTg==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.1
      has: 1.0.3
      has-tostringtag: 1.0.2

  /es-set-tostringtag@2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2
    dev: true

  /es-shim-unscopables@1.1.0:
    resolution: {integrity: sha512-d9T8ucsEhh8Bi1woXCf+TIKDIROLG5WCkxg8geBCbvk22kzwC5G2OnXVMO6FUsvQlgUUXQ2itephWDLqDzbeCw==}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2
    dev: true

  /es-to-primitive@1.2.1:
    resolution: {integrity: sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.0.4

  /es-to-primitive@1.3.0:
    resolution: {integrity: sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.0.4
    dev: true

  /escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  /escape-string-regexp@2.0.0:
    resolution: {integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==}
    engines: {node: '>=8'}
    dev: true

  /eslint-config-airbnb-base@14.2.1(eslint-plugin-import@2.31.0)(eslint@6.6.0):
    resolution: {integrity: sha512-GOrQyDtVEc1Xy20U7vsB2yAoB4nBlfH5HZJeatRXHleO+OS5Ot+MWij4Dpltw4/DyIkqUfqz1epfhVR5XWWQPA==}
    engines: {node: '>= 6'}
    peerDependencies:
      eslint: ^5.16.0 || ^6.8.0 || ^7.2.0
      eslint-plugin-import: ^2.22.1
    dependencies:
      confusing-browser-globals: 1.0.11
      eslint: 6.6.0
      eslint-plugin-import: 2.31.0(@typescript-eslint/parser@6.21.0)(eslint@6.6.0)
      object.assign: 4.1.4
      object.entries: 1.1.6
    dev: true

  /eslint-config-airbnb@18.0.0(eslint-plugin-import@2.31.0)(eslint-plugin-jsx-a11y@6.2.3)(eslint-plugin-react-hooks@3.0.0)(eslint-plugin-react@7.14.3)(eslint@6.6.0):
    resolution: {integrity: sha512-C9p7TGMuefhUWPuiObkfF9pytgAA2293bk1n0h7FdazazHNtzATTGkVv7V0gZt1/FTzdCfwWhjGSbRz/KR89og==}
    engines: {node: '>= 6'}
    peerDependencies:
      eslint: ^5.16.0 || ^6.1.0
      eslint-plugin-import: ^2.18.2
      eslint-plugin-jsx-a11y: ^6.2.3
      eslint-plugin-react: ^7.14.3
      eslint-plugin-react-hooks: ^1.7.0
    dependencies:
      eslint: 6.6.0
      eslint-config-airbnb-base: 14.2.1(eslint-plugin-import@2.31.0)(eslint@6.6.0)
      eslint-plugin-import: 2.31.0(@typescript-eslint/parser@6.21.0)(eslint@6.6.0)
      eslint-plugin-jsx-a11y: 6.2.3(eslint@6.6.0)
      eslint-plugin-react: 7.14.3(eslint@6.6.0)
      eslint-plugin-react-hooks: 3.0.0(eslint@6.6.0)
      object.assign: 4.1.4
      object.entries: 1.1.6
    dev: true

  /eslint-config-prettier@9.1.0(eslint@6.6.0):
    resolution: {integrity: sha512-NSWl5BFQWEPi1j4TjVNItzYV7dZXZ+wP6I6ZhrBGpChQhZRUaElihE9uRRkcbRnNb76UMKDF3r+WTmNcGPKsqw==}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'
    dependencies:
      eslint: 6.6.0
    dev: true

  /eslint-import-resolver-node@0.3.9:
    resolution: {integrity: sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==}
    dependencies:
      debug: 3.2.7
      is-core-module: 2.16.1
      resolve: 1.22.4
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-module-utils@2.12.0(@typescript-eslint/parser@6.21.0)(eslint-import-resolver-node@0.3.9)(eslint@6.6.0):
    resolution: {integrity: sha512-wALZ0HFoytlyh/1+4wuZ9FJCD/leWHQzzrxJ8+rebyReSLk7LApMyd3WJaLVoN+D5+WIdJyDK1c6JnE65V4Zyg==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true
    dependencies:
      '@typescript-eslint/parser': 6.21.0(eslint@6.6.0)(typescript@5.0.4)
      debug: 3.2.7
      eslint: 6.6.0
      eslint-import-resolver-node: 0.3.9
    transitivePeerDependencies:
      - supports-color
    dev: true

  /eslint-plugin-import@2.31.0(@typescript-eslint/parser@6.21.0)(eslint@6.6.0):
    resolution: {integrity: sha512-ixmkI62Rbc2/w8Vfxyh1jQRTdRTF52VxwRVHl/ykPAmqG+Nb7/kNn+byLP0LxPgI7zWA16Jt82SybJInmMia3A==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
    dependencies:
      '@rtsao/scc': 1.1.0
      '@typescript-eslint/parser': 6.21.0(eslint@6.6.0)(typescript@5.0.4)
      array-includes: 3.1.8
      array.prototype.findlastindex: 1.2.5
      array.prototype.flat: 1.3.3
      array.prototype.flatmap: 1.3.3
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 6.6.0
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.12.0(@typescript-eslint/parser@6.21.0)(eslint-import-resolver-node@0.3.9)(eslint@6.6.0)
      hasown: 2.0.2
      is-core-module: 2.16.1
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      object.groupby: 1.0.3
      object.values: 1.2.1
      semver: 6.3.1
      string.prototype.trimend: 1.0.9
      tsconfig-paths: 3.15.0
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color
    dev: true

  /eslint-plugin-jsx-a11y@6.2.3(eslint@6.6.0):
    resolution: {integrity: sha512-CawzfGt9w83tyuVekn0GDPU9ytYtxyxyFZ3aSWROmnRRFQFT2BiPJd7jvRdzNDi6oLWaS2asMeYSNMjWTV4eNg==}
    engines: {node: '>=4.0'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6
    dependencies:
      '@babel/runtime': 7.22.11
      aria-query: 3.0.0
      array-includes: 3.1.6
      ast-types-flow: 0.0.7
      axobject-query: 2.2.0
      damerau-levenshtein: 1.0.8
      emoji-regex: 7.0.3
      eslint: 6.6.0
      has: 1.0.3
      jsx-ast-utils: 2.4.1
    dev: true

  /eslint-plugin-react-hooks@3.0.0(eslint@6.6.0):
    resolution: {integrity: sha512-EjxTHxjLKIBWFgDJdhKKzLh5q+vjTFrqNZX36uIxWS4OfyXe5DawqPj3U5qeJ1ngLwatjzQnmR0Lz0J0YH3kxw==}
    engines: {node: '>=7'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0
    dependencies:
      eslint: 6.6.0
    dev: true

  /eslint-plugin-react@7.14.3(eslint@6.6.0):
    resolution: {integrity: sha512-EzdyyBWC4Uz2hPYBiEJrKCUi2Fn+BJ9B/pJQcjw5X+x/H2Nm59S4MJIvL4O5NEE0+WbnQwEBxWY03oUk+Bc3FA==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0
    dependencies:
      array-includes: 3.1.6
      doctrine: 2.1.0
      eslint: 6.6.0
      has: 1.0.3
      jsx-ast-utils: 2.4.1
      object.entries: 1.1.6
      object.fromentries: 2.0.6
      object.values: 1.1.6
      prop-types: 15.8.1
      resolve: 1.22.4
    dev: true

  /eslint-plugin-unused-imports@3.2.0(@typescript-eslint/eslint-plugin@6.21.0)(eslint@6.6.0):
    resolution: {integrity: sha512-6uXyn6xdINEpxE1MtDjxQsyXB37lfyO2yKGVVgtD7WEWQGORSOZjgrD6hBhvGv4/SO+TOlS+UnC6JppRqbuwGQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      '@typescript-eslint/eslint-plugin': 6 - 7
      eslint: '8'
    peerDependenciesMeta:
      '@typescript-eslint/eslint-plugin':
        optional: true
    dependencies:
      '@typescript-eslint/eslint-plugin': 6.21.0(@typescript-eslint/parser@6.21.0)(eslint@6.6.0)(typescript@5.0.4)
      eslint: 6.6.0
      eslint-rule-composer: 0.3.0
    dev: true

  /eslint-rule-composer@0.3.0:
    resolution: {integrity: sha512-bt+Sh8CtDmn2OajxvNO+BX7Wn4CIWMpTRm3MaiKPCQcnnlm0CS2mhui6QaoeQugs+3Kj2ESKEEGJUdVafwhiCg==}
    engines: {node: '>=4.0.0'}
    dev: true

  /eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0
    dev: true

  /eslint-utils@1.4.3:
    resolution: {integrity: sha512-fbBN5W2xdY45KulGXmLHZ3c3FHfVYmKg0IrAKGOkT/464PQsx2UeIzfz1RmEci+KLm1bBaAzZAh8+/E+XAeZ8Q==}
    engines: {node: '>=6'}
    dependencies:
      eslint-visitor-keys: 1.3.0
    dev: true

  /eslint-visitor-keys@1.3.0:
    resolution: {integrity: sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==}
    engines: {node: '>=4'}
    dev: true

  /eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  /eslint@6.6.0:
    resolution: {integrity: sha512-PpEBq7b6qY/qrOmpYQ/jTMDYfuQMELR4g4WI1M/NaSDDD/bdcMb+dj4Hgks7p41kW2caXsPsEZAEAyAgjVVC0g==}
    engines: {node: ^8.10.0 || ^10.13.0 || >=11.10.1}
    deprecated: This version is no longer supported. Please see https://eslint.org/version-support for other options.
    hasBin: true
    dependencies:
      '@babel/code-frame': 7.22.13
      ajv: 6.12.6
      chalk: 2.4.2
      cross-spawn: 6.0.5
      debug: 4.3.4
      doctrine: 3.0.0
      eslint-scope: 5.1.1
      eslint-utils: 1.4.3
      eslint-visitor-keys: 1.3.0
      espree: 6.2.1
      esquery: 1.5.0
      esutils: 2.0.3
      file-entry-cache: 5.0.1
      functional-red-black-tree: 1.0.1
      glob-parent: 5.1.2
      globals: 11.12.0
      ignore: 4.0.6
      import-fresh: 3.3.0
      imurmurhash: 0.1.4
      inquirer: 7.3.3
      is-glob: 4.0.3
      js-yaml: 3.14.1
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.3.0
      lodash: 4.17.21
      minimatch: 3.1.2
      mkdirp: 0.5.6
      natural-compare: 1.4.0
      optionator: 0.8.3
      progress: 2.0.3
      regexpp: 2.0.1
      semver: 6.3.1
      strip-ansi: 5.2.0
      strip-json-comments: 3.1.1
      table: 5.4.6
      text-table: 0.2.0
      v8-compile-cache: 2.4.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /espree@6.2.1:
    resolution: {integrity: sha512-ysCxRQY3WaXJz9tdbWOwuWr5Y/XrPTGX9Kiz3yoUXwW0VZ4w30HTkQLaGx/+ttFjF8i+ACbArnB4ce68a9m5hw==}
    engines: {node: '>=6.0.0'}
    dependencies:
      acorn: 7.4.1
      acorn-jsx: 5.3.2(acorn@7.4.1)
      eslint-visitor-keys: 1.3.0
    dev: true

  /esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /esquery@1.5.0:
    resolution: {integrity: sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==}
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: 5.3.0
    dev: true

  /estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}
    dev: true

  /estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}
    dev: true

  /esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /eventemitter3@4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}
    dev: false

  /eventemitter3@5.0.1:
    resolution: {integrity: sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==}
    dev: false

  /events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}
    dev: true

  /evp_bytestokey@1.0.3:
    resolution: {integrity: sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA==}
    dependencies:
      md5.js: 1.3.5
      safe-buffer: 5.2.1
    dev: true

  /execa@4.1.0:
    resolution: {integrity: sha512-j5W0//W7f8UxAn8hXVnwG8tLwdiUy4FJLcSupCg6maBYZDpyBvTApK7KyuI4bKj8KOh1r2YH+6ucuYtJv1bTZA==}
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 5.2.0
      human-signals: 1.1.1
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0
    dev: true

  /expect@29.6.4:
    resolution: {integrity: sha512-F2W2UyQ8XYyftHT57dtfg8Ue3X5qLgm2sSug0ivvLRH/VKNRL/pDxg/TH7zVzbQB0tu80clNFy6LU7OS/VSEKA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/expect-utils': 29.6.4
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.6.4
      jest-message-util: 29.6.3
      jest-util: 29.6.3
    dev: true

  /extend-shallow@2.0.1:
    resolution: {integrity: sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extendable: 0.1.1
    dev: false

  /extend-shallow@3.0.2:
    resolution: {integrity: sha512-BwY5b5Ql4+qZoefgMj2NUmx+tehVTH/Kf4k1ZEtOHNFcm2wSxMRo992l6X3TIgni2eZVTZ85xMOjF31fwZAj6Q==}
    engines: {node: '>=0.10.0'}
    dependencies:
      assign-symbols: 1.0.0
      is-extendable: 1.0.1
    dev: false

  /external-editor@3.1.0:
    resolution: {integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==}
    engines: {node: '>=4'}
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33
    dev: true

  /extrude-polyline@1.0.6:
    resolution: {integrity: sha512-fcKIanU/v+tcdgG0+xMbS0C2VZ0/CF3qqxSjHiWfWICh0yFBezPr3SsOhgdzwE5E82plG6p1orEsfSqgldpxVg==}
    dependencies:
      as-number: 1.0.0
      gl-vec2: 1.3.0
      polyline-miter-util: 1.0.1
    dev: false

  /falafel@2.2.5:
    resolution: {integrity: sha512-HuC1qF9iTnHDnML9YZAdCDQwT0yKl/U55K4XSUXqGAA2GLoafFgWRqdAbhWJxXaYD4pyoVxAJ8wH670jMpI9DQ==}
    engines: {node: '>=0.4.0'}
    dependencies:
      acorn: 7.4.1
      isarray: 2.0.5
    dev: false

  /fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  /fast-equals@4.0.3:
    resolution: {integrity: sha512-G3BSX9cfKttjr+2o1O22tYMLq0DPluZnYtq1rXumE1SpL/F/SLIfHx08WYQoWSIpeMYf8sRbJ8++71+v6Pnxfg==}
    dev: false

  /fast-glob@3.3.1:
    resolution: {integrity: sha512-kNFPyjhh5cKjrUltxs+wFx+ZkbRaxxmZ+X0ZU31SOsxCEtP9VPgtq2teZw1DebupL5GmDaNQ6yKMMVcM41iqDg==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5
    dev: true

  /fast-glob@3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8
    dev: true

  /fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  /fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}
    dev: true

  /fast-loops@1.1.3:
    resolution: {integrity: sha512-8EZzEP0eKkEEVX+drtd9mtuQ+/QrlfW/5MlwcwK5Nds6EkZ/tRzEexkzUY2mIssnAyVLT+TKHuRXmFNNXYUd6g==}
    dev: false

  /fast-shallow-equal@1.0.0:
    resolution: {integrity: sha512-HPtaa38cPgWvaCFmRNhlc6NG7pv6NUHqjPgVAkWGoB9mQMwYB27/K0CvOM5Czy+qpT3e8XJ6Q4aPAnzpNpzNaw==}
    dev: false

  /fastest-stable-stringify@2.0.2:
    resolution: {integrity: sha512-bijHueCGd0LqqNK9b5oCMHc0MluJAx0cwqASgbWMvkO01lCYgIhacVRLcaDz3QnyYIRNJRDwMb41VuT6pHJ91Q==}
    dev: false

  /fastq@1.15.0:
    resolution: {integrity: sha512-wBrocU2LCXXa+lWBt8RoIRD89Fi8OdABODa/kEnyeyjS5aZO5/GNvI5sEINADqP/h8M29UHTHUb53sUu5Ihqdw==}
    dependencies:
      reusify: 1.0.4
    dev: true

  /fbjs@0.8.18:
    resolution: {integrity: sha512-EQaWFK+fEPSoibjNy8IxUtaFOMXcWsY0JaVrQoZR9zC8N2Ygf9iDITPWjUTVIax95b6I742JFLqASHfsag/vKA==}
    dependencies:
      core-js: 1.2.7
      isomorphic-fetch: 2.2.1
      loose-envify: 1.4.0
      object-assign: 4.1.1
      promise: 7.3.1
      setimmediate: 1.0.5
      ua-parser-js: 0.7.35
    dev: false

  /fecha@4.2.3:
    resolution: {integrity: sha512-OP2IUU6HeYKJi3i0z4A19kHMQoLVs4Hc+DPqqxI2h/DPZHTm/vjsfC6P0b4jCMy14XizLBqvndQ+UilD7707Jw==}
    dev: false

  /fflate@0.8.2:
    resolution: {integrity: sha512-cPJU47OaAoCbg0pBvzsgpTPhmhqI5eJjh/JIu8tPj5q+T7iLvW/JAYUqmE7KOB4R1ZyEhzBaIQpQpardBF5z8A==}
    dev: false

  /figures@3.2.0:
    resolution: {integrity: sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==}
    engines: {node: '>=8'}
    dependencies:
      escape-string-regexp: 1.0.5
    dev: true

  /file-entry-cache@5.0.1:
    resolution: {integrity: sha512-bCg29ictuBaKUwwArK4ouCaqDgLZcysCFLmM/Yn/FDoqndh/9vNuQfXRDvTuXKLxfD/JtZQGKFT8MGcJBK644g==}
    engines: {node: '>=4'}
    dependencies:
      flat-cache: 2.0.1
    dev: true

  /file-source@0.6.1:
    resolution: {integrity: sha512-1R1KneL7eTXmXfKxC10V/9NeGOdbsAXJ+lQ//fvvcHUgtaZcZDWNJNblxAoVOyV1cj45pOtUrR3vZTBwqcW8XA==}
    dependencies:
      stream-source: 0.3.5
    dev: false

  /fill-range@7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1

  /fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: 5.0.1
    dev: true

  /find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0
    dev: true

  /find-versions@4.0.0:
    resolution: {integrity: sha512-wgpWy002tA+wgmO27buH/9KzyEOQnKsG/R0yrcjPT9BOFm0zRBVQbZ95nRGXWMywS8YR5knRbpohio0bcJABxQ==}
    engines: {node: '>=10'}
    dependencies:
      semver-regex: 3.1.4
    dev: true

  /flat-cache@2.0.1:
    resolution: {integrity: sha512-LoQe6yDuUMDzQAEH8sgmh4Md6oZnc/7PjtwjNFSzveXqSHt6ka9fPBuso7IGf9Rz4uqnSnWiFH2B/zj24a5ReA==}
    engines: {node: '>=4'}
    dependencies:
      flatted: 2.0.2
      rimraf: 2.6.3
      write: 1.0.3
    dev: true

  /flatted@2.0.2:
    resolution: {integrity: sha512-r5wGx7YeOwNWNlCA0wQ86zKyDLMQr+/RB8xy74M4hTphfmjlijTSSXGuH8rnvKZnfT9i+75zmd8jcKdMR4O6jA==}
    dev: true

  /fmin@0.0.2:
    resolution: {integrity: sha512-sSi6DzInhl9d8yqssDfGZejChO8d2bAGIpysPsvYsxFe898z89XhCZg6CPNV3nhUhFefeC/AXZK2bAJxlBjN6A==}
    dependencies:
      contour_plot: 0.0.1
      json2module: 0.0.3
      rollup: 0.25.8
      tape: 4.16.2
      uglify-js: 2.8.29
    dev: false

  /follow-redirects@1.5.10:
    resolution: {integrity: sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==}
    engines: {node: '>=4.0'}
    dependencies:
      debug: 3.1.0
    transitivePeerDependencies:
      - supports-color
    dev: false

  /for-each@0.3.3:
    resolution: {integrity: sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==}
    dependencies:
      is-callable: 1.2.7

  /foreground-child@3.3.1:
    resolution: {integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==}
    engines: {node: '>=14'}
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0
    dev: true

  /fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  /fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /function-bind@1.1.1:
    resolution: {integrity: sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A==}

  /function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  /function.prototype.name@1.1.5:
    resolution: {integrity: sha512-uN7m/BzVKQnCUF/iW8jYea67v++2u7m5UgENbHRtdDVclOUP+FMPlCNdmk0h/ysGyo2tavMJEDqJAkJdRa1vMA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.22.1
      functions-have-names: 1.2.3

  /function.prototype.name@1.1.8:
    resolution: {integrity: sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7
    dev: true

  /functional-red-black-tree@1.0.1:
    resolution: {integrity: sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==}
    dev: true

  /functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}

  /geobuf@3.0.2:
    resolution: {integrity: sha512-ASgKwEAQQRnyNFHNvpd5uAwstbVYmiTW0Caw3fBb509tNTqXyAAPMyFs5NNihsLZhLxU1j/kjFhkhLWA9djuVg==}
    hasBin: true
    dependencies:
      concat-stream: 2.0.0
      pbf: 3.2.1
      shapefile: 0.6.6
    dev: false

  /geojson-equality@0.1.6:
    resolution: {integrity: sha512-TqG8YbqizP3EfwP5Uw4aLu6pKkg6JQK9uq/XZ1lXQntvTHD1BBKJWhNpJ2M0ax6TuWMP3oyx6Oq7FCIfznrgpQ==}
    dependencies:
      deep-equal: 1.1.1
    dev: false

  /geojson-rbush@2.1.0:
    resolution: {integrity: sha512-9HvLGhmAJBYkYYDdPlCrlfkKGwNW3PapiS0xPekdJLobkZE4rjtduKJXsO7+kUr97SsUlz4VtMcPuSIbjjJaQg==}
    dependencies:
      '@turf/helpers': 6.5.0
      '@turf/meta': 6.5.0
      rbush: 3.0.1
    dev: false

  /geojson-vt@3.2.1:
    resolution: {integrity: sha512-EvGQQi/zPrDA6zr6BnJD/YhwAkBP8nnJ9emh3EnHQKVMfg/MRVtPbMYdgVy/IaEmn4UfagD2a6fafPDL5hbtwg==}
    dev: false

  /get-closest@0.0.4:
    resolution: {integrity: sha512-oMgZYUtnPMZB6XieXiUADpRIc5kfD+RPfpiYe9aIlEYGIcOx2mTGgKmUkctlLof/ANleypqOJRhQypbrh33DkA==}
    dev: false

  /get-intrinsic@1.2.1:
    resolution: {integrity: sha512-2DcsyfABl+gVHEfCOaTrWgyt+tb6MSEGmKq+kI5HwLbIYgjgmMcV8KQ41uaKz1xxUcn9tJtgFbQUEVcEbd0FYw==}
    dependencies:
      function-bind: 1.1.1
      has: 1.0.3
      has-proto: 1.0.1
      has-symbols: 1.0.3

  /get-intrinsic@1.2.6:
    resolution: {integrity: sha512-qxsEs+9A+u85HhllWJJFicJfPDhRmjzoYdl64aMWW9yRIJmSyxdn8IEkuIM530/7T+lv0TIHd8L6Q/ra0tEoeA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.1
      dunder-proto: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      function-bind: 1.1.2
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  /get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0
    dev: true

  /get-own-enumerable-property-symbols@3.0.2:
    resolution: {integrity: sha512-I0UBV/XOz1XkIJHEUDMZAbzCThU/H8DxmSfmdGcKPnVhu2VfFqr34jr9777IyaTYvxjedWhqVIilEDsCdP5G6g==}
    dev: true

  /get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  /get-stream@5.2.0:
    resolution: {integrity: sha512-nBF+F1rAZVCu/p7rjzgA+Yb4lfYXrpl7a6VmJrU8wF9I1CKvP/QwPNZHnOlwbTkY6dvtFIzFMSyQXbLoTQPRpA==}
    engines: {node: '>=8'}
    dependencies:
      pump: 3.0.0
    dev: true

  /get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}
    dev: false

  /get-symbol-description@1.0.0:
    resolution: {integrity: sha512-2EmdH1YvIQiZpltCNgkuiUnyukzxM/R6NDJX31Ke3BG1Nq5b0S2PhX59UKi9vZpPDQVdqn+1IcaAwnzTT5vCjw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      get-intrinsic: 1.2.1

  /get-symbol-description@1.1.0:
    resolution: {integrity: sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
    dev: true

  /get-value@2.0.6:
    resolution: {integrity: sha512-Ln0UQDlxH1BapMu3GPtf7CuYNwRZf2gwCuPqbyG6pB8WfmFpzqcy4xtAaAMUhnNqjMKTiCPZG2oMT3YSx8U2NA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /gl-matrix@3.4.3:
    resolution: {integrity: sha512-wcCp8vu8FT22BnvKVPjXa/ICBWRq/zjFfdofZy1WSpQZpphblv12/bOQLBC1rMM7SGOFS9ltVmKOHil5+Ml7gA==}
    dev: false

  /gl-vec2@1.3.0:
    resolution: {integrity: sha512-YiqaAuNsheWmUV0Sa8k94kBB0D6RWjwZztyO+trEYS8KzJ6OQB/4686gdrf59wld4hHFIvaxynO3nRxpk1Ij/A==}
    dev: false

  /glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: 4.0.3
    dev: true

  /glob@10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==}
    hasBin: true
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1
    dev: true

  /glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  /global-prefix@3.0.0:
    resolution: {integrity: sha512-awConJSVCHVGND6x3tmMaKcQvwXLhjdkmomy2W+Goaui8YPgYgXJZewhg3fWC+DlfqqQuWg8AwqjGTD2nAPVWg==}
    engines: {node: '>=6'}
    dependencies:
      ini: 1.3.8
      kind-of: 6.0.3
      which: 1.3.1
    dev: false

  /globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  /globalthis@1.0.3:
    resolution: {integrity: sha512-sFdI5LyBiNTHjRd7cGPWapiHWMOXKyuBNX/cWJ3NfzrZQVa8GI/8cofCl74AOVqq9W5kNmguTIzJ/1s2gyI9wA==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-properties: 1.2.1

  /globalthis@1.0.4:
    resolution: {integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0
    dev: true

  /globby@11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.1
      ignore: 5.2.4
      merge2: 1.4.1
      slash: 3.0.0
    dev: true

  /gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}
    dependencies:
      get-intrinsic: 1.2.1

  /gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  /graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}
    dev: true

  /graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}
    dev: true

  /graphlib@2.1.8:
    resolution: {integrity: sha512-jcLLfkpoVGmH7/InMC/1hIvOPSUh38oJtGhvrOFGzioE1DZ+0YW16RgmOJhHiuWTvGiJQ9Z1Ik43JvkRPRvE+A==}
    dependencies:
      lodash: 4.17.21
    dev: false

  /grid-index@1.1.0:
    resolution: {integrity: sha512-HZRwumpOGUrHyxO5bqKZL0B0GlUpwtCAzZ42sgxUPniu33R1LSFH5yrIcBCHjkctCAh3mtWKcKd9J4vDDdeVHA==}
    dev: false

  /hammerjs@2.0.8:
    resolution: {integrity: sha512-tSQXBXS/MWQOn/RKckawJ61vvsDpCom87JgxiYdGwHdOa0ht0vzUWDlfioofFCRU0L+6NGDt6XzbgoJvZkMeRQ==}
    engines: {node: '>=0.8.0'}
    dev: false

  /has-ansi@2.0.0:
    resolution: {integrity: sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: false

  /has-bigints@1.0.2:
    resolution: {integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==}

  /has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}
    dev: true

  /has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}
    dev: true

  /has-property-descriptors@1.0.0:
    resolution: {integrity: sha512-62DVLZGoiEBDHQyqG4w9xCuZ7eJEwNmJRWw2VY84Oedb7WFcA27fiEVe8oUQx9hAUJ4ekurquucTGwsyO1XGdQ==}
    dependencies:
      get-intrinsic: 1.2.1

  /has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}
    dependencies:
      es-define-property: 1.0.1

  /has-proto@1.0.1:
    resolution: {integrity: sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==}
    engines: {node: '>= 0.4'}

  /has-proto@1.2.0:
    resolution: {integrity: sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: 1.0.1
    dev: true

  /has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}

  /has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  /has-tostringtag@1.0.0:
    resolution: {integrity: sha512-kFjcSNhnlGV1kyoGk7OXKSawH5JOb/LzUc5w9B02hOTO0dfFRjbHQKvg1d6cf3HbeUmtU9VbbV3qzZ2Teh97WQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.1.0

  /has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.1.0

  /has@1.0.3:
    resolution: {integrity: sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==}
    engines: {node: '>= 0.4.0'}
    dependencies:
      function-bind: 1.1.1

  /hash-base@3.0.5:
    resolution: {integrity: sha512-vXm0l45VbcHEVlTCzs8M+s0VeYsB2lnlAaThoLKGXr3bE/VWDOelNUnycUPEhKEaXARL2TEFjBOyUiM6+55KBg==}
    engines: {node: '>= 0.10'}
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: true

  /hash.js@1.1.7:
    resolution: {integrity: sha512-taOaskGt4z4SOANNseOviYDvjEJinIkRgmp7LbKP2YTTmVxWBl87s/uzK9r+44BclBSp2X7K1hqeNfz9JbBeXA==}
    dependencies:
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
    dev: true

  /hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: 1.1.2

  /history@4.10.1:
    resolution: {integrity: sha512-36nwAD620w12kuzPAsyINPWJqlNbij+hpK1k9XRloDtym8mxzGYl2c17LnV6IAGB2Dmg4tEa7G7DlawS0+qjew==}
    dependencies:
      '@babel/runtime': 7.25.6
      loose-envify: 1.4.0
      resolve-pathname: 3.0.0
      tiny-invariant: 1.3.1
      tiny-warning: 1.0.3
      value-equal: 1.0.1
    dev: false

  /hmac-drbg@1.0.1:
    resolution: {integrity: sha512-Tti3gMqLdZfhOQY1Mzf/AanLiqh1WTiJgEj26ZuYQ9fbkLomzGchCws4FyrSd4VkpBfiNhaE1On+lOz894jvXg==}
    dependencies:
      hash.js: 1.1.7
      minimalistic-assert: 1.0.1
      minimalistic-crypto-utils: 1.0.1
    dev: true

  /hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}
    dependencies:
      react-is: 16.13.1

  /html-entities@2.5.2:
    resolution: {integrity: sha512-K//PSRMQk4FZ78Kyau+mZurHn3FH0Vwr+H36eE0rPbeYkRRi9YxceYPhuN60UwWorxyKHhqoAJl2OFKa4BVtaA==}
    dev: true

  /html-parse-stringify@3.0.1:
    resolution: {integrity: sha512-KknJ50kTInJ7qIScF3jeaFRpMpE8/lfiTdzf/twXyPBLAGrLRTmkz3AdTnKeh40X8k9L2fdYwEp/42WGXIRGcg==}
    dependencies:
      void-elements: 3.1.0
    dev: false

  /html2canvas@1.4.1:
    resolution: {integrity: sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      css-line-break: 2.1.0
      text-segmentation: 1.0.3
    dev: false

  /https-browserify@1.0.0:
    resolution: {integrity: sha512-J+FkSdyD+0mA0N+81tMotaRMfSL9SGi+xpD3T6YApKsc3bGSXJlfXri3VyFOeYkfLRQisDk1W+jIFFKBeUBbBg==}
    dev: true

  /human-signals@1.1.1:
    resolution: {integrity: sha512-SEQu7vl8KjNL2eoGBLF3+wAjpsNfA9XMlXAYj/3EdaNfAlxKthD1xjEQfGOUhllCGGJVNY34bRr6lPINhNjyZw==}
    engines: {node: '>=8.12.0'}
    dev: true

  /husky@4.3.8:
    resolution: {integrity: sha512-LCqqsB0PzJQ/AlCgfrfzRe3e3+NvmefAdKQhRYpxS4u6clblBoDdzzvHi8fmxKRzvMxPY/1WZWzomPZww0Anow==}
    engines: {node: '>=10'}
    hasBin: true
    requiresBuild: true
    dependencies:
      chalk: 4.1.2
      ci-info: 2.0.0
      compare-versions: 3.6.0
      cosmiconfig: 7.1.0
      find-versions: 4.0.0
      opencollective-postinstall: 2.0.3
      pkg-dir: 5.0.0
      please-upgrade-node: 3.2.0
      slash: 3.0.0
      which-pm-runs: 1.1.0
    dev: true

  /hyphenate-style-name@1.0.4:
    resolution: {integrity: sha512-ygGZLjmXfPHj+ZWh6LwbC37l43MhfztxetbFCoYTM2VjkIUpeHgSNn7QIyVFj7YQ1Wl9Cbw5sholVJPzWvC2MQ==}
    dev: false

  /i18next@23.16.8:
    resolution: {integrity: sha512-06r/TitrM88Mg5FdUXAKL96dJMzgqLE5dv3ryBAra4KCwD9mJ4ndOTS95ZuymIGoE+2hzfdaMak2X11/es7ZWg==}
    dependencies:
      '@babel/runtime': 7.25.6
    dev: false

  /iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2

  /iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: 2.1.2
    dev: false

  /ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  /ignore@4.0.6:
    resolution: {integrity: sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==}
    engines: {node: '>= 4'}
    dev: true

  /ignore@5.2.4:
    resolution: {integrity: sha512-MAb38BcSbH0eHNBxn7ql2NH/kX33OkB3lZ1BNdh7ENeRChHTYsTvWrMubiIAMNS2llXEEgZ1MUOBtXChP3kaFQ==}
    engines: {node: '>= 4'}
    dev: true

  /immer@9.0.21:
    resolution: {integrity: sha512-bc4NBHqOqSfRW7POMkHd51LvClaeMXpm8dx0e8oE2GORbq5aRK7Bxl4FyzVLdGtLmvLKL7BTDBG5ACQm4HWjTA==}
    dev: false

  /immutability-helper@3.1.1:
    resolution: {integrity: sha512-Q0QaXjPjwIju/28TsugCHNEASwoCcJSyJV3uO1sOIQGI0jKgm9f41Lvz0DZj3n46cNCyAZTsEYoY4C2bVRUzyQ==}
    dev: false

  /immutable@3.7.6:
    resolution: {integrity: sha512-AizQPcaofEtO11RZhPPHBOJRdo/20MKQF9mBLnVkBoyHi1/zXK8fzVdnEpSV9gxqtnh6Qomfp3F0xT5qP/vThw==}
    engines: {node: '>=0.8.0'}
    dev: false

  /immutable@5.0.3:
    resolution: {integrity: sha512-P8IdPQHq3lA1xVeBRi5VPqUm5HDgKnx0Ru51wZz5mjxHr5n3RWhjIpOFU7ybkUxfB+5IToy+OLaHYDBIWsv+uw==}

  /import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0
    dev: true

  /imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}
    dev: true

  /indent-string@4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}
    dev: true

  /inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  /inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  /ini@1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}
    dev: false

  /inline-style-prefixer@6.0.4:
    resolution: {integrity: sha512-FwXmZC2zbeeS7NzGjJ6pAiqRhXR0ugUShSNb6GApMl6da0/XGc4MOJsoWAywia52EEWbXNSy0pzkwz/+Y+swSg==}
    dependencies:
      css-in-js-utils: 3.1.0
      fast-loops: 1.1.3
    dev: false

  /inquirer@7.3.3:
    resolution: {integrity: sha512-JG3eIAj5V9CwcGvuOmoo6LB9kbAYT8HXffUl6memuszlwDC/qvFAJw49XJ5NROSFNPxp3iQg1GqkFhaY/CR0IA==}
    engines: {node: '>=8.0.0'}
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      run-async: 2.4.1
      rxjs: 6.6.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8
    dev: true

  /insert-css@2.0.0:
    resolution: {integrity: sha512-xGq5ISgcUP5cvGkS2MMFLtPDBtrtQPSFfC6gA6U8wHKqfjTIMZLZNxOItQnoSjdOzlXOLU/yD32RKC4SvjNbtA==}
    dev: false

  /internal-slot@1.0.5:
    resolution: {integrity: sha512-Y+R5hJrzs52QCG2laLn4udYVnxsfny9CpOhNhUvk/SSSVyF6T27FzRbF0sroPidSu3X8oEAkOn2K804mjpt6UQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.2.1
      has: 1.0.3
      side-channel: 1.0.4

  /internal-slot@1.1.0:
    resolution: {integrity: sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0
    dev: true

  /internmap@1.0.1:
    resolution: {integrity: sha512-lDB5YccMydFBtasVtxnZ3MRBHuaoE8GKsppq+EchKL2U4nK/DmEpPHNH8MZe5HkMtpSiTSOZwfN0tzYjO/lJEw==}
    dev: false

  /intersection-observer@0.12.2:
    resolution: {integrity: sha512-7m1vEcPCxXYI8HqnL8CKI6siDyD+eIWSwgB3DZA+ZTogxk9I4CDnj4wilt9x/+/QbHI4YG5YZNmC6458/e9Ktg==}
    dev: false

  /intersection-observer@0.7.0:
    resolution: {integrity: sha512-Id0Fij0HsB/vKWGeBe9PxeY45ttRiBmhFyyt/geBdDHBYNctMRTE3dC1U3ujzz3lap+hVXlEcVaB56kZP/eEUg==}
    dev: false

  /inversify-inject-decorators@3.1.0:
    resolution: {integrity: sha512-/seBlVp5bXrLQS3DpKEmlgeZL6C7Tf/QITd+IMQrbBBGuCbxb7k3hRAWu9XSreNpFzLgSboz3sClLSEmGwHphw==}
    dev: false

  /inversify@5.1.1:
    resolution: {integrity: sha512-j8grHGDzv1v+8T1sAQ+3boTCntFPfvxLCkNcxB1J8qA0lUN+fAlSyYd+RXKvaPRL4AGyPxViutBEJHNXOyUdFQ==}
    dev: false

  /is-any-array@2.0.1:
    resolution: {integrity: sha512-UtilS7hLRu++wb/WBAw9bNuP1Eg04Ivn1vERJck8zJthEvXCBEBpGR/33u/xLKWEQf95803oalHrVDptcAvFdQ==}
    dev: false

  /is-arguments@1.1.1:
    resolution: {integrity: sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      has-tostringtag: 1.0.2

  /is-array-buffer@3.0.2:
    resolution: {integrity: sha512-y+FyyR/w8vfIRq4eQcM1EYgSTnmHXPqaF+IgzgraytCFq5Xh8lllDVmAZolPJiZttZLeFSINPYMaEJ7/vWUa1w==}
    dependencies:
      call-bind: 1.0.8
      get-intrinsic: 1.2.1
      is-typed-array: 1.1.12

  /is-array-buffer@3.0.5:
    resolution: {integrity: sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      get-intrinsic: 1.3.0
    dev: true

  /is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}
    dev: true

  /is-arrayish@0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}
    dev: false

  /is-async-function@2.1.1:
    resolution: {integrity: sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.3
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0
    dev: true

  /is-bigint@1.0.4:
    resolution: {integrity: sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==}
    dependencies:
      has-bigints: 1.0.2

  /is-bigint@1.1.0:
    resolution: {integrity: sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-bigints: 1.0.2
    dev: true

  /is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: 2.2.0
    dev: true

  /is-boolean-object@1.1.2:
    resolution: {integrity: sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      has-tostringtag: 1.0.2

  /is-boolean-object@1.2.2:
    resolution: {integrity: sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2
    dev: true

  /is-buffer@1.1.6:
    resolution: {integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==}
    dev: false

  /is-callable@1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}

  /is-core-module@2.13.0:
    resolution: {integrity: sha512-Z7dk6Qo8pOCp3l4tsX2C5ZVas4V+UxwQodwZhLopL91TX8UyyHEXafPcyoeeWuLrwzHcr3igO78wNLwHJHsMCQ==}
    dependencies:
      has: 1.0.3

  /is-core-module@2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==}
    engines: {node: '>= 0.4'}
    dependencies:
      hasown: 2.0.2
    dev: true

  /is-data-view@1.0.2:
    resolution: {integrity: sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      get-intrinsic: 1.3.0
      is-typed-array: 1.1.15
    dev: true

  /is-date-object@1.1.0:
    resolution: {integrity: sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2

  /is-extendable@0.1.1:
    resolution: {integrity: sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-extendable@1.0.1:
    resolution: {integrity: sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-plain-object: 2.0.4
    dev: false

  /is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  /is-finalizationregistry@1.1.1:
    resolution: {integrity: sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
    dev: true

  /is-fullwidth-code-point@2.0.0:
    resolution: {integrity: sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==}
    engines: {node: '>=4'}
    dev: true

  /is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}
    dev: true

  /is-generator-function@1.0.10:
    resolution: {integrity: sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.2
    dev: true

  /is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: 2.1.1

  /is-map@2.0.3:
    resolution: {integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-nan@1.3.2:
    resolution: {integrity: sha512-E+zBKpQ2t6MEo1VsonYmluk9NxGrbzpeeLC2xIViuO2EjU2xsXsBPwTr3Ykv9l08UYEVEdWeRZNouaZqF6RN0w==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
    dev: true

  /is-negative-zero@2.0.2:
    resolution: {integrity: sha512-dqJvarLawXsFbNDeJW7zAz8ItJ9cd28YufuuFzh0G8pNHjJMnY08Dv7sYX2uF5UpQOwieAeOExEYAWWfu7ZZUA==}
    engines: {node: '>= 0.4'}

  /is-number-object@1.0.7:
    resolution: {integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.2

  /is-number-object@1.1.1:
    resolution: {integrity: sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2
    dev: true

  /is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  /is-obj@1.0.1:
    resolution: {integrity: sha512-l4RyHgRqGN4Y3+9JHVrNqO+tN0rV5My76uW5/nuO4K1b6vw5G8d/cmFjP9tRfEsdhZNt0IFdZuK/c2Vr4Nb+Qg==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-plain-object@2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: 3.0.1
    dev: false

  /is-regex@1.1.4:
    resolution: {integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      has-tostringtag: 1.0.2

  /is-regex@1.2.1:
    resolution: {integrity: sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  /is-regexp@1.0.0:
    resolution: {integrity: sha512-7zjFAPO4/gwyQAAgRRmqeEeyIICSdmCqa3tsVHMdBzaXXRiqopZL4Cyghg/XulGWrtABTpbnYYzzIRffLkP4oA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /is-set@2.0.3:
    resolution: {integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-shared-array-buffer@1.0.2:
    resolution: {integrity: sha512-sqN2UDu1/0y6uvXyStCOzyhAjCSlHceFoMKJW8W9EU9cvic/QdsZ0kEU93HEy3IUEFZIiH/3w+AH/UQbPHNdhA==}
    dependencies:
      call-bind: 1.0.8

  /is-shared-array-buffer@1.0.4:
    resolution: {integrity: sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
    dev: true

  /is-stream@1.1.0:
    resolution: {integrity: sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}
    dev: true

  /is-string@1.0.7:
    resolution: {integrity: sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-tostringtag: 1.0.0

  /is-string@1.1.1:
    resolution: {integrity: sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2
    dev: true

  /is-symbol@1.0.4:
    resolution: {integrity: sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==}
    engines: {node: '>= 0.4'}
    dependencies:
      has-symbols: 1.1.0

  /is-symbol@1.1.1:
    resolution: {integrity: sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0
    dev: true

  /is-typed-array@1.1.12:
    resolution: {integrity: sha512-Z14TF2JNG8Lss5/HMqt0//T9JeHXttXy5pH/DBU4vi98ozO2btxzq9MwYDZYnKwU8nRsz/+GVFVRDq3DkVuSPg==}
    engines: {node: '>= 0.4'}
    dependencies:
      which-typed-array: 1.1.11

  /is-typed-array@1.1.15:
    resolution: {integrity: sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      which-typed-array: 1.1.18
    dev: true

  /is-unicode-supported@0.1.0:
    resolution: {integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==}
    engines: {node: '>=10'}
    dev: true

  /is-weakmap@2.0.2:
    resolution: {integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==}
    engines: {node: '>= 0.4'}
    dev: true

  /is-weakref@1.0.2:
    resolution: {integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==}
    dependencies:
      call-bind: 1.0.8

  /is-weakref@1.1.1:
    resolution: {integrity: sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
    dev: true

  /is-weakset@2.0.4:
    resolution: {integrity: sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      get-intrinsic: 1.3.0
    dev: true

  /isarray@0.0.1:
    resolution: {integrity: sha512-D2S+3GLxWH+uhrNEcoh/fnmYeP8E8/zHl644d/jdA0g2uyXvy3sb0qxotE+ne0LtccHknQzWwZEzhak7oJ0COQ==}
    dev: false

  /isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}
    dev: true

  /isarray@2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}

  /isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  /isobject@3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /isomorphic-fetch@2.2.1:
    resolution: {integrity: sha512-9c4TNAKYXM5PRyVcwUZrF3W09nQ+sO7+jydgs4ZGW9dhsLG2VOlISJABombdQqQRXCwuYG3sYV/puGf5rp0qmA==}
    dependencies:
      node-fetch: 1.7.3
      whatwg-fetch: 3.6.17
    dev: false

  /isomorphic-rslog@0.0.6:
    resolution: {integrity: sha512-HM0q6XqQ93psDlqvuViNs/Ea3hAyGDkIdVAHlrEocjjAwGrs1fZ+EdQjS9eUPacnYB7Y8SoDdSY3H8p3ce205A==}
    engines: {node: '>=14.17.6'}
    dev: true

  /isomorphic-timers-promises@1.0.1:
    resolution: {integrity: sha512-u4sej9B1LPSxTGKB/HiuzvEQnXH0ECYkSVQU39koSwmFAxhlEAFl9RdTvLv4TOTQUgBS5O3O5fwUxk6byBZ+IQ==}
    engines: {node: '>=10'}
    dev: true

  /jackspeak@3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==}
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0
    dev: true

  /jest-diff@29.6.4:
    resolution: {integrity: sha512-9F48UxR9e4XOEZvoUXEHSWY4qC4zERJaOfrbBg9JpbJOO43R1vN76REt/aMGZoY6GD5g84nnJiBIVlscegefpw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      chalk: 4.1.2
      diff-sequences: 29.6.3
      jest-get-type: 29.6.3
      pretty-format: 29.6.3
    dev: true

  /jest-get-type@29.6.3:
    resolution: {integrity: sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dev: true

  /jest-matcher-utils@29.6.4:
    resolution: {integrity: sha512-KSzwyzGvK4HcfnserYqJHYi7sZVqdREJ9DMPAKVbS98JsIAvumihaNUbjrWw0St7p9IY7A9UskCW5MYlGmBQFQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      chalk: 4.1.2
      jest-diff: 29.6.4
      jest-get-type: 29.6.3
      pretty-format: 29.6.3
    dev: true

  /jest-message-util@29.6.3:
    resolution: {integrity: sha512-FtzaEEHzjDpQp51HX4UMkPZjy46ati4T5pEMyM6Ik48ztu4T9LQplZ6OsimHx7EuM9dfEh5HJa6D3trEftu3dA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@babel/code-frame': 7.22.13
      '@jest/types': 29.6.3
      '@types/stack-utils': 2.0.1
      chalk: 4.1.2
      graceful-fs: 4.2.11
      micromatch: 4.0.5
      pretty-format: 29.6.3
      slash: 3.0.0
      stack-utils: 2.0.6
    dev: true

  /jest-util@29.6.3:
    resolution: {integrity: sha512-QUjna/xSy4B32fzcKTSz1w7YYzgiHrjjJjevdRf61HYk998R5vVMMNmrHESYZVDS5DSWs+1srPLPKxXPkeSDOA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 20.17.19
      chalk: 4.1.2
      ci-info: 3.8.0
      graceful-fs: 4.2.11
      picomatch: 2.3.1
    dev: true

  /jiti@1.21.6:
    resolution: {integrity: sha512-2yTgeWTWzMWkHu6Jp9NKgePDaYHbntiwvYuuJLbbN9vl7DC9DvXKOB2BC3ZZ92D3cvV/aflH0osDfwpHepQ53w==}
    hasBin: true
    dev: true

  /jiti@2.4.2:
    resolution: {integrity: sha512-rg9zJN+G4n2nfJl5MW3BMygZX56zKPNVEYYqq7adpmMh4Jn2QNEwhvQlFy6jPVdcod7txZtKHWnyZiA3a0zP7A==}
    hasBin: true
    dev: true

  /jquery-mousewheel@3.1.13:
    resolution: {integrity: sha512-GXhSjfOPyDemM005YCEHvzrEALhKDIswtxSHSR2e4K/suHVJKJxxRCGz3skPjNxjJjQa9AVSGGlYjv1M3VLIPg==}
    dev: false

  /jquery@3.7.1:
    resolution: {integrity: sha512-m4avr8yL8kmFN8psrbFFFmB/If14iN5o9nw/NgnnM+kybDJpRsAynV2BsfpTYrTRysYUdADVD7CkUUizgkpLfg==}
    dev: false

  /js-cookie@2.2.1:
    resolution: {integrity: sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ==}
    dev: false

  /js-cookie@3.0.5:
    resolution: {integrity: sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==}
    engines: {node: '>=14'}
    dev: false

  /js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  /js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1
    dev: true

  /js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true
    dependencies:
      argparse: 2.0.1
    dev: true

  /jsencrypt@3.3.2:
    resolution: {integrity: sha512-arQR1R1ESGdAxY7ZheWr12wCaF2yF47v5qpB76TtV64H1pyGudk9Hvw8Y9tb/FiTIaaTRUyaSnm5T/Y53Ghm/A==}
    dev: false

  /jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  /jsesc@3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==}
    engines: {node: '>=6'}
    hasBin: true
    dev: false

  /json-beautify@1.1.1:
    resolution: {integrity: sha512-17j+Hk2lado0xqKtUcyAjK0AtoHnPSIgktWRsEXgdFQFG9UnaGw6CHa0J7xsvulxRpFl6CrkDFHght1p5ZJc4A==}
    hasBin: true
    dev: false

  /json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}
    dev: true

  /json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  /json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}
    dev: true

  /json-stringify-pretty-compact@3.0.0:
    resolution: {integrity: sha512-Rc2suX5meI0S3bfdZuA7JMFBGkJ875ApfVyq2WHELjBiiG22My/l7/8zPpH/CfFVQHuVLd8NLR0nv6vi0BYYKA==}
    dev: false

  /json2module@0.0.3:
    resolution: {integrity: sha512-qYGxqrRrt4GbB8IEOy1jJGypkNsjWoIMlZt4bAsmUScCA507Hbc2p1JOhBzqn45u3PWafUgH2OnzyNU7udO/GA==}
    hasBin: true
    dependencies:
      rw: 1.3.3
    dev: false

  /json2mq@0.2.0:
    resolution: {integrity: sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA==}
    dependencies:
      string-convert: 0.2.1
    dev: false

  /json5@1.0.2:
    resolution: {integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==}
    hasBin: true
    dependencies:
      minimist: 1.2.8
    dev: true

  /json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  /jsx-ast-utils@2.4.1:
    resolution: {integrity: sha512-z1xSldJ6imESSzOjd3NNkieVJKRlKYSOtMG8SFyCj2FIrvSaSuli/WjpBkEzCBoR9bYYYFgqJw61Xhu7Lcgk+w==}
    engines: {node: '>=4.0'}
    dependencies:
      array-includes: 3.1.6
      object.assign: 4.1.4
    dev: true

  /kdbush@3.0.0:
    resolution: {integrity: sha512-hRkd6/XW4HTsA9vjVpY9tuXJYLSlelnkTmVFu4M9/7MIYQtFcHpbugAU7UbOfjOiVSVYl2fqgBuJ32JUmRo5Ew==}
    dev: false

  /kdbush@4.0.2:
    resolution: {integrity: sha512-WbCVYJ27Sz8zi9Q7Q0xHC+05iwkm3Znipc2XTlrnJbsHMYktW4hPhXUE8Ys1engBrvffoSCqbil1JQAa7clRpA==}
    dev: false

  /kind-of@3.2.2:
    resolution: {integrity: sha512-NOW9QQXMoZGg/oqnVNoNTTIFEIid1627WCffUBJEdMxYApq7mNE7CpzucIPc+ZQg25Phej7IJSmX3hO+oblOtQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      is-buffer: 1.1.6
    dev: false

  /kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}
    dev: false

  /l7-tiny-sdf@0.0.3:
    resolution: {integrity: sha512-11y9TXdBAlsbIXtkIUOJHU40aZ4VQGZxbDr8pCqgmhgLlY0WKRdA8fnC1A34SWB/VhNdxW2T80hOvepAL/G8/w==}
    dev: false

  /l7eval5@0.0.3:
    resolution: {integrity: sha512-xnn9x/T0zawTM1L9DASmRXVMb5fTCib83FtGZQcn5ToM1lAo4dutNOK2JAC+jd3mEMWa9MMq188dyoQcqG2WOg==}
    dependencies:
      '@babel/runtime': 7.25.6
      '@types/acorn': 4.0.6
      '@types/estree': 0.0.41
      acorn: 7.4.1
    dev: false

  /l7hammerjs@0.0.7:
    resolution: {integrity: sha512-IFa3c0sOHBI09uK03GlKnk3Var5LXrCqL1JbnDpy63CQz8urZTk0dynGad+WU2iErVJcumHQokrR5Zt57tePZw==}
    engines: {node: '>=0.8.0'}
    dev: false

  /l7regl@0.0.20:
    resolution: {integrity: sha512-r2vDjsCKp1bbcjTVGtKovpOxn7kdcx6cup9nd47/dIZ4+ujZAXz2C/HPx4yrxzuNig81TeXpHHWslNpLolSg8A==}
    dependencies:
      falafel: 2.2.5
      l7eval5: 0.0.3
    dev: false

  /lazy-cache@1.0.4:
    resolution: {integrity: sha512-RE2g0b5VGZsOCFOCgP7omTRYFqydmZkBwl5oNnQ1lDYC57uyO9KqNnNVxT7COSHTxrRCWVcAVOcbjk+tvh/rgQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /levn@0.3.0:
    resolution: {integrity: sha512-0OO4y2iOHix2W6ujICbKIaEQXvFQHue65vUG3pb5EUomzPI90z9hsA1VsO/dbIIpC53J8gxM9Q4Oho0jrCM/yA==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2
      type-check: 0.3.2
    dev: true

  /lilconfig@3.1.3:
    resolution: {integrity: sha512-/vlFKAoH5Cgt3Ie+JLhRbwOsCQePABiU3tJ1egGvyQ+33R/vcwM2Zl2QR/LzjsBeItPt3oSVXapn+m4nQDvpzw==}
    engines: {node: '>=14'}
    dev: true

  /lineclip@1.1.5:
    resolution: {integrity: sha512-KlA/wRSjpKl7tS9iRUdlG72oQ7qZ1IlVbVgHwoO10TBR/4gQ86uhKow6nlzMAJJhjCWKto8OeoAzzIzKSmN25A==}
    dev: false

  /lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}
    dev: true

  /lint-staged@10.5.4:
    resolution: {integrity: sha512-EechC3DdFic/TdOPgj/RB3FicqE6932LTHCUm0Y2fsD9KGlLB+RwJl2q1IYBIvEsKzDOgn0D4gll+YxG5RsrKg==}
    hasBin: true
    dependencies:
      chalk: 4.1.2
      cli-truncate: 2.1.0
      commander: 6.2.1
      cosmiconfig: 7.1.0
      debug: 4.3.4
      dedent: 0.7.0
      enquirer: 2.4.1
      execa: 4.1.0
      listr2: 3.14.0(enquirer@2.4.1)
      log-symbols: 4.1.0
      micromatch: 4.0.5
      normalize-path: 3.0.0
      please-upgrade-node: 3.2.0
      string-argv: 0.3.1
      stringify-object: 3.3.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  /listr2@3.14.0(enquirer@2.4.1):
    resolution: {integrity: sha512-TyWI8G99GX9GjE54cJ+RrNMcIFBfwMPxc3XTFiAYGN4s10hWROGtOg7+O6u6LE3mNkyld7RSLE6nrKBvTfcs3g==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      enquirer: '>= 2.3.0 < 3'
    peerDependenciesMeta:
      enquirer:
        optional: true
    dependencies:
      cli-truncate: 2.1.0
      colorette: 2.0.20
      enquirer: 2.4.1
      log-update: 4.0.0
      p-map: 4.0.0
      rfdc: 1.3.0
      rxjs: 7.8.1
      through: 2.3.8
      wrap-ansi: 7.0.0
    dev: true

  /loader-utils@2.0.4:
    resolution: {integrity: sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==}
    engines: {node: '>=8.9.0'}
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3
    dev: true

  /locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}
    dependencies:
      p-locate: 5.0.0
    dev: true

  /lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}
    dev: false

  /lodash.camelcase@4.3.0:
    resolution: {integrity: sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==}
    dev: false

  /lodash.clamp@4.0.3:
    resolution: {integrity: sha512-HvzRFWjtcguTW7yd8NJBshuNaCa8aqNFtnswdT7f/cMd/1YKy5Zzoq4W/Oxvnx9l7aeY258uSdDfM793+eLsVg==}
    dev: false

  /lodash.clonedeep@4.5.0:
    resolution: {integrity: sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ==}
    dev: false

  /lodash.debounce@4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}
    dev: false

  /lodash.isequal@4.5.0:
    resolution: {integrity: sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==}
    dev: false

  /lodash.isfinite@3.3.2:
    resolution: {integrity: sha512-7FGG40uhC8Mm633uKW1r58aElFlBlxCrg9JfSi3P6aYiWmfiWF0PgMd86ZUsxE5GwWPdHoS2+48bwTh2VPkIQA==}
    dev: false

  /lodash.isstring@4.0.1:
    resolution: {integrity: sha512-0wJxfxH1wgO3GrbuP+dTTk7op+6L41QCXbGINEmD+ny/G/eCqGzxyCsh7159S+mgDDcoarnBw6PC1PS5+wUGgw==}
    dev: false

  /lodash.isundefined@3.0.1:
    resolution: {integrity: sha512-MXB1is3s899/cD8jheYYE2V9qTHwKvt+npCwpD+1Sxm3Q3cECXCiYHjeHWXNwr6Q0SOBPrYUDxendrO6goVTEA==}
    dev: false

  /lodash.result@4.5.2:
    resolution: {integrity: sha512-dlgJvozORK2oE4jXzTGIsJz9Vk6huNAINxYYvWc/R44x3/ah/F7OkNwr9c0wO2poh2cbdjS0jF3j8VgyCEOVfw==}
    dev: false

  /lodash.set@4.3.2:
    resolution: {integrity: sha512-4hNPN5jlm/N/HLMCO43v8BXKq9Z7QdAGc/VGrRD61w8gN9g/6jF9A4L1pbUgBLCffi0w9VsXfTOij5x8iTyFvg==}
    dev: false

  /lodash.throttle@4.1.1:
    resolution: {integrity: sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ==}
    dev: false

  /lodash.upperfirst@4.3.1:
    resolution: {integrity: sha512-sReKOYJIJf74dhJONhU4e0/shzi1trVbSWDOhKYE5XV2O+H7Sb2Dihwuc7xWxVl+DgFPyTqIN3zMfT9cq5iWDg==}
    dev: false

  /lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  /log-symbols@4.1.0:
    resolution: {integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==}
    engines: {node: '>=10'}
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0
    dev: true

  /log-update@4.0.0:
    resolution: {integrity: sha512-9fkkDevMefjg0mmzWFBW8YkFP91OrizzkW3diF7CpG+S2EYdy4+TVfGwz1zeF8x7hCx1ovSPTOE9Ngib74qqUg==}
    engines: {node: '>=10'}
    dependencies:
      ansi-escapes: 4.3.2
      cli-cursor: 3.1.0
      slice-ansi: 4.0.0
      wrap-ansi: 6.2.0
    dev: true

  /longest@1.0.1:
    resolution: {integrity: sha512-k+yt5n3l48JU4k8ftnKG6V7u32wyH2NfKzeMto9F/QRE0amxy/LayxwlvjjkZEIzqR+19IrtFO8p5kB9QaYUFg==}
    engines: {node: '>=0.10.0'}
    dev: false

  /loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true
    dependencies:
      js-tokens: 4.0.0

  /lru-cache@10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}
    dev: true

  /lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}
    dependencies:
      yallist: 4.0.0
    dev: true

  /mana-common@0.3.2:
    resolution: {integrity: sha512-1oylLR9CcyshQcTiBdM96HxIoaA413eudpo1INiOCwyZE554TDJNUD0zrIjmeBvw8CBAjukzaAHn2gn0hZphIg==}
    dev: false

  /mana-syringe@0.2.2:
    resolution: {integrity: sha512-Sv5r0/PrQRq4pW+9lDicGsEPzPLkd1PwjTs5zHUV1I293S3alkBNyuSjktVeBploofH8MAMLd4DS2crwct48wg==}
    dependencies:
      inversify: 5.1.1
    dev: false

  /mapbox-gl@1.13.3:
    resolution: {integrity: sha512-p8lJFEiqmEQlyv+DQxFAOG/XPWN0Wp7j/Psq93Zywz7qt9CcUKFYDBOoOEKzqe6gudHVJY8/Bhqw6VDpX2lSBg==}
    engines: {node: '>=6.4.0'}
    dependencies:
      '@mapbox/geojson-rewind': 0.5.2
      '@mapbox/geojson-types': 1.0.2
      '@mapbox/jsonlint-lines-primitives': 2.0.2
      '@mapbox/mapbox-gl-supported': 1.5.0(mapbox-gl@1.13.3)
      '@mapbox/point-geometry': 0.1.0
      '@mapbox/tiny-sdf': 1.2.5
      '@mapbox/unitbezier': 0.0.0
      '@mapbox/vector-tile': 1.3.1
      '@mapbox/whoots-js': 3.1.0
      csscolorparser: 1.0.3
      earcut: 2.2.4
      geojson-vt: 3.2.1
      gl-matrix: 3.4.3
      grid-index: 1.1.0
      murmurhash-js: 1.0.0
      pbf: 3.2.1
      potpack: 1.0.2
      quickselect: 2.0.0
      rw: 1.3.3
      supercluster: 7.1.5
      tinyqueue: 2.0.3
      vt-pbf: 3.1.3
    dev: false

  /maplibre-gl@3.6.2:
    resolution: {integrity: sha512-krg2KFIdOpLPngONDhP6ixCoWl5kbdMINP0moMSJFVX7wX1Clm2M9hlNKXS8vBGlVWwR5R3ZfI6IPrYz7c+aCQ==}
    engines: {node: '>=16.14.0', npm: '>=8.1.0'}
    dependencies:
      '@mapbox/geojson-rewind': 0.5.2
      '@mapbox/jsonlint-lines-primitives': 2.0.2
      '@mapbox/point-geometry': 0.1.0
      '@mapbox/tiny-sdf': 2.0.6
      '@mapbox/unitbezier': 0.0.1
      '@mapbox/vector-tile': 1.3.1
      '@mapbox/whoots-js': 3.1.0
      '@maplibre/maplibre-gl-style-spec': 19.3.3
      '@types/geojson': 7946.0.16
      '@types/mapbox__point-geometry': 0.1.4
      '@types/mapbox__vector-tile': 1.3.4
      '@types/pbf': 3.0.5
      '@types/supercluster': 7.1.3
      earcut: 2.2.4
      geojson-vt: 3.2.1
      gl-matrix: 3.4.3
      global-prefix: 3.0.0
      kdbush: 4.0.2
      murmurhash-js: 1.0.0
      pbf: 3.2.1
      potpack: 2.0.0
      quickselect: 2.0.0
      supercluster: 8.0.1
      tinyqueue: 2.0.3
      vt-pbf: 3.1.3
    dev: false

  /material-colors@1.2.6:
    resolution: {integrity: sha512-6qE4B9deFBIa9YSpOc9O0Sgc43zTeVYbgDT5veRKSlB2+ZuHNoVVxA1L/ckMUayV9Ay9y7Z/SZCLcGteW9i7bg==}
    dev: false

  /math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  /md5.js@1.3.5:
    resolution: {integrity: sha512-xitP+WxNPcTTOgnTJcrhM0xvdPepipPSf3I8EIpGKeFLjt3PlJLIDG3u8EX53ZIubkb+5U2+3rELYpEhHhzdkg==}
    dependencies:
      hash-base: 3.0.5
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: true

  /mdn-data@2.0.14:
    resolution: {integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==}
    dev: false

  /memoize-one@5.2.1:
    resolution: {integrity: sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q==}
    dev: false

  /memory-cache@0.2.0:
    resolution: {integrity: sha512-OcjA+jzjOYzKmKS6IQVALHLVz+rNTMPoJvCztFaZxwG14wtAW7VRZjwTQu06vKCYOxh4jVnik7ya0SXTB0W+xA==}
    dev: false

  /merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}
    dev: true

  /merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}
    dev: true

  /micromatch@4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1

  /micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1
    dev: true

  /miller-rabin@4.0.1:
    resolution: {integrity: sha512-115fLhvZVqWwHPbClyntxEVfVDfl9DLLTuJvq3g2O/Oxi8AiNouAHvDSzHS0viUJc+V5vm3eq91Xwqn9dp4jRA==}
    hasBin: true
    dependencies:
      bn.js: 4.12.1
      brorand: 1.1.0
    dev: true

  /mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}
    dev: true

  /minimalistic-assert@1.0.1:
    resolution: {integrity: sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==}
    dev: true

  /minimalistic-crypto-utils@1.0.1:
    resolution: {integrity: sha512-JIYlbt6g8i5jKfJ3xz7rF0LXmv2TkDxBLUkiBeZ7bAx4GnnNMr8xFpGnOxn6GhTEHx3SjRrZEoU+j04prX1ktg==}
    dev: true

  /minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}
    dependencies:
      brace-expansion: 1.1.11

  /minimatch@9.0.3:
    resolution: {integrity: sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==}
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: 2.0.1
    dev: true

  /minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}
    dependencies:
      brace-expansion: 2.0.1
    dev: true

  /minimist@1.2.6:
    resolution: {integrity: sha512-Jsjnk4bw3YJqYzbdyBiNsPWHPfO++UGG749Cxs6peCu5Xg4nrena6OVxOYxrQTqww0Jmwt+Ref8rggumkTLz9Q==}
    dev: false

  /minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  /minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}
    dev: true

  /mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true
    dependencies:
      minimist: 1.2.8
    dev: true

  /ml-array-max@1.2.4:
    resolution: {integrity: sha512-BlEeg80jI0tW6WaPyGxf5Sa4sqvcyY6lbSn5Vcv44lp1I2GR6AWojfUvLnGTNsIXrZ8uqWmo8VcG1WpkI2ONMQ==}
    dependencies:
      is-any-array: 2.0.1
    dev: false

  /ml-array-min@1.2.3:
    resolution: {integrity: sha512-VcZ5f3VZ1iihtrGvgfh/q0XlMobG6GQ8FsNyQXD3T+IlstDv85g8kfV0xUG1QPRO/t21aukaJowDzMTc7j5V6Q==}
    dependencies:
      is-any-array: 2.0.1
    dev: false

  /ml-array-rescale@1.3.7:
    resolution: {integrity: sha512-48NGChTouvEo9KBctDfHC3udWnQKNKEWN0ziELvY3KG25GR5cA8K8wNVzracsqSW1QEkAXjTNx+ycgAv06/1mQ==}
    dependencies:
      is-any-array: 2.0.1
      ml-array-max: 1.2.4
      ml-array-min: 1.2.3
    dev: false

  /ml-matrix@6.10.2:
    resolution: {integrity: sha512-+yxwzMazC76DAsgEvDrdCfRMH7+lO7UdnuWllavnPeWkPD2oIBA6rLzvst7BFYUxzgRz/h6V6lLzYIYtTvINbw==}
    dependencies:
      is-any-array: 2.0.1
      ml-array-rescale: 1.3.7
    dev: false

  /ml-matrix@6.10.4:
    resolution: {integrity: sha512-rUyEhfNPzqFsltYwvjNeYQXlYEaVea3KgzcJKJteQUj2WVAGFx9fLNRjtMR9mg2B6bd5buxlmkZmxM4hmO+SKg==}
    dependencies:
      is-any-array: 2.0.1
      ml-array-rescale: 1.3.7
    dev: false

  /ml-matrix@6.5.0:
    resolution: {integrity: sha512-sms732Dge+rs5dU4mnjE0oqLWm1WujvR2fr38LgUHRG2cjXjWlO3WJupLYaSz3++2iYr0UrGDK72OAivr3J8dg==}
    dependencies:
      ml-array-rescale: 1.3.7
    dev: false

  /moment@2.29.4:
    resolution: {integrity: sha512-5LC9SOxjSc2HF6vO2CyuTDNivEdoz2IvyJJGj6X8DJ0eFyfszE0QiEd+iXmBvUP3WHxSjFH/vIsA0EN00cgr8w==}
    dev: false

  /mousetrap@1.6.5:
    resolution: {integrity: sha512-QNo4kEepaIBwiT8CDhP98umTetp+JNfQYBWvC1pc6/OAibuXtRcxZ58Qz8skvEHYvURne/7R8T5VoOI7rDsEUA==}
    dev: false

  /ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}
    dev: false

  /ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}

  /ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}
    dev: true

  /murmurhash-js@1.0.0:
    resolution: {integrity: sha512-TvmkNhkv8yct0SVBSy+o8wYzXjE4Zz3PCesbfs8HiCXXdcTuocApFv11UWlNFWKYsP2okqrhb7JNlSm9InBhIw==}
    dev: false

  /mute-stream@0.0.8:
    resolution: {integrity: sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==}
    dev: true

  /mz@2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0
    dev: true

  /nano-css@5.3.5(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-vSB9X12bbNu4ALBu7nigJgRViZ6ja3OU7CeuiV1zMIbXOdmkLahgtPmh3GBOlDxbKY0CitqlPdOReGlBLSp+yg==}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      css-tree: 1.1.3
      csstype: 3.1.2
      fastest-stable-stringify: 2.0.2
      inline-style-prefixer: 6.0.4
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      rtl-css-js: 1.16.1
      sourcemap-codec: 1.4.8
      stacktrace-js: 2.0.2
      stylis: 4.3.0
    dev: false

  /nanoid@3.3.8:
    resolution: {integrity: sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true
    dev: true

  /natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}
    dev: true

  /neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}
    dev: true

  /nice-try@1.0.5:
    resolution: {integrity: sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==}
    dev: true

  /node-addon-api@7.1.1:
    resolution: {integrity: sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==}
    optional: true

  /node-fetch@1.7.3:
    resolution: {integrity: sha512-NhZ4CsKx7cYm2vSrBAr2PvFOe6sWDf0UYLRqA6svUYg7+/TSfVAu49jYC4BvQ4Sms9SZgdqGBgroqfDhJdTyKQ==}
    dependencies:
      encoding: 0.1.13
      is-stream: 1.1.0
    dev: false

  /node-polyfill-webpack-plugin@4.1.0:
    resolution: {integrity: sha512-b4ei444EKkOagG/yFqojrD3QTYM5IOU1f8tn9o6uwrG4qL+brI7oVhjPVd0ZL2xy+Z6CP5bu9w8XTvlWgiXHcw==}
    engines: {node: '>=14'}
    peerDependencies:
      webpack: '>=5'
    dependencies:
      node-stdlib-browser: 1.3.0
      type-fest: 4.31.0
    dev: true

  /node-stdlib-browser@1.3.0:
    resolution: {integrity: sha512-g/koYzOr9Fb1Jc+tHUHlFd5gODjGn48tHexUK8q6iqOVriEgSnd3/1T7myBYc+0KBVze/7F7n65ec9rW6OD7xw==}
    engines: {node: '>=10'}
    dependencies:
      assert: 2.1.0
      browser-resolve: 2.0.0
      browserify-zlib: 0.2.0
      buffer: 5.7.1
      console-browserify: 1.2.0
      constants-browserify: 1.0.0
      create-require: 1.1.1
      crypto-browserify: 3.12.1
      domain-browser: 4.22.0
      events: 3.3.0
      https-browserify: 1.0.0
      isomorphic-timers-promises: 1.0.1
      os-browserify: 0.3.0
      path-browserify: 1.0.1
      pkg-dir: 5.0.0
      process: 0.11.10
      punycode: 1.4.1
      querystring-es3: 0.2.1
      readable-stream: 3.6.2
      stream-browserify: 3.0.0
      stream-http: 3.2.0
      string_decoder: 1.3.0
      timers-browserify: 2.0.12
      tty-browserify: 0.0.1
      url: 0.11.4
      util: 0.12.5
      vm-browserify: 1.1.2
    dev: true

  /normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}
    dependencies:
      path-key: 3.1.1
    dev: true

  /object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  /object-hash@3.0.0:
    resolution: {integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==}
    engines: {node: '>= 6'}
    dev: true

  /object-inspect@1.12.3:
    resolution: {integrity: sha512-geUvdk7c+eizMNUDkRpW1wJwgfOiOeHbxBR/hLXK1aT6zmVSO0jsQcs7fj6MGw89jC/cjGfLcNOrtMYtGqm81g==}

  /object-inspect@1.13.3:
    resolution: {integrity: sha512-kDCGIbxkDSXE3euJZZXzc6to7fCrKHNI/hSRQnRuQ+BWjFNzZwiFF8fj/6o2t2G9/jTj8PSIYTfCLelLZEeRpA==}
    engines: {node: '>= 0.4'}
    dev: true

  /object-is@1.1.5:
    resolution: {integrity: sha512-3cyDsyHgtmi7I7DfSSI2LDp6SK2lwvtbg0p0R1e0RvTqF5ceGx+K2dfSjm1bKDMVCFEDAQvy+o8c6a7VujOddw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1

  /object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  /object.assign@4.1.4:
    resolution: {integrity: sha512-1mxKf0e58bvyjSCtKYY4sRe9itRk3PJpquJOjeIkz885CczcI4IvJJDLPS72oowuSh+pBxUFROpX+TU++hxhZQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      has-symbols: 1.0.3
      object-keys: 1.1.1

  /object.assign@4.1.7:
    resolution: {integrity: sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1
    dev: true

  /object.entries@1.1.6:
    resolution: {integrity: sha512-leTPzo4Zvg3pmbQ3rDK69Rl8GQvIqMWubrkxONG9/ojtFE2rD9fjMKfSI5BxW3osRH1m6VdzmqK8oAY9aT4x5w==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
    dev: true

  /object.fromentries@2.0.6:
    resolution: {integrity: sha512-VciD13dswC4j1Xt5394WR4MzmAQmlgN72phd/riNp9vtD7tp4QQWJ0R4wvclXcafgcYK8veHRed2W6XeGBvcfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
    dev: true

  /object.fromentries@2.0.8:
    resolution: {integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.0.0
    dev: true

  /object.groupby@1.0.3:
    resolution: {integrity: sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
    dev: true

  /object.values@1.1.6:
    resolution: {integrity: sha512-FVVTkD1vENCsAcwNs9k6jea2uHC/X0+JcjG8YA60FN5CMaJmG95wT9jek/xX9nornqGRrBkKtzuAu2wuHpKqvw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.2
      define-properties: 1.2.0
      es-abstract: 1.22.1
    dev: true

  /object.values@1.2.1:
    resolution: {integrity: sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-object-atoms: 1.0.0
    dev: true

  /omit.js@1.0.2:
    resolution: {integrity: sha512-/QPc6G2NS+8d4L/cQhbk6Yit1WTB6Us2g84A7A/1+w9d/eRGHyEqC5kkQtHVoHZ5NFWGG7tUGgrhVZwgZanKrQ==}
    dependencies:
      babel-runtime: 6.26.0
    dev: false

  /once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}
    dependencies:
      wrappy: 1.0.2

  /onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: 2.1.0
    dev: true

  /opencollective-postinstall@2.0.3:
    resolution: {integrity: sha512-8AV/sCtuzUeTo8gQK5qDZzARrulB3egtLzFgteqB2tcT4Mw7B8Kt7JcDHmltjz6FOAHsvTevk70gZEbhM4ZS9Q==}
    hasBin: true
    dev: true

  /optionator@0.8.3:
    resolution: {integrity: sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.3.0
      prelude-ls: 1.1.2
      type-check: 0.3.2
      word-wrap: 1.2.5
    dev: true

  /os-browserify@0.3.0:
    resolution: {integrity: sha512-gjcpUc3clBf9+210TRaDWbf+rZZZEshZ+DlXMRCeAjp0xhTrnQsKHypIy1J3d5hKdUzj69t708EHtU8P6bUn0A==}
    dev: true

  /os-tmpdir@1.0.2:
    resolution: {integrity: sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==}
    engines: {node: '>=0.10.0'}
    dev: true

  /own-keys@1.0.1:
    resolution: {integrity: sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: 1.3.0
      object-keys: 1.1.1
      safe-push-apply: 1.0.0
    dev: true

  /p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}
    dependencies:
      yocto-queue: 0.1.0
    dev: true

  /p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}
    dependencies:
      p-limit: 3.1.0
    dev: true

  /p-map@4.0.0:
    resolution: {integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==}
    engines: {node: '>=10'}
    dependencies:
      aggregate-error: 3.1.0
    dev: true

  /package-json-from-dist@1.0.1:
    resolution: {integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==}
    dev: true

  /pako@1.0.11:
    resolution: {integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==}
    dev: true

  /parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}
    dependencies:
      callsites: 3.1.0
    dev: true

  /parse-asn1@5.1.7:
    resolution: {integrity: sha512-CTM5kuWR3sx9IFamcl5ErfPl6ea/N8IYwiJ+vpeB2g+1iknv7zBl5uPwbMbRVznRVbrNY6lGuDoE5b30grmbqg==}
    engines: {node: '>= 0.10'}
    dependencies:
      asn1.js: 4.10.1
      browserify-aes: 1.2.0
      evp_bytestokey: 1.0.3
      hash-base: 3.0.5
      pbkdf2: 3.1.2
      safe-buffer: 5.2.1
    dev: true

  /parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': 7.22.13
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4
    dev: true

  /path-browserify@1.0.1:
    resolution: {integrity: sha512-b7uo2UCUOYZcnF/3ID0lulOJi/bafxa1xPe7ZPsammBSpjSWQkjNxlt635YGS2MiR9GjvuXCtz2emr3jbsz98g==}
    dev: true

  /path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}
    dev: true

  /path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  /path-key@2.0.1:
    resolution: {integrity: sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==}
    engines: {node: '>=4'}
    dev: true

  /path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}
    dev: true

  /path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  /path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2
    dev: true

  /path-source@0.1.3:
    resolution: {integrity: sha512-dWRHm5mIw5kw0cs3QZLNmpUWty48f5+5v9nWD2dw3Y0Hf+s01Ag8iJEWV0Sm0kocE8kK27DrIowha03e1YR+Qw==}
    dependencies:
      array-source: 0.0.4
      file-source: 0.6.1
    dev: false

  /path-to-regexp@1.9.0:
    resolution: {integrity: sha512-xIp7/apCFJuUHdDLWe8O1HIkb0kQrOMb/0u6FXQjemHn/ii5LrIzU6bdECnsiTF/GjZkMEKg1xdiZwNqDYlZ6g==}
    dependencies:
      isarray: 0.0.1
    dev: false

  /path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}
    dev: true

  /pbf@3.2.1:
    resolution: {integrity: sha512-ClrV7pNOn7rtmoQVF4TS1vyU0WhYRnP92fzbfF75jAIwpnzdJXf8iTd4CMEqO4yUenH6NDqLiwjqlh6QgZzgLQ==}
    hasBin: true
    dependencies:
      ieee754: 1.2.1
      resolve-protobuf-schema: 2.1.0
    dev: false

  /pbkdf2@3.1.2:
    resolution: {integrity: sha512-iuh7L6jA7JEGu2WxDwtQP1ddOpaJNC4KlDEFfdQajSGgGPNi4OyDc2R7QnbY2bR9QjBVGwgvTdNJZoE7RaxUMA==}
    engines: {node: '>=0.12'}
    dependencies:
      create-hash: 1.2.0
      create-hmac: 1.1.7
      ripemd160: 2.0.2
      safe-buffer: 5.2.1
      sha.js: 2.4.11
    dev: true

  /pdfast@0.2.0:
    resolution: {integrity: sha512-cq6TTu6qKSFUHwEahi68k/kqN2mfepjkGrG9Un70cgdRRKLKY6Rf8P8uvP2NvZktaQZNF3YE7agEkLj0vGK9bA==}
    dev: false

  /performance-now@2.1.0:
    resolution: {integrity: sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow==}
    dev: false

  /picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  /picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  /pify@2.3.0:
    resolution: {integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==}
    engines: {node: '>=0.10.0'}
    dev: true

  /pirates@4.0.6:
    resolution: {integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==}
    engines: {node: '>= 6'}
    dev: true

  /pkg-dir@5.0.0:
    resolution: {integrity: sha512-NPE8TDbzl/3YQYY7CSS228s3g2ollTFnc+Qi3tqmqJp9Vg2ovUpixcJEo2HJScN2Ez+kEaal6y70c0ehqJBJeA==}
    engines: {node: '>=10'}
    dependencies:
      find-up: 5.0.0
    dev: true

  /please-upgrade-node@3.2.0:
    resolution: {integrity: sha512-gQR3WpIgNIKwBMVLkpMUeR3e1/E1y42bqDQZfql+kDeXd8COYfM8PQA4X6y7a8u9Ua9FHmsrrmirW2vHs45hWg==}
    dependencies:
      semver-compare: 1.0.0
    dev: true

  /pmtiles@2.11.0:
    resolution: {integrity: sha512-dU9SzzaqmCGpdEuTnIba6bDHT6j09ZJFIXxwGpvkiEnce3ZnBB1VKt6+EOmJGueriweaZLAMTUmKVElU2CBe0g==}
    dependencies:
      fflate: 0.8.2
    dev: false

  /point-in-polygon@1.1.0:
    resolution: {integrity: sha512-3ojrFwjnnw8Q9242TzgXuTD+eKiutbzyslcq1ydfu82Db2y+Ogbmyrkpv0Hgj31qwT3lbS9+QAAO/pIQM35XRw==}
    dev: false

  /polygon-clipping@0.15.3:
    resolution: {integrity: sha512-ho0Xx5DLkgxRx/+n4O74XyJ67DcyN3Tu9bGYKsnTukGAW6ssnuak6Mwcyb1wHy9MZc9xsUWqIoiazkZB5weECg==}
    dependencies:
      splaytree: 3.1.2
    dev: false

  /polyline-miter-util@1.0.1:
    resolution: {integrity: sha512-/3u91zz6mBerBZo6qnOJOTjv7EfPhKtsV028jMyj86YpzLRNmCCFfrX7IO9tCEQ2W4x45yc+vKOezjf7u2Nd6Q==}
    dependencies:
      gl-vec2: 1.3.0
    dev: false

  /polyline-normals@2.0.2:
    resolution: {integrity: sha512-dpHrAi61ymhsB4N0XlNb3YpkKJeTFnXBXDWpeH8Ucstq0TUZrCN3YK4Jlgk8ofMWN25lhGC4wnxLMv+TUK8rig==}
    dependencies:
      polyline-miter-util: 1.0.1
    dev: false

  /possible-typed-array-names@1.1.0:
    resolution: {integrity: sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==}
    engines: {node: '>= 0.4'}
    dev: true

  /postcss-import@15.1.0(postcss@8.5.3):
    resolution: {integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      postcss: ^8.0.0
    dependencies:
      postcss: 8.5.3
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.10
    dev: true

  /postcss-js@4.0.1(postcss@8.5.3):
    resolution: {integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==}
    engines: {node: ^12 || ^14 || >= 16}
    peerDependencies:
      postcss: ^8.4.21
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.5.3
    dev: true

  /postcss-load-config@4.0.2(postcss@8.5.3):
    resolution: {integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==}
    engines: {node: '>= 14'}
    peerDependencies:
      postcss: '>=8.0.9'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true
    dependencies:
      lilconfig: 3.1.3
      postcss: 8.5.3
      yaml: 2.7.0
    dev: true

  /postcss-loader@8.1.1(@rspack/core@1.2.8)(postcss@8.5.3)(typescript@5.0.4):
    resolution: {integrity: sha512-0IeqyAsG6tYiDRCYKQJLAmgQr47DX6N7sFSWvQxt6AcupX8DIdmykuk/o/tx0Lze3ErGHJEp5OSRxrelC6+NdQ==}
    engines: {node: '>= 18.12.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      postcss: ^7.0.0 || ^8.0.1
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      webpack:
        optional: true
    dependencies:
      '@rspack/core': 1.2.8
      cosmiconfig: 9.0.0(typescript@5.0.4)
      jiti: 1.21.6
      postcss: 8.5.3
      semver: 7.5.4
    transitivePeerDependencies:
      - typescript
    dev: true

  /postcss-nested@6.2.0(postcss@8.5.3):
    resolution: {integrity: sha512-HQbt28KulC5AJzG+cZtj9kvKB93CFCdLvog1WFLf1D+xmMvPGlBstkpTEZfK5+AN9hfJocyBFCNiqyS48bpgzQ==}
    engines: {node: '>=12.0'}
    peerDependencies:
      postcss: ^8.2.14
    dependencies:
      postcss: 8.5.3
      postcss-selector-parser: 6.1.2
    dev: true

  /postcss-selector-parser@6.1.2:
    resolution: {integrity: sha512-Q8qQfPiZ+THO/3ZrOrO0cJJKfpYCagtMUkXbnEfmgUjwXg6z/WBeOyS9APBBPCTSiDV+s4SwQGu8yFsiMRIudg==}
    engines: {node: '>=4'}
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2
    dev: true

  /postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}
    dev: true

  /postcss@8.5.3:
    resolution: {integrity: sha512-dle9A3yYxlBSrt8Fu+IpjGT8SY8hN0mlaA6GY8t0P5PjIOZemULz/E2Bnm/2dcUOena75OTNkHI76uZBNUUq3A==}
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: 3.3.8
      picocolors: 1.1.1
      source-map-js: 1.2.1
    dev: true

  /potpack@1.0.2:
    resolution: {integrity: sha512-choctRBIV9EMT9WGAZHn3V7t0Z2pMQyl0EZE6pFc/6ml3ssw7Dlf/oAOvFwjm1HVsqfQN8GfeFyJ+d8tRzqueQ==}
    dev: false

  /potpack@2.0.0:
    resolution: {integrity: sha512-Q+/tYsFU9r7xoOJ+y/ZTtdVQwTWfzjbiXBDMM/JKUux3+QPP02iUuIoeBQ+Ot6oEDlC+/PGjB/5A3K7KKb7hcw==}
    dev: false

  /prelude-ls@1.1.2:
    resolution: {integrity: sha512-ESF23V4SKG6lVSGZgYNpbsiaAkdab6ZgOxe52p7+Kid3W3u3bxR4Vfd/o21dmN7jSt0IwgZ4v5MUd26FEtXE9w==}
    engines: {node: '>= 0.8.0'}
    dev: true

  /prettier@3.5.2:
    resolution: {integrity: sha512-lc6npv5PH7hVqozBR7lkBNOGXV9vMwROAPlumdBkX0wTbbzPu/U1hk5yL8p2pt4Xoc+2mkT8t/sow2YrV/M5qg==}
    engines: {node: '>=14'}
    hasBin: true
    dev: true

  /pretty-format@29.6.3:
    resolution: {integrity: sha512-ZsBgjVhFAj5KeK+nHfF1305/By3lechHQSMWCTl8iHSbfOm2TN5nHEtFc/+W7fAyUeCs2n5iow72gld4gW0xDw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    dependencies:
      '@jest/schemas': 29.6.3
      ansi-styles: 5.2.0
      react-is: 18.2.0
    dev: true

  /probe.gl@3.6.0:
    resolution: {integrity: sha512-19JydJWI7+DtR4feV+pu4Mn1I5TAc0xojuxVgZdXIyfmTLfUaFnk4OloWK1bKbPtkgGKLr2lnbnCXmpZEcEp9g==}
    dependencies:
      '@babel/runtime': 7.25.6
      '@probe.gl/env': 3.6.0
      '@probe.gl/log': 3.6.0
      '@probe.gl/stats': 3.6.0
    dev: false

  /process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}
    dev: true

  /process@0.11.10:
    resolution: {integrity: sha512-cdGef/drWFoydD1JsMzuFf8100nZl+GT+yacc2bEced5f9Rjk4z+WtFUTBu9PhOi9j/jfmBPu0mMEY4wIdAF8A==}
    engines: {node: '>= 0.6.0'}

  /progress@2.0.3:
    resolution: {integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==}
    engines: {node: '>=0.4.0'}
    dev: true

  /promise@7.3.1:
    resolution: {integrity: sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==}
    dependencies:
      asap: 2.0.6
    dev: false

  /prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  /protocol-buffers-schema@3.6.0:
    resolution: {integrity: sha512-TdDRD+/QNdrCGCE7v8340QyuXd4kIWIgapsE2+n/SaGiSSbomYl4TjHlvIoCWRpE7wFt02EpB35VVA2ImcBVqw==}
    dev: false

  /public-encrypt@4.0.3:
    resolution: {integrity: sha512-zVpa8oKZSz5bTMTFClc1fQOnyyEzpl5ozpi1B5YcvBrdohMjH2rfsBtyXcuNuwjsDIXmBYlF2N5FlJYhR29t8Q==}
    dependencies:
      bn.js: 4.12.1
      browserify-rsa: 4.1.1
      create-hash: 1.2.0
      parse-asn1: 5.1.7
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: true

  /pump@3.0.0:
    resolution: {integrity: sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==}
    dependencies:
      end-of-stream: 1.4.4
      once: 1.4.0
    dev: true

  /punycode@1.4.1:
    resolution: {integrity: sha512-jmYNElW7yvO7TV33CjSmvSiE2yco3bV2czu/OzDKdMNVZQWfxCblURLhf+47syQRBntjfLdd/H0egrzIG+oaFQ==}
    dev: true

  /punycode@2.3.0:
    resolution: {integrity: sha512-rRV+zQD8tVFys26lAGR9WUuS4iUAngJScM+ZRSKtvl5tKeZ2t5bvdNFdNHBW9FWR4guGHlgmsZ1G7BSm2wTbuA==}
    engines: {node: '>=6'}

  /qs@6.11.2:
    resolution: {integrity: sha512-tDNIz22aBzCDxLtVH++VnTfzxlfeK5CbqohpSqpJgj1Wg/cQbStNAz3NuqCs5vV+pjBsK4x4pN9HlVh7rcYRiA==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.0.4
    dev: false

  /qs@6.13.1:
    resolution: {integrity: sha512-EJPeIn0CYrGu+hli1xilKAPXODtJ12T0sP63Ijx2/khC2JtuaN3JyNIpvmnkmaEtha9ocbG4A4cMcr+TvqvwQg==}
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: 1.1.0
    dev: true

  /querystring-es3@0.2.1:
    resolution: {integrity: sha512-773xhDQnZBMFobEiztv8LIl70ch5MSF/jUQVlhwFyBILqq96anmoctVIYz+ZRp0qbCKATTn6ev02M3r7Ga5vqA==}
    engines: {node: '>=0.4.x'}
    dev: true

  /querystring@0.2.1:
    resolution: {integrity: sha512-wkvS7mL/JMugcup3/rMitHmd9ecIGd2lhFhK9N3UUQ450h66d1r3Y9nvXzQAW1Lq+wyx61k/1pfKS5KuKiyEbg==}
    engines: {node: '>=0.4.x'}
    deprecated: The querystring API is considered Legacy. new code should use the URLSearchParams API instead.
    dev: false

  /queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}
    dev: true

  /quickselect@1.1.1:
    resolution: {integrity: sha512-qN0Gqdw4c4KGPsBOQafj6yj/PA6c/L63f6CaZ/DCF/xF4Esu3jVmKLUDYxghFx8Kb/O7y9tI7x2RjTSXwdK1iQ==}
    dev: false

  /quickselect@2.0.0:
    resolution: {integrity: sha512-RKJ22hX8mHe3Y6wH/N3wCM6BWtjaxIyyUIkpHOvfFnxdI4yD4tBXEBKSbriGujF6jnSVkJrffuo6vxACiSSxIw==}
    dev: false

  /raf@3.4.1:
    resolution: {integrity: sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==}
    dependencies:
      performance-now: 2.1.0
    dev: false

  /randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}
    dependencies:
      safe-buffer: 5.2.1
    dev: true

  /randomfill@1.0.4:
    resolution: {integrity: sha512-87lcbR8+MhcWcUiQ+9e+Rwx8MyR2P7qnt15ynUlbm3TU/fjbgz4GsvfSUDTemtCCtVCqb4ZcEFlyPNTh9bBTLw==}
    dependencies:
      randombytes: 2.1.0
      safe-buffer: 5.2.1
    dev: true

  /rbush@2.0.2:
    resolution: {integrity: sha512-XBOuALcTm+O/H8G90b6pzu6nX6v2zCKiFG4BJho8a+bY6AER6t8uQUZdi5bomQc0AprCWhEGa7ncAbbRap0bRA==}
    dependencies:
      quickselect: 1.1.1
    dev: false

  /rbush@3.0.1:
    resolution: {integrity: sha512-XRaVO0YecOpEuIvbhbpTrZgoiI6xBlz6hnlr6EHhd+0x9ase6EmeN+hdwwUaJvLcsFFQ8iWVF1GAK1yB0BWi0w==}
    dependencies:
      quickselect: 2.0.0
    dev: false

  /rc-align@4.0.15(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-wqJtVH60pka/nOX7/IspElA8gjPNQKIx/ZqJ6heATCkXpe1Zg4cPVrMD2vC96wjsFFL8WsmhPbx9tdMo1qqlIA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      dom-align: 1.12.4
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      resize-observer-polyfill: 1.5.1
    dev: false

  /rc-animate@2.11.1(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-1NyuCGFJG/0Y+9RKh5y/i/AalUCA51opyyS/jO2seELpgymZm2u9QV3xwODwEuzkmeQ1BDPxMLmYLcTJedPlkQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.5.1
      css-animation: 1.6.1
      prop-types: 15.8.1
      raf: 3.4.1
      rc-util: 4.21.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-lifecycles-compat: 3.0.4
    dev: false

  /rc-cascader@3.7.3(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-KBpT+kzhxDW+hxPiNk4zaKa99+Lie2/8nnI11XF+FIOPl4Bj9VlFZi61GrnWzhLGA7VEN+dTxAkNOjkySDa0dA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      array-tree-filter: 2.1.0
      classnames: 2.5.1
      rc-select: 14.1.18(react-dom@16.14.0)(react@16.14.0)
      rc-tree: 5.7.10(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-checkbox@3.0.1(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-k7nxDWxYF+jDI0ZcCvuvj71xONmWRVe5+1MKcERRR9MRyP3tZ69b+yUCSXXh+sik4/Hc9P5wHr2nnUoGS2zBjA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-collapse@3.4.2(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-jpTwLgJzkhAgp2Wpi3xmbTbbYExg6fkptL67Uu5LCRVEj6wqmy0DHTjjeynsjOLsppHGHu41t1ELntZ0lEvS/Q==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-motion: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      shallowequal: 1.1.0
    dev: false

  /rc-dialog@9.0.2(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-s3U+24xWUuB6Bn2Lk/Qt6rufy+uT+QvWkiFhNBcO9APLxcFFczWamaq7x9h8SCuhfc1nHcW4y8NbMsnAjNnWyg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      '@rc-component/portal': 1.1.2(react-dom@16.14.0)(react@16.14.0)
      classnames: 2.5.1
      rc-motion: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-drawer@6.1.6(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-EBRFM9o3lPU5kYh8sFoXYA9KxpdT765HDqj/AbZWicXkhwEYUH7MjUH0ctenPCiHBxXQUgIUvK14+6rPuURd6w==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      '@rc-component/portal': 1.1.2(react-dom@16.14.0)(react@16.14.0)
      classnames: 2.5.1
      rc-motion: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-dropdown@3.6.2(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-Wsw7GkVbUXADEs8FPL0v8gd+3mWQiydPFXBlr2imMScQaf8hh79pG9KrBc1DwK+nqHmYOpQfK2gn6jG2AQw9Pw==}
    peerDependencies:
      react: '>=16.11.0'
      react-dom: '>=16.11.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-trigger: 5.3.4(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-dropdown@4.0.1(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-OdpXuOcme1rm45cR0Jzgfl1otzmU4vuBVb+etXM8vcaULGokAKVpKlw8p6xzspG7jGd/XxShvq+N3VNEfk/l5g==}
    peerDependencies:
      react: '>=16.11.0'
      react-dom: '>=16.11.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-trigger: 5.3.4(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-editor-core@0.8.10(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-T3aHpeMCIYA1sdAI7ynHHjXy5fqp83uPlD68ovZ0oClTSc3tbHmyCxXlA+Ti4YgmcpCYv7avF6a+TIbAka53kw==}
    peerDependencies:
      react: '>=15.0.0'
      react-dom: '>=15.0.0'
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.5.1
      draft-js: 0.10.5(react-dom@16.14.0)(react@16.14.0)
      immutable: 3.7.6
      lodash: 4.17.21
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      setimmediate: 1.0.5
    dev: false

  /rc-editor-mention@1.1.13(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-3AOmGir91Fi2ogfRRaXLtqlNuIwQpvla7oUnGHS1+3eo7b+fUp5IlKcagqtwUBB5oDNofoySXkLBxzWvSYNp/Q==}
    peerDependencies:
      react: '>=15.x'
      react-dom: '>=15.x'
    dependencies:
      babel-runtime: 6.26.0
      classnames: 2.5.1
      dom-scroll-into-view: 1.2.1
      draft-js: 0.10.5(react-dom@16.14.0)(react@16.14.0)
      immutable: 3.7.6
      prop-types: 15.8.1
      rc-animate: 2.11.1(react-dom@16.14.0)(react@16.14.0)
      rc-editor-core: 0.8.10(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-field-form@1.27.4(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-PQColQnZimGKArnOh8V2907+VzDCXcqtFvHgevDLtqWc/P7YASb/FqntSmdS8q3VND5SHX3Y1vgMIzY22/f/0Q==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      async-validator: 4.2.5
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-form@2.4.12(prop-types@15.8.1):
    resolution: {integrity: sha512-sHfyWRrnjCHkeCYfYAGop2GQBUC6CKMPcJF9h/gL/vTmZB/RN6fNOGKjXrXjFbwFwKXUWBoPtIDDDmXQW9xNdw==}
    peerDependencies:
      prop-types: ^15.0
    dependencies:
      async-validator: 1.11.5
      babel-runtime: 6.26.0
      create-react-class: 15.7.0
      dom-scroll-into-view: 1.2.1
      hoist-non-react-statics: 3.3.2
      lodash: 4.17.21
      prop-types: 15.8.1
      rc-util: 4.21.1
      react-is: 16.13.1
      warning: 4.0.3
    dev: false

  /rc-image@5.13.0(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-iZTOmw5eWo2+gcrJMMcnd7SsxVHl3w5xlyCgsULUdJhJbnuI8i/AL0tVOsE7aLn9VfOh1qgDT3mC2G75/c7mqg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      '@rc-component/portal': 1.1.2(react-dom@16.14.0)(react@16.14.0)
      classnames: 2.5.1
      rc-dialog: 9.0.2(react-dom@16.14.0)(react@16.14.0)
      rc-motion: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-input-number@7.3.11(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-aMWPEjFeles6PQnMqP5eWpxzsvHm9rh1jQOWXExUEIxhX62Fyl/ptifLHOn17+waDG1T/YUb6flfJbvwRhHrbA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-input@0.1.4(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-FqDdNz+fV2dKNgfXzcSLKvC+jEs1709t7nD+WdfjrdSaOcefpgc7BUJYadc3usaING+b7ediMTfKxuJBsEFbXA==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-mentions@1.13.1(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-FCkaWw6JQygtOz0+Vxz/M/NWqrWHB9LwqlY2RtcuFqWJNFK9njijOOzTSsBGANliGufVUzx/xuPHmZPBV0+Hgw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-menu: 9.8.4(react-dom@16.14.0)(react@16.14.0)
      rc-textarea: 0.4.7(react-dom@16.14.0)(react@16.14.0)
      rc-trigger: 5.3.4(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-menu@9.8.4(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-lmw2j8I2fhdIzHmC9ajfImfckt0WDb2KVJJBBRIsxPEw2kGkEfjLMUoB1NgiNT/Q5cC8PdjGOGQjHJIJMwyNMw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-motion: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-overflow: 1.3.2(react-dom@16.14.0)(react@16.14.0)
      rc-trigger: 5.3.4(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-motion@2.7.3(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-2xUvo8yGHdOHeQbdI8BtBsCIrWKchEmFEIskf0nmHtJsou+meLd/JE+vnvSX2JxcBrJtXY2LuBpxAOxrbY/wMQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-notification@4.6.1(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-NSmFYwrrdY3+un1GvDAJQw62Xi9LNMSsoQyo95tuaYrcad5Bn9gJUL8AREufRxSQAQnr64u3LtP3EUyLYT6bhw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-motion: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-overflow@1.3.2(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-nsUm78jkYAoPygDAcGZeC2VwIg/IBGSodtOY3pMof4W3M9qRJgqaDYm03ZayHlde3I6ipliAxbN0RUcGf5KOzw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-resize-observer: 1.2.1(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-pagination@3.2.0(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-5tIXjB670WwwcAJzAqp2J+cOBS9W3cH/WU1EiYwXljuZ4vtZXKlY2Idq8FZrnYBz8KhN3vwPo9CoV/SJS6SL1w==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-picker@2.7.3(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-LuuU4ECPYmfIQD3NXTdoiHTH9xnc6Cb69Ad62ggX34Dy60RJmrchNhj6Gjp0puf/l1oIhFKO190slGcHob6ALA==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      date-fns: 2.30.0
      dayjs: 1.11.13
      moment: 2.29.4
      rc-trigger: 5.3.4(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      shallowequal: 1.1.0
    dev: false

  /rc-picker@4.11.2(dayjs@1.11.13)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-Cwa3frWpefhESBF20HBJtvWx3q1hCrMxSUrzuuWMTGoZVPhQllGEp2IUfzo9jC5LKm4kJx7IrH8q/W/y9wClAw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      date-fns: '>= 2.x'
      dayjs: '>= 1.x'
      luxon: '>= 3.x'
      moment: '>= 2.x'
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    peerDependenciesMeta:
      date-fns:
        optional: true
      dayjs:
        optional: true
      luxon:
        optional: true
      moment:
        optional: true
    dependencies:
      '@babel/runtime': 7.25.6
      '@rc-component/trigger': 2.2.5(react-dom@16.14.0)(react@16.14.0)
      classnames: 2.5.1
      dayjs: 1.11.13
      rc-overflow: 1.3.2(react-dom@16.14.0)(react@16.14.0)
      rc-resize-observer: 1.4.0(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-progress@3.4.2(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-iAGhwWU+tsayP+Jkl9T4+6rHeQTG9kDz8JAHZk4XtQOcYN5fj9H34NXNEdRdZx94VUDHMqCb1yOIvi8eJRh67w==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-rate@2.9.2(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-SaiZFyN8pe0Fgphv8t3+kidlej+cq/EALkAJAc3A0w0XcPaH2L1aggM8bhe1u6GAGuQNAoFvTLjw4qLPGRKV5g==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-resize-observer@1.2.1(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-g53PnWLeVOmt4XWkt2x+QlIdf/PhJSd7JqHhtMrUY370e7wJ+kxbgXicYqvENUcgFiiOiMCd07YsC2GNsoSbnA==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      resize-observer-polyfill: 1.5.1
    dev: false

  /rc-resize-observer@1.4.0(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-PnMVyRid9JLxFavTjeDXEXo65HCRqbmLBw9xX9gfC4BZiSzbLXKzW3jPz+J0P71pLbD5tBMTT+mkstV5gD0c9Q==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      resize-observer-polyfill: 1.5.1
    dev: false

  /rc-segmented@2.1.2(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-qGo1bCr83ESXpXVOCXjFe1QJlCAQXyi9KCiy8eX3rIMYlTeJr/ftySIaTnYsitL18SvWf5ZEHsfqIWoX0EMfFQ==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-motion: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-select@14.1.18(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-4JgY3oG2Yz68ECMUSCON7mtxuJvCSj+LJpHEg/AONaaVBxIIrmI/ZTuMJkyojall/X50YdBe5oMKqHHPNiPzEg==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-motion: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-overflow: 1.3.2(react-dom@16.14.0)(react@16.14.0)
      rc-trigger: 5.3.4(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      rc-virtual-list: 3.10.5(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-slider@10.0.1(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-igTKF3zBet7oS/3yNiIlmU8KnZ45npmrmHlUUio8PNbIhzMcsh+oE/r2UD42Y6YD2D/s+kzCQkzQrPD6RY435Q==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      shallowequal: 1.1.0
    dev: false

  /rc-steps@5.0.0(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-9TgRvnVYirdhbV0C3syJFj9EhCRqoJAsxt4i1rED5o8/ZcSv5TLIYyo4H8MCjLPvbe2R+oBAm/IYBEtC+OS1Rw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-switch@3.2.2(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-+gUJClsZZzvAHGy1vZfnwySxj+MjLlGRyXKXScrtCTcmiYNPzxDFOxdQ/3pK1Kt/0POvwJ/6ALOR8gwdXGhs+A==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-table@7.26.0(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-0cD8e6S+DTGAt5nBZQIPFYEaIukn17sfa5uFL98faHlH/whZzD8ii3dbFL4wmUDEL4BLybhYop+QUfZJ4CPvNQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-resize-observer: 1.2.1(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      shallowequal: 1.1.0
    dev: false

  /rc-tabs@12.5.10(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-Ay0l0jtd4eXepFH9vWBvinBjqOpqzcsJTerBGwJy435P2S90Uu38q8U/mvc1sxUEVOXX5ZCFbxcWPnfG3dH+tQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-dropdown: 4.0.1(react-dom@16.14.0)(react@16.14.0)
      rc-menu: 9.8.4(react-dom@16.14.0)(react@16.14.0)
      rc-motion: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-resize-observer: 1.2.1(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-textarea@0.4.7(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-IQPd1CDI3mnMlkFyzt2O4gQ2lxUsnBAeJEoZGJnkkXgORNqyM9qovdrCj9NzcRfpHgLdzaEbU3AmobNFGUznwQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-resize-observer: 1.2.1(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      shallowequal: 1.1.0
    dev: false

  /rc-tooltip@5.2.2(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-jtQzU/18S6EI3lhSGoDYhPqNpWajMtS5VV/ld1LwyfrDByQpYmw/LW6U7oFXXLukjfDHQ7Ju705A82PRNFWYhg==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-trigger: 5.3.4(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-tree-select@5.5.5(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-k2av7jF6tW9bIO4mQhaVdV4kJ1c54oxV3/hHVU+oD251Gb5JN+m1RbJFTMf1o0rAFqkvto33rxMdpafaGKQRJw==}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-select: 14.1.18(react-dom@16.14.0)(react@16.14.0)
      rc-tree: 5.7.10(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-tree@5.7.10(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-n4UkMQY3bzvJUNnbw6e3YI7sy2kE9c9vAYbSt94qAhcPKtMOThONNr1LIaFB/M5XeFYYrWVbvRVoT8k38eFuSQ==}
    engines: {node: '>=10.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-motion: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      rc-virtual-list: 3.10.5(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-trigger@5.3.4(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-mQv+vas0TwKcjAO2izNPkqR4j86OemLRmvL2nOzdP9OWNWA1ivoTt5hzFqYNW9zACwmTezRiN8bttrC7cZzYSw==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-align: 4.0.15(react-dom@16.14.0)(react@16.14.0)
      rc-motion: 2.7.3(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-upload@4.3.4(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-uVbtHFGNjHG/RyAfm9fluXB6pvArAGyAx8z7XzXXyorEgVIWj6mOlriuDm0XowDHYz4ycNK0nE0oP3cbFnzxiQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /rc-util@4.21.1:
    resolution: {integrity: sha512-Z+vlkSQVc1l8O2UjR3WQ+XdWlhj5q9BMQNLk2iOBch75CqPfrJyGtcWMcnhRlNuDu0Ndtt4kLVO8JI8BrABobg==}
    dependencies:
      add-dom-event-listener: 1.1.0
      prop-types: 15.8.1
      react-is: 16.13.1
      react-lifecycles-compat: 3.0.4
      shallowequal: 1.1.0
    dev: false

  /rc-util@5.37.0(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-cPMV8DzaHI1KDaS7XPRXAf4J7mtBqjvjikLpQieaeOO7+cEbqY2j7Kso/T0R0OiEZTNcLS/8Zl9YrlXiO9UbjQ==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-is: 16.13.1
    dev: false

  /rc-util@5.43.0(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-AzC7KKOXFqAdIBqdGWepL9Xn7cm3vnAmjlHqUnoQaTMZYhM4VlXGLkkHHxj/BZ7Td0+SOPKB4RGPboBVKT9htw==}
    peerDependencies:
      react: '>=16.9.0'
      react-dom: '>=16.9.0'
    dependencies:
      '@babel/runtime': 7.25.6
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-is: 18.2.0
    dev: false

  /rc-virtual-list@3.10.5(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-Vc89TL3JHfRlLVQXVj5Hmv0dIflgwmHDcbjt9lrZjOG3wNUDkTF5zci8kFDU/CzdmmqgKu+CUktEpT10VUKYSQ==}
    engines: {node: '>=8.x'}
    peerDependencies:
      react: '*'
      react-dom: '*'
    dependencies:
      '@babel/runtime': 7.25.6
      classnames: 2.5.1
      rc-resize-observer: 1.2.1(react-dom@16.14.0)(react@16.14.0)
      rc-util: 5.43.0(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /react-codemirror2@7.3.0(codemirror@5.65.18)(react@16.14.0):
    resolution: {integrity: sha512-gCgJPXDX+5iaPolkHAu1YbJ92a2yL7Je4TuyO3QEqOtI/d6mbEk08l0oIm18R4ctuT/Sl87X63xIMBnRQBXYXA==}
    peerDependencies:
      codemirror: 5.x
      react: '>=15.5 <=17.x'
    dependencies:
      codemirror: 5.65.18
      react: 16.14.0
    dev: false

  /react-color@2.17.1(react@16.14.0):
    resolution: {integrity: sha512-S+I6TkUKJaqfALLkAIfiCZ/MANQyy7dKkf7g9ZU5GTUy2rf8c2Rx62otyvADAviWR+6HRkzdf2vL1Qvz9goCLQ==}
    peerDependencies:
      react: '*'
    dependencies:
      '@icons/material': 0.2.4(react@16.14.0)
      lodash: 4.17.21
      material-colors: 1.2.6
      prop-types: 15.8.1
      react: 16.14.0
      reactcss: 1.2.3(react@16.14.0)
      tinycolor2: 1.6.0
    dev: false

  /react-color@2.17.3(react@16.14.0):
    resolution: {integrity: sha512-1dtO8LqAVotPIChlmo6kLtFS1FP89ll8/OiA8EcFRDR+ntcK+0ukJgByuIQHRtzvigf26dV5HklnxDIvhON9VQ==}
    peerDependencies:
      react: '*'
    dependencies:
      '@icons/material': 0.2.4(react@16.14.0)
      lodash: 4.17.21
      material-colors: 1.2.6
      prop-types: 15.8.1
      react: 16.14.0
      reactcss: 1.2.3(react@16.14.0)
      tinycolor2: 1.6.0
    dev: false

  /react-color@2.19.3(react@16.14.0):
    resolution: {integrity: sha512-LEeGE/ZzNLIsFWa1TMe8y5VYqr7bibneWmvJwm1pCn/eNmrabWDh659JSPn9BuaMpEfU83WTOJfnCcjDZwNQTA==}
    peerDependencies:
      react: '*'
    dependencies:
      '@icons/material': 0.2.4(react@16.14.0)
      lodash: 4.17.21
      lodash-es: 4.17.21
      material-colors: 1.2.6
      prop-types: 15.8.1
      react: 16.14.0
      reactcss: 1.2.3(react@16.14.0)
      tinycolor2: 1.6.0
    dev: false

  /react-content-loader@5.1.4(react@16.14.0):
    resolution: {integrity: sha512-hTq7pZi2GKCK6a9d3u6XStozm0QGCEjw8cSqQReiWnh2up6IwCha5R5TF0o6SY5qUDpByloEZEZtnFxpJyENFw==}
    engines: {node: '>=10'}
    peerDependencies:
      react: '>=16.0.0'
    dependencies:
      react: 16.14.0
    dev: false

  /react-dat-gui@4.1.1(prop-types@15.8.1)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-FdCUoXuCjh/8wXMc4AiZncthUQzuqRrr/iCi+V6byL48yQ4Bo1JK2534Lygt40yztaYVilj/AOKPkQYC8BI9fg==}
    peerDependencies:
      prop-types: ^15.7.2
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      classnames: 2.2.6
      lodash.clamp: 4.0.3
      lodash.clonedeep: 4.5.0
      lodash.isfinite: 3.3.2
      lodash.isstring: 4.0.1
      lodash.isundefined: 3.0.1
      lodash.result: 4.5.2
      lodash.set: 4.3.2
      prop-types: 15.8.1
      react: 16.14.0
      react-color: 2.19.3(react@16.14.0)
      react-dom: 16.14.0(react@16.14.0)
      sass: 1.85.1
    dev: false

  /react-dnd-html5-backend@11.1.3:
    resolution: {integrity: sha512-/1FjNlJbW/ivkUxlxQd7o3trA5DE33QiRZgxent3zKme8DwF4Nbw3OFVhTRFGaYhHFNL1rZt6Rdj1D78BjnNLw==}
    dependencies:
      dnd-core: 11.1.3
    dev: false

  /react-dnd@11.1.3(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-8rtzzT8iwHgdSC89VktwhqdKKtfXaAyC4wiqp0SywpHG12TTLvfOoL6xNEIUWXwIEWu+CFfDn4GZJyynCEuHIQ==}
    peerDependencies:
      react: '>= 16.9.0'
      react-dom: '>= 16.9.0'
    dependencies:
      '@react-dnd/shallowequal': 2.0.0
      '@types/hoist-non-react-statics': 3.3.1
      dnd-core: 11.1.3
      hoist-non-react-statics: 3.3.2
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /react-dom@16.14.0(react@16.14.0):
    resolution: {integrity: sha512-1gCeQXDLoIqMgqD3IO2Ah9bnf0w9kzhwN5q4FGnHZ67hBm9yePzB5JJAIQCc8x3pFnNlwFq4RidZggNAAkzWWw==}
    peerDependencies:
      react: ^16.14.0
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      prop-types: 15.8.1
      react: 16.14.0
      scheduler: 0.19.1
    dev: false

  /react-draggable@4.4.5(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-OMHzJdyJbYTZo4uQE393fHcqqPYsEtkjfMgvCHr6rejT+Ezn4OZbNyGH50vv+SunC1RMvwOTSWkEODQLzw1M9g==}
    peerDependencies:
      react: '>= 16.3.0'
      react-dom: '>= 16.3.0'
    dependencies:
      clsx: 1.2.1
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /react-fast-compare@3.2.2:
    resolution: {integrity: sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ==}
    dev: false

  /react-fast-marquee@1.6.5(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-swDnPqrT2XISAih0o74zQVE2wQJFMvkx+9VZXYYNSLb/CUcAzU9pNj637Ar2+hyRw6b4tP6xh4GQZip2ZCpQpg==}
    peerDependencies:
      react: '>= 16.8.0 || ^18.0.0'
      react-dom: '>= 16.8.0 || ^18.0.0'
    dependencies:
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /react-grid-layout@1.5.0(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-WBKX7w/LsTfI99WskSu6nX2nbJAUD7GD6nIXcwYLyPpnslojtmql2oD3I2g5C3AK8hrxIarYT8awhuDIp7iQ5w==}
    peerDependencies:
      react: '>= 16.3.0'
      react-dom: '>= 16.3.0'
    dependencies:
      clsx: 2.1.1
      fast-equals: 4.0.3
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-draggable: 4.4.5(react-dom@16.14.0)(react@16.14.0)
      react-resizable: 3.0.5(react-dom@16.14.0)(react@16.14.0)
      resize-observer-polyfill: 1.5.1
    dev: false

  /react-i18next@15.4.1(i18next@23.16.8)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-ahGab+IaSgZmNPYXdV1n+OYky95TGpFwnKRflX/16dY04DsYYKHtVLjeny7sBSCREEcoMbAgSkFiGLF5g5Oofw==}
    peerDependencies:
      i18next: '>= 23.2.3'
      react: '>= 16.8.0'
      react-dom: '*'
      react-native: '*'
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true
    dependencies:
      '@babel/runtime': 7.25.6
      html-parse-stringify: 3.0.1
      i18next: 23.16.8
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  /react-is@17.0.2:
    resolution: {integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==}
    dev: false

  /react-is@18.2.0:
    resolution: {integrity: sha512-xWGDIW6x921xtzPkhiULtthJHoJvBbF3q26fzloPCK0hsvxtPVelvftw3zjbHWSkR2km9Z+4uxbDDK/6Zw9B8w==}

  /react-lifecycles-compat@3.0.4:
    resolution: {integrity: sha512-fBASbA6LnOU9dOU2eW7aQ8xmYBSXUIWr+UmF9b1efZBazGNO+rcXT/icdKnYm2pTwcRylVUYwW7H1PHfLekVzA==}
    dev: false

  /react-redux@7.2.9(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-Gx4L3uM182jEEayZfRbI/G11ZpYdNAnBs70lFVMNdHJI76XYtR+7m0MN+eAs7UHBPhWXcnFPaS+9owSCJQHNpQ==}
    peerDependencies:
      react: ^16.8.3 || ^17 || ^18
      react-dom: '*'
      react-native: '*'
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true
    dependencies:
      '@babel/runtime': 7.25.6
      '@types/react-redux': 7.1.34
      hoist-non-react-statics: 3.3.2
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-is: 17.0.2
    dev: false

  /react-refresh@0.16.0:
    resolution: {integrity: sha512-FPvF2XxTSikpJxcr+bHut2H4gJ17+18Uy20D5/F+SKzFap62R3cM5wH6b8WN3LyGSYeQilLEcJcR1fjBSI2S1A==}
    engines: {node: '>=0.10.0'}
    dev: true

  /react-resizable@1.11.1(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-S70gbLaAYqjuAd49utRHibtHLrHXInh7GuOR+6OO6RO6uleQfuBnWmZjRABfqNEx3C3Z6VPLg0/0uOYFrkfu9Q==}
    peerDependencies:
      react: 0.14.x || 15.x || 16.x || 17.x
      react-dom: 0.14.x || 15.x || 16.x || 17.x
    dependencies:
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-draggable: 4.4.5(react-dom@16.14.0)(react@16.14.0)
    dev: false

  /react-resizable@3.0.5(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-vKpeHhI5OZvYn82kXOs1bC8aOXktGU5AmKAgaZS4F5JPburCtbmDPqE7Pzp+1kN4+Wb81LlF33VpGwWwtXem+w==}
    peerDependencies:
      react: '>= 16.3'
    dependencies:
      prop-types: 15.8.1
      react: 16.14.0
      react-draggable: 4.4.5(react-dom@16.14.0)(react@16.14.0)
    transitivePeerDependencies:
      - react-dom
    dev: false

  /react-resize-detector@7.1.2(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-zXnPJ2m8+6oq9Nn8zsep/orts9vQv3elrpA+R8XTcW7DVVUJ9vwDwMXaBtykAYjMnkCIaOoK9vObyR7ZgFNlOw==}
    peerDependencies:
      react: ^16.0.0 || ^17.0.0 || ^18.0.0
      react-dom: ^16.0.0 || ^17.0.0 || ^18.0.0
    dependencies:
      lodash: 4.17.21
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /react-router-dom@5.3.4(react@16.14.0):
    resolution: {integrity: sha512-m4EqFMHv/Ih4kpcBCONHbkT68KoAeHN4p3lAGoNryfHi0dMy0kCzEZakiKRsvg5wHZ/JLrLW8o8KomWiz/qbYQ==}
    peerDependencies:
      react: '>=15'
    dependencies:
      '@babel/runtime': 7.25.6
      history: 4.10.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 16.14.0
      react-router: 5.3.4(react@16.14.0)
      tiny-invariant: 1.3.1
      tiny-warning: 1.0.3
    dev: false

  /react-router@5.3.4(react@16.14.0):
    resolution: {integrity: sha512-Ys9K+ppnJah3QuaRiLxk+jDWOR1MekYQrlytiXxC1RyfbdsZkS5pvKAzCCr031xHixZwpnsYNT5xysdFHQaYsA==}
    peerDependencies:
      react: '>=15'
    dependencies:
      '@babel/runtime': 7.25.6
      history: 4.10.1
      hoist-non-react-statics: 3.3.2
      loose-envify: 1.4.0
      path-to-regexp: 1.9.0
      prop-types: 15.8.1
      react: 16.14.0
      react-is: 16.13.1
      tiny-invariant: 1.3.1
      tiny-warning: 1.0.3
    dev: false

  /react-slick@0.28.1(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-JwRQXoWGJRbUTE7eZI1rGIHaXX/4YuwX6gn7ulfvUZ4vFDVQAA25HcsHSYaUiRCduTr6rskyIuyPMpuG6bbluw==}
    peerDependencies:
      react: ^0.14.0 || ^15.0.1 || ^16.0.0 || ^17.0.0
      react-dom: ^0.14.0 || ^15.0.1 || ^16.0.0 || ^17.0.0
    dependencies:
      classnames: 2.5.1
      enquire.js: 2.1.6
      json2mq: 0.2.0
      lodash.debounce: 4.0.8
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      resize-observer-polyfill: 1.5.1
    dev: false

  /react-universal-interface@0.6.2(react@16.14.0)(tslib@2.8.1):
    resolution: {integrity: sha512-dg8yXdcQmvgR13RIlZbTRQOoUrDciFVoSBZILwjE2LFISxZZ8loVJKAkuzswl5js8BHda79bIb2b84ehU8IjXw==}
    peerDependencies:
      react: '*'
      tslib: '*'
    dependencies:
      react: 16.14.0
      tslib: 2.8.1
    dev: false

  /react-use@17.3.1(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-hs7+tS4rRm1QLHPfanLCqXIi632tP4V7Sai1ENUP2WTufU6am++tU9uSw9YrNCFqbABiEv0ndKU1XCUcfu2tXA==}
    peerDependencies:
      react: ^16.8.0  || ^17.0.0
      react-dom: ^16.8.0  || ^17.0.0
    dependencies:
      '@types/js-cookie': 2.2.7
      '@xobotyi/scrollbar-width': 1.9.5
      copy-to-clipboard: 3.3.3
      fast-deep-equal: 3.1.3
      fast-shallow-equal: 1.0.0
      js-cookie: 2.2.1
      nano-css: 5.3.5(react-dom@16.14.0)(react@16.14.0)
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
      react-universal-interface: 0.6.2(react@16.14.0)(tslib@2.8.1)
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
      set-harmonic-interval: 1.0.1
      throttle-debounce: 3.0.1
      ts-easing: 0.2.0
      tslib: 2.8.1
    dev: false

  /react-visibility-sensor@5.1.1(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-cTUHqIK+zDYpeK19rzW6zF9YfT4486TIgizZW53wEZ+/GPBbK7cNS0EHyJVyHYacwFEvvHLEKfgJndbemWhB/w==}
    peerDependencies:
      react: '>=16.0.0'
      react-dom: '>=16.0.0'
    dependencies:
      prop-types: 15.8.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /react-window@1.8.11(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-+SRbUVT2scadgFSWx+R1P754xHPEqvcfSfVX10QYg6POOz+WNgkN48pS+BtZNIMGiL1HYrSEiCkwsMS15QogEQ==}
    engines: {node: '>8.0.0'}
    peerDependencies:
      react: ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      '@babel/runtime': 7.25.6
      memoize-one: 5.2.1
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    dev: false

  /react@16.14.0:
    resolution: {integrity: sha512-0X2CImDkJGApiAlcf0ODKIneSwBPhqJawOa5wCtKbu7ZECrmS26NvtSILynQ66cgkT/RJ4LidJOc3bUESwmU8g==}
    engines: {node: '>=0.10.0'}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      prop-types: 15.8.1
    dev: false

  /reactcss@1.2.3(react@16.14.0):
    resolution: {integrity: sha512-KiwVUcFu1RErkI97ywr8nvx8dNOpT03rbnma0SSalTYjkrPYaEajR4a/MRt6DZ46K6arDRbWMNHF+xH7G7n/8A==}
    peerDependencies:
      react: '*'
    dependencies:
      lodash: 4.17.21
      react: 16.14.0
    dev: false

  /read-cache@1.0.0:
    resolution: {integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==}
    dependencies:
      pify: 2.3.0
    dev: true

  /readable-stream@1.1.14:
    resolution: {integrity: sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 0.0.1
      string_decoder: 0.10.31
    dev: false

  /readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2
    dev: true

  /readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  /readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: 2.3.1
    dev: true

  /readdirp@4.0.2:
    resolution: {integrity: sha512-yDMz9g+VaZkqBYS/ozoBJwaBhTbZo3UNYQHNRw1D3UFQB8oHB4uS/tAODO+ZLjGWmUbKnIlOWO+aaIiAxrUWHA==}
    engines: {node: '>= 14.16.0'}

  /reduce-configs@1.1.0:
    resolution: {integrity: sha512-DQxy6liNadHfrLahZR7lMdc227NYVaQZhY5FMsxLEjX8X0SCuH+ESHSLCoz2yDZFq1/CLMDOAHdsEHwOEXKtvg==}
    dev: true

  /redux-thunk@2.4.2(redux@4.2.1):
    resolution: {integrity: sha512-+P3TjtnP0k/FEjcBL5FZpoovtvrTNT/UXd4/sluaSyrURlSlhLSzEdfsTBW7WsKB6yPvgd7q/iZPICFjW4o57Q==}
    peerDependencies:
      redux: ^4
    dependencies:
      redux: 4.2.1
    dev: false

  /redux@4.2.1:
    resolution: {integrity: sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==}
    dependencies:
      '@babel/runtime': 7.25.6

  /reflect-metadata@0.1.13:
    resolution: {integrity: sha512-Ts1Y/anZELhSsjMcU605fU9RE4Oi3p5ORujwbIKXfWa+0Zxs510Qrmrce5/Jowq3cHSZSJqBjypxmHarc+vEWg==}
    dev: false

  /reflect.getprototypeof@1.0.10:
    resolution: {integrity: sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      which-builtin-type: 1.2.1
    dev: true

  /regenerator-runtime@0.11.1:
    resolution: {integrity: sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==}
    dev: false

  /regenerator-runtime@0.14.0:
    resolution: {integrity: sha512-srw17NI0TUWHuGa5CFGGmhfNIeja30WMBfbslPNhf6JrqQlLN5gcrvig1oqPxiVaXb0oW0XRKtH6Nngs5lKCIA==}

  /regexp.prototype.flags@1.5.0:
    resolution: {integrity: sha512-0SutC3pNudRKgquxGoRGIz946MZVHqbNfPjBdxeOhBrdgDKlRoXmYLQN9xRbrR09ZXWeGAdPuif7egofn6v5LA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      functions-have-names: 1.2.3

  /regexp.prototype.flags@1.5.4:
    resolution: {integrity: sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2

  /regexpp@2.0.1:
    resolution: {integrity: sha512-lv0M6+TkDVniA3aD1Eg0DVpfU/booSu7Eev3TDO/mZKHBfVjgCGTV4t4buppESEYDtkArYFOxTJWv6S5C+iaNw==}
    engines: {node: '>=6.5.0'}
    dev: true

  /regl@1.6.1:
    resolution: {integrity: sha512-7Z9rmpEqmLNwC9kCYCyfyu47eWZaQWeNpwZfwz99QueXN8B/Ow40DB0N+OeUeM/yu9pZAB01+JgJ+XghGveVoA==}
    dev: false

  /regl@1.7.0:
    resolution: {integrity: sha512-bEAtp/qrtKucxXSJkD4ebopFZYP0q1+3Vb2WECWv/T8yQEgKxDxJ7ztO285tAMaYZVR6mM1GgI6CCn8FROtL1w==}
    dev: false

  /repeat-string@1.6.1:
    resolution: {integrity: sha512-PV0dzCYDNfRi1jCDbJzpW7jNNDRuCOG/jI5ctQcGKt/clZD+YcPS3yIlWuTJMmESC8aevCFmWJy5wjAFgNqN6w==}
    engines: {node: '>=0.10'}
    dev: false

  /resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==}
    dev: false

  /resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}
    dev: true

  /resolve-pathname@3.0.0:
    resolution: {integrity: sha512-C7rARubxI8bXFNB/hqcp/4iUeIXJhJZvFPFPiSPRnhU5UPxzMFIl+2E6yY6c4k9giDJAhtV+enfA+G89N6Csng==}
    dev: false

  /resolve-protobuf-schema@2.1.0:
    resolution: {integrity: sha512-kI5ffTiZWmJaS/huM8wZfEMer1eRd7oJQhDuxeCLe3t7N7mX3z94CN0xPxBQxFYQTSNz9T0i+v6inKqSdK8xrQ==}
    dependencies:
      protocol-buffers-schema: 3.6.0
    dev: false

  /resolve@1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==}
    engines: {node: '>= 0.4'}
    hasBin: true
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0
    dev: true

  /resolve@1.22.4:
    resolution: {integrity: sha512-PXNdCiPqDqeUou+w1C2eTQbNfxKSuMxqTCuvlmmMsk1NWHL5fRrhY6Pl0qEYYc6+QqGClco1Qj8XnjPego4wfg==}
    hasBin: true
    dependencies:
      is-core-module: 2.13.0
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  /restore-cursor@3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    engines: {node: '>=8'}
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7
    dev: true

  /resumer@0.0.0:
    resolution: {integrity: sha512-Fn9X8rX8yYF4m81rZCK/5VmrmsSbqS/i3rDLl6ZZHAXgC2nTAx3dhwG8q8odP/RmdLa2YrybDJaAMg+X1ajY3w==}
    dependencies:
      through: 2.3.8
    dev: false

  /reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  /rfdc@1.3.0:
    resolution: {integrity: sha512-V2hovdzFbOi77/WajaSMXk2OLm+xNIeQdMMuB7icj7bk6zi2F8GGAxigcnDFpJHbNyNcgyJDiP+8nOrY5cZGrA==}
    dev: true

  /right-align@0.1.3:
    resolution: {integrity: sha512-yqINtL/G7vs2v+dFIZmFUDbnVyFUJFKd6gK22Kgo6R4jfJGFtisKyncWDDULgjfqf4ASQuIQyjJ7XZ+3aWpsAg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      align-text: 0.1.4
    dev: false

  /rimraf@2.6.3:
    resolution: {integrity: sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true
    dependencies:
      glob: 7.2.3
    dev: true

  /ripemd160@2.0.2:
    resolution: {integrity: sha512-ii4iagi25WusVoiC4B4lq7pbXfAp3D9v5CwfkY33vffw2+pkDjY1D8GaN7spsxvCSx8dkPqOZCEZyfxcmJG2IA==}
    dependencies:
      hash-base: 3.0.5
      inherits: 2.0.4
    dev: true

  /robust-predicates@2.0.4:
    resolution: {integrity: sha512-l4NwboJM74Ilm4VKfbAtFeGq7aEjWL+5kVFcmgFA2MrdnQWx9iE/tUGvxY5HyMI7o/WpSIUFLbC5fbeaHgSCYg==}
    dev: false

  /rollup@0.25.8:
    resolution: {integrity: sha512-a2S4Bh3bgrdO4BhKr2E4nZkjTvrJ2m2bWjMTzVYtoqSCn0HnuxosXnaJUHrMEziOWr3CzL9GjilQQKcyCQpJoA==}
    hasBin: true
    dependencies:
      chalk: 1.1.3
      minimist: 1.2.8
      source-map-support: 0.3.3
    dev: false

  /rsbuild-cli@0.0.0:
    resolution: {integrity: sha512-U+yk30O6JXXivMS2zOo5wZQ+/JEFF2S0jlwWT+8J2DBszD/DyPJKxORhnZ9qcUackTmZf1ac8DrcHNw5n1w+8A==}
    dev: true

  /rtl-css-js@1.16.1:
    resolution: {integrity: sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==}
    dependencies:
      '@babel/runtime': 7.25.6
    dev: false

  /run-async@2.4.1:
    resolution: {integrity: sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==}
    engines: {node: '>=0.12.0'}
    dev: true

  /run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}
    dependencies:
      queue-microtask: 1.2.3
    dev: true

  /rw@1.3.3:
    resolution: {integrity: sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ==}
    dev: false

  /rxjs@6.6.7:
    resolution: {integrity: sha512-hTdwr+7yYNIT5n4AMYp85KA6yw2Va0FLa3Rguvbpa4W3I5xynaBZo41cM3XM+4Q6fRMj3sBYIR1VAmZMXYJvRQ==}
    engines: {npm: '>=2.0.0'}
    dependencies:
      tslib: 1.14.1

  /rxjs@7.8.1:
    resolution: {integrity: sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==}
    dependencies:
      tslib: 2.8.1
    dev: true

  /safe-array-concat@1.0.0:
    resolution: {integrity: sha512-9dVEFruWIsnie89yym+xWTAYASdpw3CJV7Li/6zBewGf9z2i1j31rP6jnY0pHEO4QZh6N0K11bFjWmdR8UGdPQ==}
    engines: {node: '>=0.4'}
    dependencies:
      call-bind: 1.0.8
      get-intrinsic: 1.2.1
      has-symbols: 1.1.0
      isarray: 2.0.5

  /safe-array-concat@1.1.3:
    resolution: {integrity: sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==}
    engines: {node: '>=0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      isarray: 2.0.5
    dev: true

  /safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}
    dev: true

  /safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  /safe-push-apply@1.0.0:
    resolution: {integrity: sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5
    dev: true

  /safe-regex-test@1.0.0:
    resolution: {integrity: sha512-JBUUzyOgEwXQY1NuPtvcj/qcBDbDmEvWufhlnXZIm75DEHp+afM1r1ujJpJsV/gSM4t59tpDyPi1sd6ZaPFfsA==}
    dependencies:
      call-bind: 1.0.8
      get-intrinsic: 1.2.1
      is-regex: 1.2.1

  /safe-regex-test@1.1.0:
    resolution: {integrity: sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-regex: 1.2.1
    dev: true

  /safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  /sass-embedded-android-arm64@1.85.1:
    resolution: {integrity: sha512-27oRheqNA3SJM2hAxpVbs7mCKUwKPWmEEhyiNFpBINb5ELVLg+Ck5RsGg+SJmo130ul5YX0vinmVB5uPWc8X5w==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-android-arm@1.85.1:
    resolution: {integrity: sha512-GkcgUGMZtEF9gheuE1dxCU0ZSAifuaFXi/aX7ZXvjtdwmTl9Zc/OHR9oiUJkc8IW9UI7H8TuwlTAA8+SwgwIeQ==}
    engines: {node: '>=14.0.0'}
    cpu: [arm]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-android-ia32@1.85.1:
    resolution: {integrity: sha512-f3x16NyRgtXFksIaO/xXKrUhttUBv8V0XsAR2Dhdb/yz4yrDrhzw9Wh8fmw7PlQqECcQvFaoDr3XIIM6lKzasw==}
    engines: {node: '>=14.0.0'}
    cpu: [ia32]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-android-riscv64@1.85.1:
    resolution: {integrity: sha512-IP6OijpJ8Mqo7XqCe0LsuZVbAxEFVboa0kXqqR5K55LebEplsTIA2GnmRyMay3Yr/2FVGsZbCb6Wlgkw23eCiA==}
    engines: {node: '>=14.0.0'}
    cpu: [riscv64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-android-x64@1.85.1:
    resolution: {integrity: sha512-Mh7CA53wR3ADvXAYipFc/R3vV4PVOzoKwWzPxmq+7i8UZrtsVjKONxGtqWe9JG1mna0C9CRZAx0sv/BzbOJxWg==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [android]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-darwin-arm64@1.85.1:
    resolution: {integrity: sha512-msWxzhvcP9hqGVegxVePVEfv9mVNTlUgGr6k7O7Ihji702mbtrH/lKwF4aRkkt4g1j7tv10+JtQXmTNi/pi9kA==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-darwin-x64@1.85.1:
    resolution: {integrity: sha512-J4UFHUiyI9Z+mwYMwz11Ky9TYr3hY1fCxeQddjNGL/+ovldtb0yAIHvoVM0BGprQDm5JqhtUk8KyJ3RMJqpaAA==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-linux-arm64@1.85.1:
    resolution: {integrity: sha512-jGadetB03BMFG2rq3OXub/uvC/lGpbQOiLGEz3NLb2nRZWyauRhzDtvZqkr6BEhxgIWtMtz2020yD8ZJSw/r2w==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-linux-arm@1.85.1:
    resolution: {integrity: sha512-X0fDh95nNSw1wfRlnkE4oscoEA5Au4nnk785s9jghPFkTBg+A+5uB6trCjf0fM22+Iw6kiP4YYmDdw3BqxAKLQ==}
    engines: {node: '>=14.0.0'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-linux-ia32@1.85.1:
    resolution: {integrity: sha512-7HlYY90d9mitDtNi5s+S+5wYZrTVbkBH2/kf7ixrzh2BFfT0YM81UHLJRnGX93y9aOMBL6DSZAIfkt1RsV9bkQ==}
    engines: {node: '>=14.0.0'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-linux-musl-arm64@1.85.1:
    resolution: {integrity: sha512-FLkIT0p18XOkR6wryJ13LqGBDsrYev2dRk9dtiU18NCpNXruKsdBQ1ZnWHVKB3h1dA9lFyEEisC0sooKdNfeOQ==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-linux-musl-arm@1.85.1:
    resolution: {integrity: sha512-5vcdEqE8QZnu6i6shZo7x2N36V7YUoFotWj2rGekII5ty7Nkaj+VtZhUEOp9tAzEOlaFuDp5CyO1kUCvweT64A==}
    engines: {node: '>=14.0.0'}
    cpu: [arm]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-linux-musl-ia32@1.85.1:
    resolution: {integrity: sha512-N1093T84zQJor1yyIAdYScB5eAuQarGK1tKgZ4uTnxVlgA7Xi1lXV8Eh7ox9sDqKCaWkVQ3MjqU26vYRBeRWyw==}
    engines: {node: '>=14.0.0'}
    cpu: [ia32]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-linux-musl-riscv64@1.85.1:
    resolution: {integrity: sha512-WRsZS/7qlfYXsa93FBpSruieuURIu7ySfFhzYfF1IbKrNAGwmbduutkHZh2ddm5/vQMvQ0Rdosgv+CslaQHMcw==}
    engines: {node: '>=14.0.0'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-linux-musl-x64@1.85.1:
    resolution: {integrity: sha512-+OlLIilA5TnP0YEqTQ8yZtkW+bJIQYvzoGoNLUEskeyeGuOiIyn2CwL6G4JQB4xZQFaxPHb7JD3EueFkQbH0Pw==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-linux-riscv64@1.85.1:
    resolution: {integrity: sha512-mKKlOwMGLN7yP1p0gB5yG/HX4fYLnpWaqstNuOOXH+fOzTaNg0+1hALg0H0CDIqypPO74M5MS9T6FAJZGdT6dQ==}
    engines: {node: '>=14.0.0'}
    cpu: [riscv64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-linux-x64@1.85.1:
    resolution: {integrity: sha512-uKRTv0z8NgtHV7xSren78+yoWB79sNi7TMqI7Bxd8fcRNIgHQSA8QBdF8led2ETC004hr8h71BrY60RPO+SSvA==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [linux]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-win32-arm64@1.85.1:
    resolution: {integrity: sha512-/GMiZXBOc6AEMBC3g25Rp+x8fq9Z6Ql7037l5rajBPhZ+DdFwtdHY0Ou3oIU6XuWUwD06U3ii4XufXVFhsP6PA==}
    engines: {node: '>=14.0.0'}
    cpu: [arm64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-win32-ia32@1.85.1:
    resolution: {integrity: sha512-L+4BWkKKBGFOKVQ2PQ5HwFfkM5FvTf1Xx2VSRvEWt9HxPXp6SPDho6zC8fqNQ3hSjoaoASEIJcSvgfdQYO0gdg==}
    engines: {node: '>=14.0.0'}
    cpu: [ia32]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded-win32-x64@1.85.1:
    resolution: {integrity: sha512-/FO0AGKWxVfCk4GKsC0yXWBpUZdySe3YAAbQQL0lL6xUd1OiUY8Kow6g4Kc1TB/+z0iuQKKTqI/acJMEYl4iTQ==}
    engines: {node: '>=14.0.0'}
    cpu: [x64]
    os: [win32]
    requiresBuild: true
    dev: true
    optional: true

  /sass-embedded@1.85.1:
    resolution: {integrity: sha512-0i+3h2Df/c71afluxC1SXqyyMmJlnKWfu9ZGdzwuKRM1OftEa2XM2myt5tR36CF3PanYrMjFKtRIj8PfSf838w==}
    engines: {node: '>=16.0.0'}
    hasBin: true
    dependencies:
      '@bufbuild/protobuf': 2.2.2
      buffer-builder: 0.2.0
      colorjs.io: 0.5.2
      immutable: 5.0.3
      rxjs: 7.8.1
      supports-color: 8.1.1
      sync-child-process: 1.0.2
      varint: 6.0.0
    optionalDependencies:
      sass-embedded-android-arm: 1.85.1
      sass-embedded-android-arm64: 1.85.1
      sass-embedded-android-ia32: 1.85.1
      sass-embedded-android-riscv64: 1.85.1
      sass-embedded-android-x64: 1.85.1
      sass-embedded-darwin-arm64: 1.85.1
      sass-embedded-darwin-x64: 1.85.1
      sass-embedded-linux-arm: 1.85.1
      sass-embedded-linux-arm64: 1.85.1
      sass-embedded-linux-ia32: 1.85.1
      sass-embedded-linux-musl-arm: 1.85.1
      sass-embedded-linux-musl-arm64: 1.85.1
      sass-embedded-linux-musl-ia32: 1.85.1
      sass-embedded-linux-musl-riscv64: 1.85.1
      sass-embedded-linux-musl-x64: 1.85.1
      sass-embedded-linux-riscv64: 1.85.1
      sass-embedded-linux-x64: 1.85.1
      sass-embedded-win32-arm64: 1.85.1
      sass-embedded-win32-ia32: 1.85.1
      sass-embedded-win32-x64: 1.85.1
    dev: true

  /sass-loader@16.0.4(@rspack/core@1.2.8)(sass@1.85.1):
    resolution: {integrity: sha512-LavLbgbBGUt3wCiYzhuLLu65+fWXaXLmq7YxivLhEqmiupCFZ5sKUAipK3do6V80YSU0jvSxNhEdT13IXNr3rg==}
    engines: {node: '>= 18.12.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      node-sass: ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
      sass: ^1.3.0
      sass-embedded: '*'
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      node-sass:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      webpack:
        optional: true
    dependencies:
      '@rspack/core': 1.2.8
      neo-async: 2.6.2
      sass: 1.85.1
    dev: true

  /sass-resources-loader@2.2.5:
    resolution: {integrity: sha512-po8rfETH9cOQACWxubT/1CCu77KjxwRtCDm6QAXZH99aUHBydwSoxdIjC40SGp/dcS/FkSNJl0j1VEojGZqlvQ==}
    dependencies:
      async: 3.2.4
      chalk: 4.1.2
      glob: 7.2.3
      loader-utils: 2.0.4
    dev: true

  /sass@1.85.1:
    resolution: {integrity: sha512-Uk8WpxM5v+0cMR0XjX9KfRIacmSG86RH4DCCZjLU2rFh5tyutt9siAXJ7G+YfxQ99Q6wrRMbMlVl6KqUms71ag==}
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      chokidar: 4.0.1
      immutable: 5.0.3
      source-map-js: 1.2.1
    optionalDependencies:
      '@parcel/watcher': 2.5.0

  /scheduler@0.19.1:
    resolution: {integrity: sha512-n/zwRWRYSUj0/3g/otKDRPMh6qv2SYMWNq85IEa8iZyAv8od9zDYpGSnpBEjNgcMNq6Scbu5KfIPxNF72R/2EA==}
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
    dev: false

  /screenfull@5.2.0:
    resolution: {integrity: sha512-9BakfsO2aUQN2K9Fdbj87RJIEZ82Q9IGim7FqM5OsebfoFC6ZHXgDq/KvniuLTPdeM8wY2o6Dj3WQ7KeQCj3cA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /scroll-into-view-if-needed@2.2.31:
    resolution: {integrity: sha512-dGCXy99wZQivjmjIqihaBQNjryrz5rueJY7eHfTdyWEiR4ttYpsajb14rn9s5d4DY4EcY6+4+U/maARBXJedkA==}
    dependencies:
      compute-scroll-into-view: 1.0.20
    dev: false

  /semver-compare@1.0.0:
    resolution: {integrity: sha512-YM3/ITh2MJ5MtzaM429anh+x2jiLVjqILF4m4oyQB18W7Ggea7BfqdH/wGMK7dDiMghv/6WG7znWMwUDzJiXow==}
    dev: true

  /semver-regex@3.1.4:
    resolution: {integrity: sha512-6IiqeZNgq01qGf0TId0t3NvKzSvUsjcpdEO3AQNeIjR6A2+ckTnQlDpl4qu1bjRv0RzN3FP9hzFmws3lKqRWkA==}
    engines: {node: '>=8'}
    dev: true

  /semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true
    dev: true

  /semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  /semver@7.5.4:
    resolution: {integrity: sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==}
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      lru-cache: 6.0.0
    dev: true

  /set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.6
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  /set-function-name@2.0.2:
    resolution: {integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  /set-harmonic-interval@1.0.1:
    resolution: {integrity: sha512-AhICkFV84tBP1aWqPwLZqFvAwqEoVA9kxNMniGEUvzOlm4vLmOFLiTT3UZ6bziJTy4bOVpzWGTfSCbmaayGx8g==}
    engines: {node: '>=6.9'}
    dev: false

  /set-proto@1.0.0:
    resolution: {integrity: sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==}
    engines: {node: '>= 0.4'}
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
    dev: true

  /set-value@2.0.1:
    resolution: {integrity: sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 2.0.1
      is-extendable: 0.1.1
      is-plain-object: 2.0.4
      split-string: 3.1.0
    dev: false

  /setimmediate@1.0.5:
    resolution: {integrity: sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==}

  /sha.js@2.4.11:
    resolution: {integrity: sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==}
    hasBin: true
    dependencies:
      inherits: 2.0.4
      safe-buffer: 5.2.1
    dev: true

  /shallowequal@1.1.0:
    resolution: {integrity: sha512-y0m1JoUZSlPAjXVtPPW70aZWfIL/dSP7AFkRnniLCrK/8MDKog3TySTBmckD+RObVxH0v4Tox67+F14PdED2oQ==}
    dev: false

  /shapefile@0.6.6:
    resolution: {integrity: sha512-rLGSWeK2ufzCVx05wYd+xrWnOOdSV7xNUW5/XFgx3Bc02hBkpMlrd2F1dDII7/jhWzv0MSyBFh5uJIy9hLdfuw==}
    hasBin: true
    dependencies:
      array-source: 0.0.4
      commander: 2.20.3
      path-source: 0.1.3
      slice-source: 0.4.1
      stream-source: 0.3.5
      text-encoding: 0.6.4
    dev: false

  /shebang-command@1.2.0:
    resolution: {integrity: sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      shebang-regex: 1.0.0
    dev: true

  /shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: 3.0.0
    dev: true

  /shebang-regex@1.0.0:
    resolution: {integrity: sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==}
    engines: {node: '>=0.10.0'}
    dev: true

  /shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}
    dev: true

  /side-channel-list@1.0.0:
    resolution: {integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.3
    dev: true

  /side-channel-map@1.0.1:
    resolution: {integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.6
      object-inspect: 1.13.3
    dev: true

  /side-channel-weakmap@1.0.2:
    resolution: {integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.6
      object-inspect: 1.13.3
      side-channel-map: 1.0.1
    dev: true

  /side-channel@1.0.4:
    resolution: {integrity: sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==}
    dependencies:
      call-bind: 1.0.8
      get-intrinsic: 1.2.1
      object-inspect: 1.12.3

  /side-channel@1.1.0:
    resolution: {integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==}
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.3
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2
    dev: true

  /signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}
    dev: true

  /signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}
    dev: true

  /simple-swizzle@0.2.2:
    resolution: {integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==}
    dependencies:
      is-arrayish: 0.3.2
    dev: false

  /simplify-geojson@1.0.5:
    resolution: {integrity: sha512-02l1W4UipP5ivNVq6kX15mAzCRIV1oI3tz0FUEyOsNiv1ltuFDjbNhO+nbv/xhbDEtKqWLYuzpWhUsJrjR/ypA==}
    hasBin: true
    dependencies:
      concat-stream: 1.4.11
      minimist: 1.2.6
      simplify-geometry: 0.0.2
    dev: false

  /simplify-geometry@0.0.2:
    resolution: {integrity: sha512-ZEyrplkqgCqDlL7V8GbbYgTLlcnNF+MWWUdy8s8ZeJru50bnI71rDew/I+HG36QS2mPOYAq1ZjwNXxHJ8XOVBw==}
    dev: false

  /size-sensor@1.0.1:
    resolution: {integrity: sha512-QTy7MnuugCFXIedXRpUSk9gUnyNiaxIdxGfUjr8xxXOqIB3QvBUYP9+b51oCg2C4dnhaeNk/h57TxjbvoJrJUA==}
    dev: false

  /skmeans@0.9.7:
    resolution: {integrity: sha512-hNj1/oZ7ygsfmPZ7ZfN5MUBRoGg1gtpnImuJBgLO0ljQ67DtJuiQaiYdS4lUA6s0KCwnPhGivtC/WRwIZLkHyg==}
    dev: false

  /slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}
    dev: true

  /slice-ansi@2.1.0:
    resolution: {integrity: sha512-Qu+VC3EwYLldKa1fCxuuvULvSJOKEgk9pi8dZeCVK7TqBfUNTH4sFkk4joj8afVSfAYgJoSOetjx9QWOJ5mYoQ==}
    engines: {node: '>=6'}
    dependencies:
      ansi-styles: 3.2.1
      astral-regex: 1.0.0
      is-fullwidth-code-point: 2.0.0
    dev: true

  /slice-ansi@3.0.0:
    resolution: {integrity: sha512-pSyv7bSTC7ig9Dcgbw9AuRNUb5k5V6oDudjZoMBSr13qpLBG7tB+zgCkARjq7xIUgdz5P1Qe8u+rSGdouOOIyQ==}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0
    dev: true

  /slice-ansi@4.0.0:
    resolution: {integrity: sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      astral-regex: 2.0.0
      is-fullwidth-code-point: 3.0.0
    dev: true

  /slice-source@0.4.1:
    resolution: {integrity: sha512-YiuPbxpCj4hD9Qs06hGAz/OZhQ0eDuALN0lRWJez0eD/RevzKqGdUx1IOMUnXgpr+sXZLq3g8ERwbAH0bCb8vg==}
    dev: false

  /slick-carousel@1.8.1:
    resolution: {integrity: sha512-XB9Ftrf2EEKfzoQXt3Nitrt/IPbT+f1fgqBdoxO3W/+JYvtEOW6EgxnWfr9GH6nmULv7Y2tPmEX3koxThVmebA==}
    peerDependencies:
      jquery: '>=1.8.0'
    dev: false

  /sort-asc@0.2.0:
    resolution: {integrity: sha512-umMGhjPeHAI6YjABoSTrFp2zaBtXBej1a0yKkuMUyjjqu6FJsTF+JYwCswWDg+zJfk/5npWUUbd33HH/WLzpaA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /sort-desc@0.2.0:
    resolution: {integrity: sha512-NqZqyvL4VPW+RAxxXnB8gvE1kyikh8+pR+T+CXLksVRN9eiQqkQlPwqWYU0mF9Jm7UnctShlxLyAt1CaBOTL1w==}
    engines: {node: '>=0.10.0'}
    dev: false

  /sort-object@3.0.3:
    resolution: {integrity: sha512-nK7WOY8jik6zaG9CRwZTaD5O7ETWDLZYMM12pqY8htll+7dYeqGfEUPcUBHOpSJg2vJOrvFIY2Dl5cX2ih1hAQ==}
    engines: {node: '>=0.10.0'}
    dependencies:
      bytewise: 1.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      sort-asc: 0.2.0
      sort-desc: 0.2.0
      union-value: 1.0.1
    dev: false

  /sortablejs@1.15.6:
    resolution: {integrity: sha512-aNfiuwMEpfBM/CN6LY0ibyhxPfPbyFeBTYJKCvzkJ2GkUpazIt3H+QIPAMHwqQ7tMKaHz1Qj+rJJCqljnf4p3A==}
    dev: false

  /source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  /source-map-support@0.3.3:
    resolution: {integrity: sha512-9O4+y9n64RewmFoKUZ/5Tx9IHIcXM6Q+RTSw6ehnqybUz4a7iwR3Eaw80uLtqqQ5D0C+5H03D4KKGo9PdP33Gg==}
    dependencies:
      source-map: 0.1.32
    dev: false

  /source-map@0.1.32:
    resolution: {integrity: sha512-htQyLrrRLkQ87Zfrir4/yN+vAUd6DNjVayEjTSHXu29AYQJw57I4/xEL/M6p6E/woPNJwvZt6rVlzc7gFEJccQ==}
    engines: {node: '>=0.8.0'}
    dependencies:
      amdefine: 1.0.1
    dev: false

  /source-map@0.5.6:
    resolution: {integrity: sha512-MjZkVp0NHr5+TPihLcadqnlVoGIoWo4IBHptutGh9wI3ttUYvCG26HkSuDi+K6lsZ25syXJXcctwgyVCt//xqA==}
    engines: {node: '>=0.10.0'}
    dev: false

  /source-map@0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==}
    engines: {node: '>=0.10.0'}
    dev: false

  /source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}
    dev: false

  /sourcemap-codec@1.4.8:
    resolution: {integrity: sha512-9NykojV5Uih4lgo5So5dtw+f0JgJX30KCNI8gwhz2J9A15wD0Ml6tjHKwf6fTSa6fAdVBdZeNOs9eJ71qCk8vA==}
    deprecated: Please use @jridgewell/sourcemap-codec instead
    dev: false

  /splaytree@3.1.2:
    resolution: {integrity: sha512-4OM2BJgC5UzrhVnnJA4BkHKGtjXNzzUfpQjCO8I05xYPsfS/VuQDwjCGGMi8rYQilHEV4j8NBqTFbls/PZEE7A==}
    dev: false

  /split-string@3.1.0:
    resolution: {integrity: sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==}
    engines: {node: '>=0.10.0'}
    dependencies:
      extend-shallow: 3.0.2
    dev: false

  /sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}
    dev: true

  /stack-generator@2.0.10:
    resolution: {integrity: sha512-mwnua/hkqM6pF4k8SnmZ2zfETsRUpWXREfA/goT8SLCV4iOFa4bzOX2nDipWAZFPTjLvQB82f5yaodMVhK0yJQ==}
    dependencies:
      stackframe: 1.3.4
    dev: false

  /stack-utils@2.0.6:
    resolution: {integrity: sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==}
    engines: {node: '>=10'}
    dependencies:
      escape-string-regexp: 2.0.0
    dev: true

  /stackframe@1.3.4:
    resolution: {integrity: sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==}

  /stacktrace-gps@3.1.2:
    resolution: {integrity: sha512-GcUgbO4Jsqqg6RxfyTHFiPxdPqF+3LFmQhm7MgCuYQOYuWyqxo5pwRPz5d/u6/WYJdEnWfK4r+jGbyD8TSggXQ==}
    dependencies:
      source-map: 0.5.6
      stackframe: 1.3.4
    dev: false

  /stacktrace-js@2.0.2:
    resolution: {integrity: sha512-Je5vBeY4S1r/RnLydLl0TBTi3F2qdfWmYsGvtfZgEI+SCprPppaIhQf5nGcal4gI4cGpCV/duLcAzT1np6sQqg==}
    dependencies:
      error-stack-parser: 2.1.4
      stack-generator: 2.0.10
      stacktrace-gps: 3.1.2
    dev: false

  /stream-browserify@3.0.0:
    resolution: {integrity: sha512-H73RAHsVBapbim0tU2JwwOiXUj+fikfiaoYAKHF3VJfA0pe2BCzkhAHBlLG6REzE+2WNZcxOXjK7lkso+9euLA==}
    dependencies:
      inherits: 2.0.4
      readable-stream: 3.6.2
    dev: true

  /stream-http@3.2.0:
    resolution: {integrity: sha512-Oq1bLqisTyK3TSCXpPbT4sdeYNdmyZJv1LxpEm2vu1ZhK89kSE5YXwZc3cWk0MagGaKriBh9mCFbVGtO+vY29A==}
    dependencies:
      builtin-status-codes: 3.0.0
      inherits: 2.0.4
      readable-stream: 3.6.2
      xtend: 4.0.2
    dev: true

  /stream-source@0.3.5:
    resolution: {integrity: sha512-ZuEDP9sgjiAwUVoDModftG0JtYiLUV8K4ljYD1VyUMRWtbVf92474o4kuuul43iZ8t/hRuiDAx1dIJSvirrK/g==}
    dev: false

  /string-argv@0.3.1:
    resolution: {integrity: sha512-a1uQGz7IyVy9YwhqjZIZu1c8JO8dNIe20xBmSS6qu9kv++k3JGzCVmprbNN5Kn+BgzD5E7YYwg1CcjuJMRNsvg==}
    engines: {node: '>=0.6.19'}
    dev: true

  /string-builder@0.1.8:
    resolution: {integrity: sha512-0pUtikmhChLaf+uLqzYTgzTCQc4jAjaWHolxPGq3D77SgSoTqkOlv0RVF3XwDxMR9x/y1WPPwkTNalZCA9DGnQ==}
    dev: true

  /string-convert@0.2.1:
    resolution: {integrity: sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A==}
    dev: false

  /string-width@3.1.0:
    resolution: {integrity: sha512-vafcv6KjVZKSgz06oM/H6GDBrAtz8vdhQakGjFIvNrHA6y3HCF1CInLy+QLq8dTJPQ1b+KDUqDFctkdRW44e1w==}
    engines: {node: '>=6'}
    dependencies:
      emoji-regex: 7.0.3
      is-fullwidth-code-point: 2.0.0
      strip-ansi: 5.2.0
    dev: true

  /string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1
    dev: true

  /string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0
    dev: true

  /string.prototype.trim@1.2.10:
    resolution: {integrity: sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2
    dev: true

  /string.prototype.trim@1.2.7:
    resolution: {integrity: sha512-p6TmeT1T3411M8Cgg9wBTMRtY2q9+PNy9EV1i2lIXUN/btt763oIfxwN3RR8VU6wHX8j/1CFy0L+YuThm6bgOg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.22.1

  /string.prototype.trimend@1.0.6:
    resolution: {integrity: sha512-JySq+4mrPf9EsDBEDYMOb/lM7XQLulwg5R/m1r0PXEFqrV0qHvl58sdTilSXtKOflCsK2E8jxf+GKC0T07RWwQ==}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.22.1

  /string.prototype.trimend@1.0.9:
    resolution: {integrity: sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-object-atoms: 1.0.0
    dev: true

  /string.prototype.trimstart@1.0.6:
    resolution: {integrity: sha512-omqjMDaY92pbn5HOX7f9IccLA+U1tA9GvtU4JrodiXFfYB7jPzzHpRzpglLAjtUV6bB557zwClJezTqnAiYnQA==}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.22.1

  /string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
    dev: true

  /string_decoder@0.10.31:
    resolution: {integrity: sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ==}
    dev: false

  /string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}
    dependencies:
      safe-buffer: 5.1.2
    dev: true

  /string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}
    dependencies:
      safe-buffer: 5.2.1

  /stringify-object@3.3.0:
    resolution: {integrity: sha512-rHqiFh1elqCQ9WPLIC8I0Q/g/wj5J1eMkyoiD6eoQApWHP0FtlK7rqnhmabL5VUY9JQCcqwwvlOaSuutekgyrw==}
    engines: {node: '>=4'}
    dependencies:
      get-own-enumerable-property-symbols: 3.0.2
      is-obj: 1.0.1
      is-regexp: 1.0.0
    dev: true

  /strip-ansi@3.0.1:
    resolution: {integrity: sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      ansi-regex: 2.1.1
    dev: false

  /strip-ansi@5.2.0:
    resolution: {integrity: sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==}
    engines: {node: '>=6'}
    dependencies:
      ansi-regex: 4.1.1
    dev: true

  /strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: 5.0.1
    dev: true

  /strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-regex: 6.1.0
    dev: true

  /strip-bom@3.0.0:
    resolution: {integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==}
    engines: {node: '>=4'}
    dev: true

  /strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}
    dev: true

  /strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}
    dev: true

  /stylis@4.3.0:
    resolution: {integrity: sha512-E87pIogpwUsUwXw7dNyU4QDjdgVMy52m+XEOPEKUn161cCzWjjhPSQhByfd1CcNvrOLnXQ6OnnZDwnJrz/Z4YQ==}
    dev: false

  /sucrase@3.35.0:
    resolution: {integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      commander: 4.1.1
      glob: 10.4.5
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.6
      ts-interface-checker: 0.1.13
    dev: true

  /supercluster@7.1.5:
    resolution: {integrity: sha512-EulshI3pGUM66o6ZdH3ReiFcvHpM3vAigyK+vcxdjpJyEbIIrtbmBdY23mGgnI24uXiGFvrGq9Gkum/8U7vJWg==}
    dependencies:
      kdbush: 3.0.0
    dev: false

  /supercluster@8.0.1:
    resolution: {integrity: sha512-IiOea5kJ9iqzD2t7QJq/cREyLHTtSmUT6gQsweojg9WH2sYJqZK9SswTu6jrscO6D1G5v5vYZ9ru/eq85lXeZQ==}
    dependencies:
      kdbush: 4.0.2
    dev: false

  /supports-color@2.0.0:
    resolution: {integrity: sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==}
    engines: {node: '>=0.8.0'}
    dev: false

  /supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}
    dependencies:
      has-flag: 3.0.0
    dev: true

  /supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}
    dependencies:
      has-flag: 4.0.0
    dev: true

  /supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  /sync-child-process@1.0.2:
    resolution: {integrity: sha512-8lD+t2KrrScJ/7KXCSyfhT3/hRq78rC0wBFqNJXv3mZyn6hW2ypM05JmlSvtqRbeq6jqA94oHbxAr2vYsJ8vDA==}
    engines: {node: '>=16.0.0'}
    dependencies:
      sync-message-port: 1.1.3
    dev: true

  /sync-message-port@1.1.3:
    resolution: {integrity: sha512-GTt8rSKje5FilG+wEdfCkOcLL7LWqpMlr2c3LRuKt/YXxcJ52aGSbGBAdI4L3aaqfrBt6y711El53ItyH1NWzg==}
    engines: {node: '>=16.0.0'}
    dev: true

  /table@5.4.6:
    resolution: {integrity: sha512-wmEc8m4fjnob4gt5riFRtTu/6+4rSe12TpAELNSqHMfF3IqnA+CH37USM6/YR3qRZv7e56kAEAtd6nKZaxe0Ug==}
    engines: {node: '>=6.0.0'}
    dependencies:
      ajv: 6.12.6
      lodash: 4.17.21
      slice-ansi: 2.1.0
      string-width: 3.1.0
    dev: true

  /tailwindcss@3.4.17:
    resolution: {integrity: sha512-w33E2aCvSDP0tW9RZuNXadXlkHXqFzSkQew/aIa2i/Sj8fThxwovwlXHSPXTbAHwEIhBFXAedUhP2tueAKP8Og==}
    engines: {node: '>=14.0.0'}
    hasBin: true
    dependencies:
      '@alloc/quick-lru': 5.2.0
      arg: 5.0.2
      chokidar: 3.6.0
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.3
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.6
      lilconfig: 3.1.3
      micromatch: 4.0.8
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.1.1
      postcss: 8.5.3
      postcss-import: 15.1.0(postcss@8.5.3)
      postcss-js: 4.0.1(postcss@8.5.3)
      postcss-load-config: 4.0.2(postcss@8.5.3)
      postcss-nested: 6.2.0(postcss@8.5.3)
      postcss-selector-parser: 6.1.2
      resolve: 1.22.10
      sucrase: 3.35.0
    transitivePeerDependencies:
      - ts-node
    dev: true

  /tape@4.16.2:
    resolution: {integrity: sha512-TUChV+q0GxBBCEbfCYkGLkv8hDJYjMdSWdE0/Lr331sB389dsvFUHNV9ph5iQqKzt8Ss9drzcda/YeexclBFqg==}
    hasBin: true
    dependencies:
      call-bind: 1.0.8
      deep-equal: 1.1.1
      defined: 1.0.1
      dotignore: 0.1.2
      for-each: 0.3.3
      glob: 7.2.3
      has: 1.0.3
      inherits: 2.0.4
      is-regex: 1.1.4
      minimist: 1.2.8
      object-inspect: 1.12.3
      resolve: 1.22.4
      resumer: 0.0.0
      string.prototype.trim: 1.2.7
      through: 2.3.8
    dev: false

  /text-encoding@0.6.4:
    resolution: {integrity: sha512-hJnc6Qg3dWoOMkqP53F0dzRIgtmsAge09kxUIqGrEUS4qr5rWLckGYaQAVr+opBrIMRErGgy6f5aPnyPpyGRfg==}
    deprecated: no longer maintained
    dev: false

  /text-segmentation@1.0.3:
    resolution: {integrity: sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw==}
    dependencies:
      utrie: 1.0.2
    dev: false

  /text-table@0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}
    dev: true

  /thenify-all@1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}
    dependencies:
      thenify: 3.3.1
    dev: true

  /thenify@3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}
    dependencies:
      any-promise: 1.3.0
    dev: true

  /throttle-debounce@3.0.1:
    resolution: {integrity: sha512-dTEWWNu6JmeVXY0ZYoPuH5cRIwc0MeGbJwah9KUNYSJwommQpCzTySTpEe8Gs1J23aeWEuAobe4Ag7EHVt/LOg==}
    engines: {node: '>=10'}
    dev: false

  /through@2.3.8:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==}

  /timers-browserify@2.0.12:
    resolution: {integrity: sha512-9phl76Cqm6FhSX9Xe1ZUAMLtm1BLkKj2Qd5ApyWkXzsMRaA7dgr81kf4wJmQf/hAvg8EEyJxDo3du/0KlhPiKQ==}
    engines: {node: '>=0.6.0'}
    dependencies:
      setimmediate: 1.0.5
    dev: true

  /tiny-invariant@1.3.1:
    resolution: {integrity: sha512-AD5ih2NlSssTCwsMznbvwMZpJ1cbhkGd2uueNxzv2jDlEeZdU04JQfRnggJQ8DrcVBGjAsCKwFBbDlVNtEMlzw==}
    dev: false

  /tiny-warning@1.0.3:
    resolution: {integrity: sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==}
    dev: false

  /tinycolor2@1.6.0:
    resolution: {integrity: sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==}
    dev: false

  /tinyqueue@2.0.3:
    resolution: {integrity: sha512-ppJZNDuKGgxzkHihX8v9v9G5f+18gzaTfrukGrq6ueg0lmH4nqVnA2IPG0AEH3jKEk2GRJCUhDoqpoiw3PHLBA==}
    dev: false

  /tmp@0.0.33:
    resolution: {integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==}
    engines: {node: '>=0.6.0'}
    dependencies:
      os-tmpdir: 1.0.2
    dev: true

  /to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}
    dev: true

  /to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}
    dependencies:
      is-number: 7.0.0

  /toggle-selection@1.0.6:
    resolution: {integrity: sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==}
    dev: false

  /topojson-client@3.1.0:
    resolution: {integrity: sha512-605uxS6bcYxGXw9qi62XyrV6Q3xwbndjachmNxu8HWTtVPxZfEJN9fd/SZS1Q54Sn2y0TMyMxFj/cJINqGHrKw==}
    hasBin: true
    dependencies:
      commander: 2.20.3
    dev: false

  /topojson-server@3.0.1:
    resolution: {integrity: sha512-/VS9j/ffKr2XAOjlZ9CgyyeLmgJ9dMwq6Y0YEON8O7p/tGGk+dCWnrE03zEdu7i4L7YsFZLEPZPzCvcB7lEEXw==}
    hasBin: true
    dependencies:
      commander: 2.20.3
    dev: false

  /toposort@2.0.2:
    resolution: {integrity: sha512-0a5EOkAUp8D4moMi2W8ZF8jcga7BgZd91O/yabJCFY8az+XSzeGyTKs0Aoo897iV1Nj6guFq8orWDS96z91oGg==}
    dev: false

  /ts-api-utils@1.0.2(typescript@5.0.4):
    resolution: {integrity: sha512-Cbu4nIqnEdd+THNEsBdkolnOXhg0I8XteoHaEKgvsxpsbWda4IsUut2c187HxywQCvveojow0Dgw/amxtSKVkQ==}
    engines: {node: '>=16.13.0'}
    peerDependencies:
      typescript: '>=4.2.0'
    dependencies:
      typescript: 5.0.4
    dev: true

  /ts-easing@0.2.0:
    resolution: {integrity: sha512-Z86EW+fFFh/IFB1fqQ3/+7Zpf9t2ebOAxNI/V6Wo7r5gqiqtxmgTlQ1qbqQcjLKYeSHPTsEmvlJUDg/EuL0uHQ==}
    dev: false

  /ts-interface-checker@0.1.13:
    resolution: {integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==}
    dev: true

  /tsconfig-paths@3.15.0:
    resolution: {integrity: sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==}
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0
    dev: true

  /tslib@1.14.1:
    resolution: {integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==}

  /tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  /tty-browserify@0.0.1:
    resolution: {integrity: sha512-C3TaO7K81YvjCgQH9Q1S3R3P3BtN3RIM8n+OvX4il1K1zgE8ZhI0op7kClgkxtutIE8hQrcrHBXvIheqKUUCxw==}
    dev: true

  /turf-jsts@1.2.3:
    resolution: {integrity: sha512-Ja03QIJlPuHt4IQ2FfGex4F4JAr8m3jpaHbFbQrgwr7s7L6U8ocrHiF3J1+wf9jzhGKxvDeaCAnGDot8OjGFyA==}
    dev: false

  /type-check@0.3.2:
    resolution: {integrity: sha512-ZCmOJdvOWDBYJlzAoFkC+Q0+bUyEOS1ltgp1MGU03fqHG+dbi9tBFU2Rd9QKiDZFAYrhPh2JUf7rZRIuHRKtOg==}
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: 1.1.2
    dev: true

  /type-fest@0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}
    dev: true

  /type-fest@4.31.0:
    resolution: {integrity: sha512-yCxltHW07Nkhv/1F6wWBr8kz+5BGMfP+RbRSYFnegVb0qV/UMT0G0ElBloPVerqn4M2ZV80Ir1FtCcYv1cT6vQ==}
    engines: {node: '>=16'}
    dev: true

  /typed-array-buffer@1.0.0:
    resolution: {integrity: sha512-Y8KTSIglk9OZEr8zywiIHG/kmQ7KWyjseXs1CbSo8vC42w7hg2HgYTxSWwP0+is7bWDc1H+Fo026CpHFwm8tkw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      get-intrinsic: 1.2.1
      is-typed-array: 1.1.12

  /typed-array-buffer@1.0.3:
    resolution: {integrity: sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-typed-array: 1.1.15
    dev: true

  /typed-array-byte-length@1.0.0:
    resolution: {integrity: sha512-Or/+kvLxNpeQ9DtSydonMxCx+9ZXOswtwJn17SNLvhptaXYDJvkFFP5zbfU/uLmvnBJlI4yrnXRxpdWH/M5tNA==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.3
      has-proto: 1.0.1
      is-typed-array: 1.1.12

  /typed-array-byte-length@1.0.3:
    resolution: {integrity: sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.3
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
    dev: true

  /typed-array-byte-offset@1.0.0:
    resolution: {integrity: sha512-RD97prjEt9EL8YgAgpOkf3O4IF9lhJFr9g0htQkm0rchFp/Vx7LW5Q8fSXXub7BXAODyUQohRMyOc3faCPd0hg==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.5
      call-bind: 1.0.8
      for-each: 0.3.3
      has-proto: 1.0.1
      is-typed-array: 1.1.12

  /typed-array-byte-offset@1.0.4:
    resolution: {integrity: sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.3
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10
    dev: true

  /typed-array-length@1.0.4:
    resolution: {integrity: sha512-KjZypGq+I/H7HI5HlOoGHkWUUGq+Q0TPhQurLbyrVrvnKTBgzLhIJ7j6J/XTQOi0d1RjyZ0wdas8bKs2p0x3Ng==}
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.3
      is-typed-array: 1.1.12

  /typed-array-length@1.0.7:
    resolution: {integrity: sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.3
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.1.0
      reflect.getprototypeof: 1.0.10
    dev: true

  /typedarray@0.0.6:
    resolution: {integrity: sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA==}
    dev: false

  /typedarray@0.0.7:
    resolution: {integrity: sha512-ueeb9YybpjhivjbHP2LdFDAjbS948fGEPj+ACAMs4xCMmh72OCOMQWBQKlaN4ZNQ04yfLSDLSx1tGRIoWimObQ==}
    dev: false

  /typescript@5.0.4:
    resolution: {integrity: sha512-cW9T5W9xY37cc+jfEnaUvX91foxtHkza3Nw3wkoF4sSlKn0MONdkdEndig/qPBWXNkmplh3NzayQzCiHM4/hqw==}
    engines: {node: '>=12.20'}
    hasBin: true
    dev: true

  /typewise-core@1.2.0:
    resolution: {integrity: sha512-2SCC/WLzj2SbUwzFOzqMCkz5amXLlxtJqDKTICqg30x+2DZxcfZN2MvQZmGfXWKNWaKK9pBPsvkcwv8bF/gxKg==}
    dev: false

  /typewise@1.0.3:
    resolution: {integrity: sha512-aXofE06xGhaQSPzt8hlTY+/YWQhm9P0jYUp1f2XtmW/3Bk0qzXcyFWAtPoo2uTGQj1ZwbDuSyuxicq+aDo8lCQ==}
    dependencies:
      typewise-core: 1.2.0
    dev: false

  /ua-parser-js@0.7.35:
    resolution: {integrity: sha512-veRf7dawaj9xaWEu9HoTVn5Pggtc/qj+kqTOFvNiN1l0YdxwC1kvel57UCjThjGa3BHBihE8/UJAHI+uQHmd/g==}
    dev: false

  /uglify-js@2.8.29:
    resolution: {integrity: sha512-qLq/4y2pjcU3vhlhseXGGJ7VbFO4pBANu0kwl8VCa9KEI0V8VfZIx2Fy3w01iSTA/pGwKZSmu/+I4etLNDdt5w==}
    engines: {node: '>=0.8.0'}
    hasBin: true
    dependencies:
      source-map: 0.5.7
      yargs: 3.10.0
    optionalDependencies:
      uglify-to-browserify: 1.0.2
    dev: false

  /uglify-to-browserify@1.0.2:
    resolution: {integrity: sha512-vb2s1lYx2xBtUgy+ta+b2J/GLVUR+wmpINwHePmPRhOsIVCG2wDzKJ0n14GslH1BifsqVzSOwQhRaCAsZ/nI4Q==}
    requiresBuild: true
    dev: false
    optional: true

  /umi-request@1.4.0:
    resolution: {integrity: sha512-OknwtQZddZHi0Ggi+Vr/olJ7HNMx4AzlywyK0W3NZBT7B0stjeZ9lcztA85dBgdAj3KVk8uPJPZSnGaDjELhrA==}
    dependencies:
      isomorphic-fetch: 2.2.1
      qs: 6.11.2
    dev: false

  /unbox-primitive@1.0.2:
    resolution: {integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==}
    dependencies:
      call-bind: 1.0.8
      has-bigints: 1.0.2
      has-symbols: 1.1.0
      which-boxed-primitive: 1.0.2

  /unbox-primitive@1.1.0:
    resolution: {integrity: sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      has-bigints: 1.0.2
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1
    dev: true

  /undici-types@6.19.8:
    resolution: {integrity: sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==}
    dev: true

  /union-value@1.0.1:
    resolution: {integrity: sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==}
    engines: {node: '>=0.10.0'}
    dependencies:
      arr-union: 3.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      set-value: 2.0.1
    dev: false

  /uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}
    dependencies:
      punycode: 2.3.0

  /url@0.11.4:
    resolution: {integrity: sha512-oCwdVC7mTuWiPyjLUz/COz5TLk6wgp0RCsN+wHZ2Ekneac9w8uuV0njcbbie2ME+Vs+d6duwmYuR3HgQXs1fOg==}
    engines: {node: '>= 0.4'}
    dependencies:
      punycode: 1.4.1
      qs: 6.13.1
    dev: true

  /use-sync-external-store@1.4.0(react@16.14.0):
    resolution: {integrity: sha512-9WXSPC5fMv61vaupRkCKCxsPxBocVnwakBEkMIHHpkTTg6icbJtg6jzgtLDm4bl3cSHAca52rYWih0k4K3PfHw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    dependencies:
      react: 16.14.0
    dev: false

  /util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  /util@0.12.5:
    resolution: {integrity: sha512-kZf/K6hEIrWHI6XqOFUiiMa+79wE/D8Q+NCNAWclkyg3b4d2k7s0QGepNjiABc+aR3N1PAyHL7p6UcLY6LmrnA==}
    dependencies:
      inherits: 2.0.4
      is-arguments: 1.1.1
      is-generator-function: 1.0.10
      is-typed-array: 1.1.12
      which-typed-array: 1.1.11
    dev: true

  /utility-types@3.10.0:
    resolution: {integrity: sha512-O11mqxmi7wMKCo6HKFt5AhO4BwY3VV68YU07tgxfz8zJTIxr4BpsezN49Ffwy9j3ZpwwJp4fkRwjRzq3uWE6Rg==}
    engines: {node: '>= 4'}
    dev: false

  /utrie@1.0.2:
    resolution: {integrity: sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw==}
    dependencies:
      base64-arraybuffer: 1.0.2
    dev: false

  /uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true
    dev: false

  /v8-compile-cache@2.4.0:
    resolution: {integrity: sha512-ocyWc3bAHBB/guyqJQVI5o4BZkPhznPYUG2ea80Gond/BgNWpap8TOmLSeeQG7bnh2KMISxskdADG59j7zruhw==}
    dev: true

  /value-equal@1.0.1:
    resolution: {integrity: sha512-NOJ6JZCAWr0zlxZt+xqCHNTEKOsrks2HQd4MqhP1qy4z1SkbEP467eNx6TgDKXMvUOb+OENfJCZwM+16n7fRfw==}
    dev: false

  /varint@6.0.0:
    resolution: {integrity: sha512-cXEIW6cfr15lFv563k4GuVuW/fiwjknytD37jIOLSdSWuOI6WnO/oKwmP2FQTU2l01LP8/M5TSAJpzUaGe3uWg==}
    dev: true

  /viewport-mercator-project@6.2.3:
    resolution: {integrity: sha512-QQb0/qCLlP4DdfbHHSWVYXpghB2wkLIiiZQnoelOB59mXKQSyZVxjreq1S+gaBJFpcGkWEcyVtre0+2y2DTl/Q==}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.
    dependencies:
      '@babel/runtime': 7.25.6
      gl-matrix: 3.4.3
    dev: false

  /vm-browserify@1.1.2:
    resolution: {integrity: sha512-2ham8XPWTONajOR0ohOKOHXkm3+gaBmGut3SRuu75xLd/RRaY6vqgh8NBYYk7+RW3u5AtzPQZG8F10LHkl0lAQ==}
    dev: true

  /void-elements@3.1.0:
    resolution: {integrity: sha512-Dhxzh5HZuiHQhbvTW9AMetFfBHDMYpo23Uo9btPXgdYP+3T5S+p+jgNy7spra+veYhBP2dCSgxR/i2Y02h5/6w==}
    engines: {node: '>=0.10.0'}
    dev: false

  /vt-pbf@3.1.3:
    resolution: {integrity: sha512-2LzDFzt0mZKZ9IpVF2r69G9bXaP2Q2sArJCmcCgvfTdCCZzSyz4aCLoQyUilu37Ll56tCblIZrXFIjNUpGIlmA==}
    dependencies:
      '@mapbox/point-geometry': 0.1.0
      '@mapbox/vector-tile': 1.3.1
      pbf: 3.2.1
    dev: false

  /warning@4.0.3:
    resolution: {integrity: sha512-rpJyN222KWIvHJ/F53XSZv0Zl/accqHR8et1kpaMTD/fLCRxtV8iX8czMzY7sVZupTI3zcUTg8eycS2kNF9l6w==}
    dependencies:
      loose-envify: 1.4.0
    dev: false

  /web-worker-helper@0.0.3:
    resolution: {integrity: sha512-/TllNPjGenDwjE67M16TD9ALwuY847/zIoH7r+e5rSeG4kEa3HiMTAsUDj80yzIzhtshkv215KfsnQ/RXR3nVA==}
    dev: false

  /whatwg-fetch@3.6.17:
    resolution: {integrity: sha512-c4ghIvG6th0eudYwKZY5keb81wtFz9/WeAHAoy8+r18kcWlitUIrmGFQ2rWEl4UCKUilD3zCLHOIPheHx5ypRQ==}
    dev: false

  /which-boxed-primitive@1.0.2:
    resolution: {integrity: sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==}
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4

  /which-boxed-primitive@1.1.1:
    resolution: {integrity: sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.2
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1
    dev: true

  /which-builtin-type@1.2.1:
    resolution: {integrity: sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==}
    engines: {node: '>= 0.4'}
    dependencies:
      call-bound: 1.0.3
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.0.10
      is-regex: 1.2.1
      is-weakref: 1.1.1
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.18
    dev: true

  /which-collection@1.0.2:
    resolution: {integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==}
    engines: {node: '>= 0.4'}
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4
    dev: true

  /which-pm-runs@1.1.0:
    resolution: {integrity: sha512-n1brCuqClxfFfq/Rb0ICg9giSZqCS+pLtccdag6C2HyufBrh3fBOiy9nb6ggRMvWOVH5GrdJskj5iGTZNxd7SA==}
    engines: {node: '>=4'}
    dev: true

  /which-typed-array@1.1.11:
    resolution: {integrity: sha512-qe9UWWpkeG5yzZ0tNYxDmd7vo58HDBc39mZ0xWWpolAGADdFOzkfamWLDxkOWcvHQKVmdTyQdLD4NOfjLWTKew==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.5
      call-bind: 1.0.8
      for-each: 0.3.3
      gopd: 1.2.0
      has-tostringtag: 1.0.2

  /which-typed-array@1.1.18:
    resolution: {integrity: sha512-qEcY+KJYlWyLH9vNbsr6/5j59AXk5ni5aakf8ldzBvGde6Iz4sxZGkJyWSAueTG7QhOvNRYb1lDdFmL5Td0QKA==}
    engines: {node: '>= 0.4'}
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.3
      for-each: 0.3.3
      gopd: 1.2.0
      has-tostringtag: 1.0.2
    dev: true

  /which@1.3.1:
    resolution: {integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==}
    hasBin: true
    dependencies:
      isexe: 2.0.0

  /which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: 2.0.0
    dev: true

  /window-size@0.1.0:
    resolution: {integrity: sha512-1pTPQDKTdd61ozlKGNCjhNRd+KPmgLSGa3mZTHoOliaGcESD8G1PXhh7c1fgiPjVbNVfgy2Faw4BI8/m0cC8Mg==}
    engines: {node: '>= 0.8.0'}
    dev: false

  /wolf-static-cpnt@1.5.2(classnames@2.5.1)(dayjs@1.11.13)(prop-types@15.8.1)(react-dom@16.14.0)(react@16.14.0):
    resolution: {integrity: sha512-8QcCvq8qWYLMbNpAf31f+AkBpR578+BIxyQ16HZhFsA+KaL1ls2kl21vXlTSJWch+EbX5f/Dn3wJ9h+soybFDg==}
    engines: {node: '>=10'}
    peerDependencies:
      dayjs: ^1.11.13
      react: '>=16.10.2'
      react-dom: '>=16.10.2'
    dependencies:
      '@ant-design/charts': 1.4.2(@ant-design/icons@4.8.1)(antd@4.24.9)(classnames@2.5.1)(lodash@4.17.21)(react-dom@16.14.0)(react@16.14.0)
      '@ant-design/compatible': 1.1.2(antd@4.24.9)(prop-types@15.8.1)(react-dom@16.14.0)(react@16.14.0)
      '@ant-design/icons': 4.8.1(react-dom@16.14.0)(react@16.14.0)
      animate.css: 3.7.2
      antd: 4.24.9(react-dom@16.14.0)(react@16.14.0)
      babel-plugin-transform-async-to-promises: 0.8.18
      dayjs: 1.11.13
      lodash: 4.17.21
      ml-matrix: 6.10.2
      react: 16.14.0
      react-dom: 16.14.0(react@16.14.0)
    transitivePeerDependencies:
      - classnames
      - prop-types
    dev: false

  /word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}
    dev: true

  /wordwrap@0.0.2:
    resolution: {integrity: sha512-xSBsCeh+g+dinoBv3GAOWM4LcVVO68wLXRanibtBSdUvkGWQRGeE9P7IwU9EmDDi4jA6L44lz15CGMwdw9N5+Q==}
    engines: {node: '>=0.4.0'}
    dev: false

  /wrap-ansi@6.2.0:
    resolution: {integrity: sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==}
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1
    dev: true

  /wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0
    dev: true

  /wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  /write@1.0.3:
    resolution: {integrity: sha512-/lg70HAjtkUgWPVZhZcm+T4hkL8Zbtp1nFNOn3lRrxnlv50SRBv7cR7RqR+GMsd3hUXy9hWBo4CHTbFTcOYwig==}
    engines: {node: '>=4'}
    dependencies:
      mkdirp: 0.5.6
    dev: true

  /xregexp-quotemeta@1.0.7:
    resolution: {integrity: sha512-XWSdHVeKUG7gO1Lkg0aHKbUk85bxIP3CkT3BHZcthyrUc+IEZsHcd2MwNV79/s1F0HdqcsJOyRNzTNh6a42N0w==}
    dev: true

  /xregexp@4.4.1:
    resolution: {integrity: sha512-2u9HwfadaJaY9zHtRRnH6BY6CQVNQKkYm3oLtC9gJXXzfsbACg5X5e4EZZGVAH+YIfa+QA9lsFQTTe3HURF3ag==}
    dependencies:
      '@babel/runtime-corejs3': 7.22.11
    dev: true

  /xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}
    dev: true

  /yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}
    dev: true

  /yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==}
    engines: {node: '>= 6'}
    dev: true

  /yaml@2.7.0:
    resolution: {integrity: sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==}
    engines: {node: '>= 14'}
    hasBin: true
    dev: true

  /yargs@3.10.0:
    resolution: {integrity: sha512-QFzUah88GAGy9lyDKGBqZdkYApt63rCXYBGYnEP4xDJPXNqXXnBDACnbrXnViV6jRSqAePwrATi2i8mfYm4L1A==}
    dependencies:
      camelcase: 1.2.1
      cliui: 2.1.0
      decamelize: 1.2.0
      window-size: 0.1.0
    dev: false

  /yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}
    dev: true

  /zustand@4.5.6(@types/react@16.14.62)(react@16.14.0):
    resolution: {integrity: sha512-ibr/n1hBzLLj5Y+yUcU7dYw8p6WnIVzdJbnX+1YpaScvZVF2ziugqHs+LAmHw4lWO9c/zRj+K1ncgWDQuthEdQ==}
    engines: {node: '>=12.7.0'}
    peerDependencies:
      '@types/react': '>=16.8'
      immer: '>=9.0.6'
      react: '>=16.8'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      immer:
        optional: true
      react:
        optional: true
    dependencies:
      '@types/react': 16.14.62
      react: 16.14.0
      use-sync-external-store: 1.4.0(react@16.14.0)
    dev: false
